services:
  main-api:
    container_name: gramio-bot-dev
    restart: unless-stopped
    ports:
      - "3004:3004"
    build:
      context: ../../
      dockerfile: ./packages/docker-config/Dockerfile.main-api-dev
      target: development-image
    environment:
      - NODE_ENV=development
      - DATABASE_URL=${DATABASE_URL} 
      - REDIS_HOST=redis 
      # Add other necessary dev environment variables
    volumes:
      # Mount your source code for live reloading
      - apps/main-api:/app/apps/main-api 
      - packages:/app/packages # Mount shared packages
      # You might need to mount the root package.json and bun.lockb if changes there should trigger rebuilds or affect install
      - package.json:/app/package.json
      - bun.lock:/app/bun.lockb
      # Careful with node_modules mounting; it can cause issues between host and container.
      # It's often better to let node_modules live inside the container after `bun install`.
      # If you mount node_modules, ensure your host and container OS are compatible.
      # - /app/apps/main-api/node_modules # Named volume to persist node_modules if not mounting host's
    depends_on:
      - postgres
      - redis
      - rabbitmq

  dashboard:
    container_name: dashboard-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    build:
      context: ../../
      dockerfile: ./packages/docker-config/Dockerfile.dashboard
      target: ./packages/docker-config/development-image 
    environment:
      - NODE_ENV=development
      # NEXT_PUBLIC_API_URL=http://localhost:3004 # If accessing API from browser on host
      # Or if using service discovery from within another container (less common for frontend)
    volumes:
      - apps/dashboard:/app/apps/dashboard
      - packages:/app/packages
      - package.json:/app/package.json
      - bun.lock:/app/bun.lock
    depends_on:
      - main-api 

  mini-app:
    container_name: mini-app-dev
    restart: unless-stopped
    ports:
      - "3001:3001"
    build:
      context: ../../
      dockerfile: ./packages/docker-config/Dockerfile.mini-api
      target: development-image 
    environment:
      - NODE_ENV=development
    volumes:
      - apps/mini-app:/app/apps/mini-app
      - packages:/app/packages
      - package.json:/app/package.json
      - bun.lockb:/app/bun.lockb
    depends_on:
      - main-api

  # postgres, redis, rabbitmq services remain the same as your production-like compose file
  postgres:
    container_name: gramio-postgres-dev
    image: postgres:latest
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${POSTGRES_USER_DEV} # Use separate dev credentials if desired
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD_DEV}
      - POSTGRES_DB=${POSTGRES_DB_DEV}
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    ports:
      - "5433:5432" # Use a different host port for dev DB to avoid conflict if you run prod DB locally

  redis:
    container_name: gramio-redis-dev
    image: redis:latest
    command: ["redis-server", "--maxmemory-policy", "noeviction"]
    restart: unless-stopped
    volumes:
      - redis_data_dev:/data
    ports:
      - "6380:6379" # Use a different host port for dev Redis

  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq-dev
    ports:
      - "${RABBITMQ_PORT_DEV}:5672"
      - "15673:15672" # Different management port
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
    volumes:
      - rabbitmq_data_dev:/var/lib/rabbitmq

volumes:
  postgres_data_dev:
  redis_data_dev:
  rabbitmq_data_dev: