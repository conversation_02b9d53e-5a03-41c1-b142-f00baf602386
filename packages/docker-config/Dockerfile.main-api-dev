# Stage 1: Base image (use one that has Node/npm if scripts need it)
# Using :latest which includes Node/npm
FROM oven/bun:1 AS base 
WORKDIR /app

# Stage 2: Copy all package manifests and the root lockfile
FROM base AS manifests
WORKDIR /app
COPY package.json bun.lock ./
COPY apps/main-api/package.json ./apps/main-api/
COPY apps/dashboard/package.json ./apps/dashboard/
COPY apps/mini-app/package.json ./apps/mini-app/
COPY packages/db/package.json ./packages/db/
COPY packages/eslint-config/package.json ./packages/eslint-config/ 
COPY packages/typescript-config/package.json ./packages/typescript-config/ 
COPY packages/typescript-config ./packages/typescript-config

# Stage 3: Install ALL monorepo dependencies (including devDependencies)
FROM manifests AS full-deps-installer
WORKDIR /app
# This installs everything, including devDependencies, respecting the lockfile
RUN bun install --frozen-lockfile 
# If the "npm: not found" error persists, it means a script needs npm.
# Since oven/bun:latest includes npm, this should ideally work.
# If not, you might need to explicitly ensure npm is in PATH or troubleshoot the specific script.

# Stage 4: Copy all source code
FROM base AS source-copier
WORKDIR /app
COPY . . 

# Stage 5: Build the main-api application (if needed for dev, often not for NestJS watch mode)
# For a dev setup, you might skip the explicit build if `bun run dev` handles it.
# If you do build, it uses devDependencies.
FROM base AS builder 
WORKDIR /app
COPY --from=full-deps-installer /app/node_modules ./node_modules
COPY --from=full-deps-installer /app/packages/typescript-config ./packages/typescript-config
COPY --from=source-copier /app .

WORKDIR /app/apps/main-api
# For dev, you might not run `nest build` here if `start:watch` does it.
# If you do:
# ENV NODE_ENV=development # Or remove if not needed for build
# RUN bun run build # This would be `nest build`

# Stage 6: Final Development Image (different from production)
# Use the base that has Node/npm and all tools
FROM base AS development-image 
WORKDIR /app

# Copy ALL installed node_modules (including devDependencies)
COPY --from=full-deps-installer /app/node_modules ./node_modules
# Copy all source code
COPY --from=source-copier /app .
# Copy shared configs
COPY --from=full-deps-installer /app/packages/typescript-config ./packages/typescript-config


USER bun
EXPOSE 3004 

# Command to run the development server for main-api
# Assumes apps/main-api/package.json has a "dev" script like "nest start --watch"
WORKDIR /app/apps/main-api
CMD ["bun", "run", "dev"] 