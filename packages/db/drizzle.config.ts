import type { Config } from "drizzle-kit";
import env from "env-var";

// Use the correct schema path! (relative to this config file)
const DATABASE_URL = env.get("DATABASE_URL").required().asString();

const drizzleConfig: Config = {
  schema: ["./src/schema/index.ts", "./src/enums/index.ts"],
  out: "./drizzle",
  dialect: "postgresql",
  casing: "snake_case",
  dbCredentials: {
    url: DATABASE_URL,
  },
} satisfies Config;

export default drizzleConfig;