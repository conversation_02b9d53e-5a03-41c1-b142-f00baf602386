{"name": "@monorepo/db", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "bunx tsc -p tsconfig.json", "typecheck": "bunx tsc --noEmit -p tsconfig.json", "lint": "bunx eslint . --ext .ts", "clean": "rm -rf dist .turbo tsconfig.tsbuildinfo", "generate": "bun x drizzle-kit generate:pg --schema=./src/schema/index.ts --out=./drizzle", "push": "bun x drizzle-kit push:pg", "migrate": "bun run ./scripts/migrate.ts", "generate-migrations": "bun run ./scripts/generate-migrations.ts", "studio": "bun x drizzle-kit studio"}, "dependencies": {"@repo/typescript-config": "workspace:^", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.0", "env-var": "^7.5.0", "postgres": "^3.4.7"}, "devDependencies": {"@repo/eslint-config": "workspace:^", "drizzle-kit": "^0.31.1", "typescript": "^5.8.3"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./schema": {"import": "./dist/schema/index.js", "require": "./dist/schema/index.js", "types": "./dist/schema/index.d.ts"}, "./package.json": "./package.json"}, "imports": {"#*": "./src/*"}}