import { config } from "dotenv"; // .js extension not typically needed for node_modules
import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";

// Load environment variables
config();

// Database connection string
const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
	console.error("DATABASE_URL environment variable is not set");
	process.exit(1);
}

// Create a postgres client
const sql = postgres(connectionString, { max: 1 });

// Create a drizzle instance
const db = drizzle(sql);

// Run migrations
async function main() {
	try {
		console.log("Running migrations...");
		await migrate(db, { migrationsFolder: "./drizzle/migrations" });
		console.log("Migrations completed successfully");
		process.exit(0);
	} catch (error) {
		console.error("Error running migrations:", error);
		process.exit(1);
	}
}

main();
