import { exec } from "node:child_process";
import { promisify } from "node:util";
import { config } from "dotenv"; // .js extension not typically needed for node_modules
import { drizzle } from "drizzle-orm/postgres-js";
import { migrate } from "drizzle-orm/postgres-js/migrator";
import postgres from "postgres";

// Load environment variables
config();

const execAsync = promisify(exec);

// Database connection string
const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
	console.error("DATABASE_URL environment variable is not set");
	process.exit(1);
}

// TypeScript type assertion since we've checked connectionString is not null/undefined
const dbConnectionString: string = connectionString;

async function main() {
	try {
		console.log("Generating migrations...");

		// Run drizzle-kit generate command
		const { stdout, stderr } = await execAsync(
			"npx drizzle-kit generate:pg --schema=./src/schema --out=./drizzle/migrations",
		);

		if (stderr) {
			console.error("Error generating migrations:", stderr);
			process.exit(1);
		}

		console.log(stdout);
		console.log("Migrations generated successfully");

		// Ask if the user wants to apply the migrations
		const readline = require("node:readline").createInterface({
			input: process.stdin,
			output: process.stdout,
		});

		readline.question(
			"Do you want to apply the migrations? (y/n) ",
			async (answer: string) => {
				readline.close();

				if (answer.toLowerCase() === "y") {
					// Using our type-safe connection string
					const sql = postgres(dbConnectionString, { max: 1 });

					// Create a drizzle instance
					const db = drizzle(sql);

					// Run migrations
					console.log("Running migrations...");
					await migrate(db, { migrationsFolder: "./drizzle/migrations" });
					console.log("Migrations applied successfully");
				}

				process.exit(0);
			},
		);
	} catch (error) {
		console.error("Error:", error);
		process.exit(1);
	}
}

main();
