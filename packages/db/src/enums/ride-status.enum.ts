import { pgEnum } from "drizzle-orm/pg-core";

const VALUES = [
	"SEARCHING",
	"ASSIGNED",
	"PICKED_UP",
	"COMPLETED",
	"CANCELLED",
] as const;

export const rideStatusEnum = pgEnum("ride_status_enum", VALUES);
export type RideStatusType = (typeof VALUES)[number];
export enum RIDE_STATUS {
	SEARCHING = "SEARCHING",
	ASSIGNED = "ASSIGNED",
	PICKED_UP = "PICKED_UP",
	COMPLETED = "COMPLETED",
	CANCELLED = "CANCELLED",
}
