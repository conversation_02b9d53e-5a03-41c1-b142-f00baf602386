import { pgEnum } from "drizzle-orm/pg-core";

const VALUES = [
	"TELEGRAM",
	"VIBER",
	"FACEBOOK",
	"GOOGLE",
	"APPLE",
	"PHONE",
] as const;

export const providerEnum = pgEnum("provider_type_enum", VALUES);
export type ProviderType = (typeof VALUES)[number];
export enum PROVIDER {
	TELEGRAM = "TELEGRAM",
	VIBER = "VIBER",
	FACEBOOK = "FACEBOOK",
	GOOGLE = "GOOGLE",
	APPLE = "APPLE",
	PHONE = "PHONE",
}
