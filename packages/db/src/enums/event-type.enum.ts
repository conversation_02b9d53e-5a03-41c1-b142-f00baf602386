import { pgEnum } from "drizzle-orm/pg-core";

const VALUES = [
	"MESSAGE_RECEIVED",
	"MESSAGE_SENT",
	"USER_LINKED",
	"USER_UNLINKED",
	"CONSENT_GRANTED",
	"CONSENT_REVOKED",
	"VERIFICATION_REQUESTED",
	"VERIFICATION_SUCCEEDED",
	"VERIFICATION_FAILED",
	"SESSION_STARTED",
	"SESSION_ENDED",
	"ERROR",
	"WEBHOOK_RECEIVED",
] as const;

export const eventTypeDbEnum = pgEnum("events_event_type_enum", VALUES);
export type EventTypeDb = (typeof eventTypeDbEnum.enumValues)[number];

export enum EventType {
	MessageReceived = "MESSAGE_RECEIVED",
	MessageSent = "MESSAGE_SENT",
	UserLinked = "USER_LINKED",
	UserUnlinked = "USER_UNLINKED",
	ConsentGranted = "CONSENT_GRANTED",
	ConsentRevoked = "CONSENT_REVOKED",
	VerificationRequested = "VERIFICATION_REQUESTED",
	VerificationSucceeded = "VERIFICATION_SUCCEEDED",
	VerificationFailed = "VERIFICATION_FAILED",
	SessionStarted = "SESSION_STARTED",
	SessionEnded = "SESSION_ENDED",
	Error = "ERROR",
	WebhookReceived = "WEBHOOK_RECEIVED",
}
