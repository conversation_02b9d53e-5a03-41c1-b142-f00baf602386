import { pgEnum } from "drizzle-orm/pg-core";

/**
 * Array of valid billing method values.
 */
const BILLING_METHOD_VALUES = [
    "CREDIT_CARD",
    "PAYPAL",
    "BANK_TRANSFER",
    "CRYPTO",
] as const;

/**
 * Function that returns an enum for billing methods.
 */
export const billingMethodEnum = pgEnum("billing_method", BILLING_METHOD_VALUES);

/**
 * TypeScript union type for billing methods.
 */
export type BillingMethodType = (typeof BILLING_METHOD_VALUES)[number];

/**
 * Optional string enum mirroring BILLING_METHOD_VALUES for convenience.
 */
export enum BillingMethodString {
    CREDIT_CARD = "CREDIT_CARD",
    PAYPAL = "PAYPAL",
    BANK_TRANSFER = "BANK_TRANSFER",
    CRYPTO = "CRYPTO",
}