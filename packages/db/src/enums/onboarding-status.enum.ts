import { pgEnum } from "drizzle-orm/pg-core";

const VALUES = ["INCOMPLETE", "IN_PROGRESS", "COMPLETED"] as const;

export const onboardingStatusDbEnum = pgEnum(
	"user_onboarding_status_enum",
	VALUES,
);
export type OnboardingStatusDb =
	(typeof onboardingStatusDbEnum.enumValues)[number];

export enum OnboardingStatus {
	Incomplete = "INCOMPLETE",
	InProgress = "IN_PROGRESS",
	Completed = "COMPLETED",
}
