import { pgEnum } from "drizzle-orm/pg-core";

const VALUES = [
	"TEXT",
	"IMAGE",
	"VOICE",
	"LOCATION",
	"VERIFICATION",
	"DOCUMENT",
	"STICKER",
	"BUT<PERSON><PERSON>",
	"TEMPLAT<PERSON>",
	"INTERACTIVE",
	"CARO<PERSON><PERSON>",
	"GROUP_CHAT",
	"UNKNOWN",
] as const;

export const messageTypeDbEnum = pgEnum("message_type_enum", VALUES);
export type MessageTypeDb = (typeof messageTypeDbEnum.enumValues)[number];

export enum MessageType {
	TEXT = "TEXT",
	IMAGE = "IMAGE",
	VOICE = "VOICE",
	LOCATION = "LOCATION",
	VERIFICATION = "VERIFICATION",
	DOCUMENT = "DOCUMENT",
	STICKER = "STICKER",
	BUTTON = "BUTTON",
	TEMPLATE = "TEMPLATE",
	INTERACTIVE = "INTERACTIVE",
	CAROUSEL = "CAROUSEL",
	GROUP_CHAT = "GROUP_CHAT",
	UNKNOWN = "UNKNOWN",
}
