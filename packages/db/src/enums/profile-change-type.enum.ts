import { pgEnum } from "drizzle-orm/pg-core";

const VALUES = ["ONBOARDING", "PHONE_CHANGE", "ADMIN_UPDATE"] as const;

export const profileChangeTypeDbEnum = pgEnum(
	"user_profile_change_type_enum",
	VALUES,
);
export type ProfileChangeTypeDb =
	(typeof profileChangeTypeDbEnum.enumValues)[number];

export enum ProfileChangeType {
	Onboarding = "ONBOARDING",
	PhoneChange = "PHONE_CHANGE",
	AdminUpdate = "ADMIN_UPDATE",
}
