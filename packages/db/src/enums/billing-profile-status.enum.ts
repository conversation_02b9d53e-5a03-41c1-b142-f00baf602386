import { pgEnum } from "drizzle-orm/pg-core";

const VALUES = ["ACTIVE", "INACTIVE", "DEPRECATED"] as const;

export const billingProfileStatusDbEnum = pgEnum(
	"billing_profiles_status_enum",
	VALUES,
);
export type BillingProfileStatusDb =
	(typeof billingProfileStatusDbEnum.enumValues)[number];

export enum BillingProfileStatus {
	Active = "ACTIVE",
	Inactive = "INACTIVE",
	Deprecated = "DEPRECATED",
}
