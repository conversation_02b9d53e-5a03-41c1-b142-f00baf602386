### **1. Creating an Enum File** (`*.enum.js.ts`)
**Purpose**: Define database enums with TypeScript types for type safety and database compatibility.

**Steps**:
1. **Create a new `.enum.js.ts` file** (e.g., `user-role.enum.js.ts`)
2. **Import `pgEnum`** from Drizzle ORM
3. **Define enum values** as a `const` array for consistency
4. **Create the Drizzle enum** using `pgEnum("column_name", values)`
5. **Add a TypeScript union type** for type safety
6. **Add a string enum** for compatibility with application logic

**Example Structure**:
```ts
// user-role.enum.js.ts
import { pgEnum } from "drizzle-orm/pg-core";

const VALUES = ["ROLE1", "ROLE2"] as const;

export const enumName = pgEnum("column_name", VALUES);
export type EnumType = (typeof VALUES)[number];
export enum EnumString {
  ROLE1 = "ROLE1",
  ROLE2 = "ROLE2",
}
```

---

### **2. Creating a Schema File** (`*.schema.js.ts`)
**Purpose**: Define database tables with columns, relationships, and indexes.

**Steps**:
1. **Create a new `.schema.js.ts` file** (e.g., `tenant-bots.schema.js.ts`)
2. **Import dependencies**:
   - `pgTable`, `uuid`, `varchar`, etc., from Drizzle
   - Related schemas/enums (e.g., `tenants`, `botStatusEnum`)
3. **Define the table** using `pgTable("table_name", { columns })`
4. **Add columns** with appropriate types and constraints:
   - Primary keys (e.g., `uuid().primaryKey().defaultRandom()`)
   - Foreign keys (e.g., `.references(() => otherTable.id)`)
   - Enums (e.g., `botStatusEnum("column_name")`)
   - Timestamps, indexes, etc.
5. **Add an index builder** for secondary indexes
6. **Define relationships** using `relations()`
7. **Export the schema and relations**

**Example Structure**:
```ts
// tenant-bots.schema.js.ts
import { pgTable, uuid, varchar, timestamp, pgEnum, uniqueIndex, boolean } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { tenants } from "./tenants.schema.js";
import { botStatusEnum } from "./bot-status.enum.js";

export const tenantBots = pgTable("tenant_bots", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: uuid("tenant_id").references(() => tenants.id).notNull(),
  botName: varchar("bot_name", { length: 100 }),
  telegramBotToken: varchar("telegram_bot_token", { length: 255 }).notNull(),
  status: botStatusEnum("status").default("STOPPED").notNull(),
  // ... other columns
}, (table) => ({
  indexes: [uniqueIndex("telegram_bot_token_idx").on(table.telegramBotToken)],
}));

export const tenantBotsRelations = relations(tenantBots, ({ one }) => ({
  tenant: one(tenants, {
    fields: [tenantBots.tenantId],
    references: [tenants.id],
  }),
}));
```

---

### **Key Patterns**
- **Naming**: Use PascalCase for enum/table names, snake_case for column names
- **Enums**: Always pair `pgEnum` with a TypeScript union type and string enum
- **Relations**: Use `relations()` to define foreign key relationships
- **Indexes**: Add unique indexes for critical lookup fields
- **Timestamps**: Use `defaultNow()` for `created_at`/`updated_at`

This structure ensures type safety, database compatibility, and maintainability.