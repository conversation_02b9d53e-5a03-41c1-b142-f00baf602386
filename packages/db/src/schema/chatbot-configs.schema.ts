import { relations } from "drizzle-orm";
import { jsonb, pgTable, timestamp, uuid } from "drizzle-orm/pg-core";
import { chatbotProviders } from "./chatbot-providers.schema.js";
import { tenants } from "./tenants.schema.js"; // Ensure this path is correct

export const chatbotConfigs = pgTable("chatbot_config", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(),
	providerId: uuid("provider_id")
		.references(() => chatbotProviders.id)
		.notNull(), // Consider onDelete: "cascade" or "set null"
	settings: jsonb("settings").notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const chatbotConfigsRelations = relations(chatbotConfigs, ({ one }) => ({
	tenant: one(tenants, {
		fields: [chatbotConfigs.tenantId],
		references: [tenants.id],
	}),
	provider: one(chatbotProviders, {
		fields: [chatbotConfigs.providerId],
		references: [chatbotProviders.id],
	}),
}));
