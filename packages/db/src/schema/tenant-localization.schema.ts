import { relations } from "drizzle-orm";
import {
	jsonb,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";

export const tenantLocalization = pgTable(
	"tenant_localization",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.notNull()
			.references(() => tenants.id),
		defaultLocale: varchar("default_locale", { length: 10 }).notNull(),
		supportedLocales: jsonb("supported_locales").notNull(),
		timezone: varchar("timezone", { length: 64 }).notNull(),
		currencySymbol: varchar("currency_symbol", { length: 10 }).notNull(),
		labels: jsonb("labels").notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
		metadata: jsonb("metadata"),
	},
	(table) => ({
		tenantIdIndex: uniqueIndex().on(table.tenantId),
	}),
);

export const tenantLocalizationRelations = relations(
	tenantLocalization,
	({ one }) => ({
		tenant: one(tenants, {
			fields: [tenantLocalization.tenantId],
			references: [tenants.id],
		}),
	}),
);
