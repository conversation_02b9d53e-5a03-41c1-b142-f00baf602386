// packages/db/src/schema/support-ticket.schema.ts
import { relations } from "drizzle-orm"; // 1. Add import for relations
import { pgTable, text, timestamp, uuid, varchar } from "drizzle-orm/pg-core";
import { supportTicketStatusEnum } from "../enums/support-ticket-status.enum.js"; // Import from kebab-case enum
import { tenants } from "./tenants.schema.js"; // 2. Add imports for related tables
import { users } from "./users.schema.js";
import { operators } from "./operators.schema.js"; // Assuming operators can be assigned

// 3. Drizzle object name is already camelCase: supportTickets - GOOD
// 4. Change DB table name to snake_case: "support_ticket"
export const supportTickets = pgTable("support_ticket", {
	id: uuid("id").primaryKey().defaultRandom(),
    // 5. Column names are already camelCase - GOOD
	tenantId: uuid("tenant_id").notNull(),
	userId: uuid("user_id").notNull(),
	subject: varchar("subject", { length: 256 }).notNull(), // Length was 255 in snake_case, 256 is fine.
	details: text("details").notNull(),
	status: supportTicketStatusEnum("status").notNull(),
	assignedToId: uuid("assigned_to_id"), // This was assigned_to_id in snake_case
    // 6. Ensure createdAt is notNull
	createdAt: timestamp("created_at").defaultNow().notNull(),
	closedAt: timestamp("closed_at"),
});

// 7. Add and adapt relations from the snake_case file
export const supportTicketsRelations = relations(supportTickets, ({ one }) => ({
	tenant: one(tenants, {
		fields: [supportTickets.tenantId],
		references: [tenants.id],
	}),
	user: one(users, {
		fields: [supportTickets.userId],
		references: [users.id],
	}),
	assignedTo: one(operators, { // Assuming 'operators' is the correct table for assigned_to_id
		fields: [supportTickets.assignedToId],
		references: [operators.id],
	}),
}));

// 8. Add types
export type SupportTicket = typeof supportTickets.$inferSelect;
export type NewSupportTicket = typeof supportTickets.$inferInsert;