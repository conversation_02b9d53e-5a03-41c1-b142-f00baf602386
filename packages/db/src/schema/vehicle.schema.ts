import { relations } from "drizzle-orm";
import {
	integer,
	jsonb,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js"; // Ensure this file exists
import { vehicleStatusEnum } from "../enums/vehicle-status.enum.js";

export const vehicles = pgTable(
	"vehicle",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id)
			.notNull(),
		licensePlate: varchar("license_plate", { length: 20 }).notNull(),
		make: varchar("make", { length: 100 }).notNull(),
		model: varchar("model", { length: 100 }).notNull(),
		color: varchar("color", { length: 50 }),
		year: integer("year"),
		registrationId: varchar("registration_id", { length: 100 }),
		status: vehicleStatusEnum("status").default("ACTIVE").notNull(),
		metadata: jsonb("metadata"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(table) => ({
		indexes: [
			uniqueIndex("license_plate_tenant_id_idx").on(
				table.licensePlate,
				table.tenantId,
			),
		],
	}),
);

export const vehicleRelations = relations(vehicles, ({ one }) => ({
	tenant: one(tenants, {
		fields: [vehicles.tenantId],
		references: [tenants.id],
	}),
}));
