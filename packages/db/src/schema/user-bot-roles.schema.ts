import { relations } from "drizzle-orm";
import { bigint, pgTable, primaryKey, uuid } from "drizzle-orm/pg-core";
import { bots } from "./bots.schema.js";
import { roles } from "./role.schema.js";
import { telegramUsers } from "./telegram_users.schema.js";

export const userBotRoles = pgTable(
	"user_bot_roles",
	{
		telegramUserId: bigint("telegram_user_id", { mode: "number" }).notNull(), // Telegram user ID
		botId: uuid("bot_id")
			.references(() => bots.id, { onDelete: "cascade" })
			.notNull(),
		roleId: uuid("role_id")
			.references(() => roles.id, { onDelete: "cascade" })
			.notNull(),
	},
	(table) => ({
		// Primary key to ensure a user has a role only once per bot
		pk_user_bot_role: primaryKey({
			columns: [table.telegramUserId, table.botId, table.roleId],
		}),
	}),
);

export const userBotRolesRelations = relations(userBotRoles, ({ one }) => ({
	telegramUser: one(telegramUsers, {
		fields: [userBotRoles.telegramUserId],
		references: [telegramUsers.id],
	}),
	bot: one(bots, {
		fields: [userBotRoles.botId],
		references: [bots.id],
	}),
	role: one(roles, {
		fields: [userBotRoles.roleId],
		references: [roles.id],
	}),
}));

export type UserBotRole = typeof userBotRoles.$inferSelect;
export type NewUserBotRole = typeof userBotRoles.$inferInsert;
