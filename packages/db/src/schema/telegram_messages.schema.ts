import {
	bigint,
	integer,
	json,
	pgTable,
	timestamp,
	varchar,
} from "drizzle-orm/pg-core";

export const telegramMessages = pgTable("telegram_messages", {
	id: bigint("id", { mode: "number" }).primaryKey(),
	chatId: bigint("chat_id", { mode: "number" }).notNull(),
	userId: integer("user_id"),
	messageType: varchar("message_type", { length: 32 }), // e.g. "text", "photo", etc.
	text: varchar("text", { length: 4096 }),
	fileId: varchar("file_id", { length: 256 }), // for media messages
	caption: varchar("caption", { length: 1024 }),
	replyToMessageId: bigint("reply_to_message_id", { mode: "number" }),
	forwardFromUserId: integer("forward_from_user_id"),
	forwardFromChatId: bigint("forward_from_chat_id", { mode: "number" }),
	editDate: timestamp("edit_date", { withTimezone: true }),
	entities: json("entities"), // store array of entity objects (optional)
	command: varchar("command", { length: 64 }),
	payload: json("payload"), // callback or extra data (optional)
	serviceType: varchar("service_type", { length: 32 }),
	date: timestamp("date", { withTimezone: true }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	rawData: json("raw_data"), // (optional) the complete raw Telegram message
});
