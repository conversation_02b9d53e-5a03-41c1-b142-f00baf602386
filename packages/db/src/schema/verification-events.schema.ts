import { relations } from "drizzle-orm";
import {
	jsonb,
	pgEnum,
	pgTable,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { chatbotUsers } from "./chatbot-users.schema.js";

// Enum for verification methods
export const verificationMethodEnum = pgEnum("verification_method", [
	"sms",
	"email",
	"phone_call",
	"other",
]);

// Enum for verification statuses
export const verificationStatusEnum = pgEnum("verification_status", [
	"initiated",
	"code_sent",
	"verified",
	"failed",
	"expired",
]);

export const verificationEvents = pgTable("verification_event", {
	id: uuid("id").primaryKey().defaultRandom(),
	chatbotUserId: uuid("chatbot_user_id")
		.references(() => chatbotUsers.id)
		.notNull(),
	method: verificationMethodEnum("method").notNull(),
	status: verificationStatusEnum("status").notNull(),
	reference: varchar("reference", { length: 255 }),
	initiatedAt: timestamp("initiated_at").notNull().defaultNow(),
	completedAt: timestamp("completed_at"),
	expiresAt: timestamp("expires_at"),
	metadata: jsonb("metadata").$type<{
		provider_reference?: string;
		error_message?: string;
		verification_code?: string;
		attempts?: number;
		ip_address?: string;
		device_info?: string;
	}>(),
});

export const verificationEventsRelations = relations(
	verificationEvents,
	({ one }) => ({
		chatbotUser: one(chatbotUsers, {
			fields: [verificationEvents.chatbotUserId],
			references: [chatbotUsers.id],
		}),
	}),
);
