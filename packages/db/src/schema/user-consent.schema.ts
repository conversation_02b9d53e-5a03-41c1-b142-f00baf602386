import { relations } from "drizzle-orm";
import {
	boolean,
	jsonb,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";

export const userConsent = pgTable(
	"user_consent",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: uuid("user_id")
			.references(() => users.id)
			.notNull(),
		tenantId: uuid("tenant_id").references(() => tenants.id),
		consentType: varchar("consent_type", { length: 50 }).notNull(),
		granted: boolean("granted").notNull(),
		grantedAt: timestamp("granted_at").defaultNow().notNull(),
		revokedAt: timestamp("revoked_at"),
		deletedAt: timestamp("deleted_at"),
		metadata: jsonb("metadata"),
	},
	(table) => ({
		userConsentUserConsentTypeIdx: uniqueIndex(
			"user_consent_user_consent_type_idx",
		).on(table.userId, table.consentType),
		userConsentDeletedAtIdx: uniqueIndex("user_consent_deleted_at_idx").on(
			table.deletedAt,
		),
	}),
);

export const userConsentRelations = relations(userConsent, ({ one }) => ({
	user: one(users, {
		fields: [userConsent.userId],
		references: [users.id],
	}),
	tenant: one(tenants, {
		fields: [userConsent.tenantId],
		references: [tenants.id],
	}),
}));
