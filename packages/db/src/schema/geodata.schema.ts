import {
	doublePrecision,
	index,
	jsonb,
	pgTable,
	timestamp, // Moved timestamp import
	uuid, // Moved uuid import
	varchar, // Moved varchar import
} from "drizzle-orm/pg-core"; // Group imports
import { geometry } from "drizzle-orm/pg-core";
import { relations, sql } from "drizzle-orm"; // Added sql import
import { SOURCE_DB_ENUM } from "../enums/source.enum.js"; // Corrected import
import { users } from "./users.schema.js"; // For userId foreign key
import { areas } from "./area.schema.js";   // For areaId foreign key
import { tenants } from "./tenants.schema.js"; // For tenantId foreign key

export const geodataEntries = pgTable(
	"geodata_entries", // Renamed for clarity
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id").references(() => tenants.id, { onDelete: "cascade" }), // Nullable for global geodata
		country: varchar("country", { length: 2 }), // Nullable if not always available
		city: varchar("city", { length: 255 }),    // Nullable if not always available
		address: varchar("address", { length: 255 }), // Nullable if not always available
		geom: geometry("geom", { srid: 4326, type: "point", mode: "xy" }).notNull(), // Changed mode to "xy"
		latitude: doublePrecision("latitude").notNull(),
		longitude: doublePrecision("longitude").notNull(),
		source: SOURCE_DB_ENUM("source").notNull(), // Corrected enum usage
		userId: uuid("user_id").references(() => users.id, { onDelete: "set null" }), // FK to users
		accuracy: doublePrecision("accuracy"),
		areaId: uuid("area_id").references(() => areas.id, { onDelete: "set null" }),   // FK to areas
		osmTags: jsonb("osm_tags").$type<Record<string, any>>(), // Added .$type
		metadata: jsonb("metadata").$type<Record<string, any>>(), // Added .$type
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull().$onUpdate(() => new Date()),
		deletedAt: timestamp("deleted_at"), // For soft deletes
	},
	(table) => [
		index("geodata_entries_tenant_id_idx").on(table.tenantId),
		index("geodata_entries_user_id_idx").on(table.userId),
		index("geodata_entries_area_id_idx").on(table.areaId),
		index("geodata_entries_source_idx").on(table.source),
		index("geodata_entries_geom_idx").using("gist", table.geom), // GIST index for spatial queries
		index("geodata_entries_deleted_at_idx")
			.on(table.deletedAt)
			.where(sql`${table.deletedAt} IS NOT NULL`),
	]
);

export const geodataEntriesRelations = relations(geodataEntries, ({ one }) => ({
	tenant: one(tenants, { fields: [geodataEntries.tenantId], references: [tenants.id], relationName: "tenantGeodata" }),
	user: one(users, { fields: [geodataEntries.userId], references: [users.id], relationName: "userGeodata" }),
	area: one(areas, { fields: [geodataEntries.areaId], references: [areas.id], relationName: "areaGeodata" }),
}));

export type GeodataEntry = typeof geodataEntries.$inferSelect;
export type NewGeodataEntry = typeof geodataEntries.$inferInsert;
