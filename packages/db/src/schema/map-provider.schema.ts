import { relations } from "drizzle-orm";
import {
	boolean,
	json,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar, // Moved varchar import
} from "drizzle-orm/pg-core"; // Group imports
import { tenants } from "./tenants.schema.js";

export const mapProvider = pgTable(
	"map_provider",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id)
			.notNull(),
		providerName: varchar("provider_name", { length: 50 }).notNull(),
		accessToken: varchar("access_token", { length: 255 }).notNull(),
		refreshToken: varchar("refresh_token", { length: 255 }),
		expiresAt: timestamp("expires_at"),
		isActive: boolean("is_active").notNull().default(true),
		config: json("config"),
		createdAt: timestamp("created_at").notNull().defaultNow(),
		updatedAt: timestamp("updated_at").notNull().defaultNow(),
	},
	(table) => ({
		indexes: [
			uniqueIndex("map_provider_tenant_provider_idx").on(
				table.tenantId,
				table.providerName,
			),
			uniqueIndex("map_provider_access_token_idx").on(table.accessToken),
		],
	}),
);

export const mapProviderRelations = relations(mapProvider, ({ one }) => ({
	tenant: one(tenants, {
		fields: [mapProvider.tenantId],
		references: [tenants.id],
	}),
}));

export type MapProvider = typeof mapProvider.$inferSelect;
export type NewMapProvider = typeof mapProvider.$inferInsert;
