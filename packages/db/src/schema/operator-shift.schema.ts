import { relations } from "drizzle-orm";
import { index, pgTable, timestamp, uniqueIndex, uuid } from "drizzle-orm/pg-core"; // Ensure this import exists
import { operators } from "./operators.schema.js";
import { tenants } from "./tenants.schema.js";

export const operatorShift = pgTable(
	"operator_shift",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		operatorId: uuid("operator_id")
			.references(() => operators.id)
			.notNull(),
		shiftStart: timestamp("shift_start").notNull(),
		shiftEnd: timestamp("shift_end").notNull(),
	},
	(table) => ({
		indexes: [
			index("operator_shift_tenant_id_idx").on(table.tenantId),
			uniqueIndex("operator_shift_idx").on(table.operatorId, table.shiftStart),
		],
	}),
);

export const operatorShiftRelations = relations(operatorShift, ({ one }) => ({
	tenant: one(tenants, {
		fields: [operatorShift.tenantId],
		references: [tenants.id],
	}),
	operator: one(operators, {
		fields: [operatorShift.operatorId],
		references: [operators.id],
	}),
}));
