import { bigint, pgTable, varchar } from "drizzle-orm/pg-core";

export const telegramUsers = pgTable("telegram_users", {
	id: bigint("id", { mode: "number" }).primaryKey(), // Telegram user ID as primary key
	firstName: varchar("first_name", { length: 255 }),
	lastName: varchar("last_name", { length: 255 }),
	username: varchar("username", { length: 255 }),
	// Add other relevant Telegram user fields as needed
});

export type TelegramUser = typeof telegramUsers.$inferSelect;
export type NewTelegramUser = typeof telegramUsers.$inferInsert;
