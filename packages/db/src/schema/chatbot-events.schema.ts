import { relations, sql } from "drizzle-orm"; // Added sql for filtered index
import {
	jsonb,
	index, // Added index
	pgTable,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core";
import { chatbotInstances } from "./chatbot-instances.schema.js";
import { chatbotMessages } from "./chatbot-messages.schema.js";
import { chatbotSessions } from "./chatbot-sessions.schema.js";
import { eventTypeDbEnum } from "../enums/event-type.enum.js";
import { users } from "./users.schema.js";

export const chatbotEvents = pgTable(
	"chatbot_events", // Pluralized table name
	{
		id: uuid("id").primaryKey().defaultRandom(),
		chatbotInstanceId: uuid("chatbot_instance_id")
			.references(() => chatbotInstances.id, { onDelete: "cascade" }) // Added onDelete
			.notNull(),
		eventType: eventTypeDbEnum("event_type").notNull(),
		userId: uuid("user_id").references(() => users.id, { onDelete: "set null" }), // Added onDelete
		sessionId: uuid("session_id").references(() => chatbotSessions.id, { onDelete: "cascade" }), // Added onDelete
		messageId: uuid("message_id").references(() => chatbotMessages.id, { onDelete: "cascade" }), // Added onDelete
		details: jsonb("details").$type<Record<string, any>>().notNull(), // Added .$type
		occurredAt: timestamp("occurred_at").defaultNow().notNull(),
		// No createdAt/updatedAt needed if events are immutable once created.
		// If events can be modified, add updatedAt.
		deletedAt: timestamp("deleted_at"),
	},
	(table) => [ // Changed from object to array
		// Indexes on foreign keys for query performance (typically not unique for an event table)
		index("chatbot_events_chatbot_instance_id_idx").on(table.chatbotInstanceId),
		index("chatbot_events_user_id_idx").on(table.userId),
		index("chatbot_events_session_id_idx").on(table.sessionId),
		index("chatbot_events_message_id_idx").on(table.messageId),
		index("chatbot_events_event_type_idx").on(table.eventType),
		index("chatbot_events_occurred_at_idx").on(table.occurredAt),
		// Optional: Filtered index for soft deletes
		index("chatbot_events_deleted_at_idx")
			.on(table.deletedAt)
			.where(sql`${table.deletedAt} IS NOT NULL`),
	],
);

export const chatbotEventsRelations = relations(chatbotEvents, ({ one }) => ({
	chatbotInstance: one(chatbotInstances, {
		fields: [chatbotEvents.chatbotInstanceId],
		references: [chatbotInstances.id],
		relationName: "instanceEvents", // More descriptive relation name
	}),
	user: one(users, {
		fields: [chatbotEvents.userId],
		references: [users.id],
		relationName: "userEvents",
	}),
	session: one(chatbotSessions, {
		fields: [chatbotEvents.sessionId],
		references: [chatbotSessions.id],
		relationName: "sessionEvents",
	}),
	message: one(chatbotMessages, {
		fields: [chatbotEvents.messageId],
		references: [chatbotMessages.id],
		relationName: "messageEvents",
	}),
}));

export type ChatbotEvent = typeof chatbotEvents.$inferSelect;
export type NewChatbotEvent = typeof chatbotEvents.$inferInsert;
