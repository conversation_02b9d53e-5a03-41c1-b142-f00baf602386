import { relations } from "drizzle-orm";
import { uniqueIndex } from "drizzle-orm/pg-core";
import { boolean, json, pgTable, timestamp, uuid } from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js"; // Added .js
import { userRoleDbEnum } from "../enums/user-role.enum.js"; // Added .js
import { userStatusDbEnum } from "../enums/user-status.enum.js"; // Added .js
import { users } from "./users.schema.js"; // Added .js

export const userTenants = pgTable(
	"user_tenant",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: uuid("user_id")
			.notNull()
			.references(() => users.id),
		tenantId: uuid("tenant_id")
			.notNull()
			.references(() => tenants.id),
		role: userRoleDbEnum("role").notNull(),
		status: userStatusDbEnum("status").notNull().default("ACTIVE"),
		isPrimary: boolean("is_primary").notNull().default(false),
		lastUsed: timestamp("last_used"),
		registeredAt: timestamp("registered_at").notNull().defaultNow(),
		invitedById: uuid("invited_by_id").references(() => users.id),
		metadata: json("metadata").notNull().default({}),
	},
	(table) => ({
		indexes: [
			uniqueIndex("user_tenant_user_id_tenant_id_idx").on(
				table.userId,
				table.tenantId,
			),
			uniqueIndex("user_tenant_invited_by_id_idx").on(table.invitedById),
		],
	}),
);

export const userTenantsRelations = relations(userTenants, ({ one }) => ({
	user: one(users, {
		fields: [userTenants.userId],
		references: [users.id],
	}),
	tenant: one(tenants, {
		fields: [userTenants.tenantId],
		references: [tenants.id],
	}),
	inviter: one(users, {
		fields: [userTenants.invitedById],
		references: [users.id],
	}),
}));
