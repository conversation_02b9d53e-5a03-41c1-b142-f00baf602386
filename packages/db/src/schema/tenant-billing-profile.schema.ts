import { relations } from "drizzle-orm";
import {
	boolean,
	jsonb,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { billingProfileStatusDbEnum } from "../enums/billing-profile-status.enum.js";
import { paymentMethodDbEnum } from "../enums/payment-method.enum.js";
import { tenants } from "./tenants.schema.js";

export const tenantBillingProfile = pgTable(
	"tenant_billing_profile",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id)
			.notNull(),
		name: varchar("name", { length: 255 }).notNull(),
		billingEmail: varchar("billing_email", { length: 255 }),
		billingPhone: varchar("billing_phone", { length: 50 }),
		taxId: varchar("tax_id", { length: 100 }),
		address: varchar("address", { length: 512 }),
		country: varchar("country", { length: 100 }),
		currency: varchar("currency", { length: 10 }),
		paymentMethod: paymentMethodDbEnum("payment_method").notNull(),
		defaultProfile: boolean("default_profile").default(false).notNull(),
		status: billingProfileStatusDbEnum("status").default("ACTIVE").notNull(),
		metadata: jsonb("metadata"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => ({
		tenantIdIndex: uniqueIndex("tenant_billing_profile_tenant_id_idx").on(
			table.tenantId,
		),
		taxIdUnique: uniqueIndex("tenant_billing_profile_tax_id_unique").on(
			table.taxId,
		),
		defaultProfilePerTenant: uniqueIndex(
			"tenant_billing_profile_default_per_tenant",
		).on(table.tenantId, table.defaultProfile),
	}),
);

export const tenantBillingProfileRelations = relations(
	tenantBillingProfile,
	({ one }) => ({
		tenant: one(tenants, {
			fields: [tenantBillingProfile.tenantId],
			references: [tenants.id],
		}),
	}),
);
