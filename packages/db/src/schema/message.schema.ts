import { relations, sql } from "drizzle-orm";
import { index, jsonb, pgTable, text, timestamp, uuid, varchar, boolean, geometry, } from "drizzle-orm/pg-core"; // Added text, boolean, geometry, index, sql, PgTable
import { chats } from "./chat.schema.js";
import { messageTypeDbEnum } from "../enums/message-type.enum.js";
import { providerEnum } from "../enums/provider.enum.js";
import { messageDirectionEnum } from "../enums/message-direction.enum.js";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";

// Define the table structure first

const messagesColumns = {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(),
	chatId: uuid("chat_id")
		.references(() => chats.id, { onDelete: "cascade" })
		.notNull(),
	fromUserId: uuid("from_user_id")
		.references(() => users.id, { onDelete: "set null" }) // Assuming users can be deleted but messages retained
		.notNull(),
	toUserId: uuid("to_user_id").references(() => users.id, { onDelete: "set null" }),
	providerMessageId: varchar("provider_message_id", { length: 255 }),
	direction: messageDirectionEnum("direction").notNull(),
	viaChannel: providerEnum("via_channel").notNull(),
	messageType: messageTypeDbEnum("message_type").notNull(),
	content: text("content").notNull(),
	phoneVerified: boolean("phone_verified"),
	verificationReference: varchar("verification_reference", { length: 255 }),
    // CORRECTED GEOLOCATION:
	geolocation: geometry("geolocation", { srid: 4326, type: "point", mode: "xy" }), // Use mode: "xy"
	repliedToMessageId: uuid("replied_to_message_id").references((): any => messages.id, { onDelete: "set null" }),
	errorFlag: boolean("error_flag").default(false),
	metadata: jsonb("metadata").$type<Record<string, any>>(),
	sentAt: timestamp("sent_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull().$onUpdate(() => new Date()),
	deletedAt: timestamp("deleted_at"),
};

export const messages = pgTable(
	"messages",
	messagesColumns,
	(table) => [
		index("messages_tenant_id_idx").on(table.tenantId),
		index("messages_chat_id_idx").on(table.chatId),
		index("messages_from_user_id_idx").on(table.fromUserId),
		index("messages_to_user_id_idx").on(table.toUserId),
		index("messages_sent_at_idx").on(table.sentAt),
		index("messages_message_type_idx").on(table.messageType),
		index("messages_phone_verified_idx").on(table.phoneVerified),
        // GIST index on the geometry column
		index("messages_geolocation_idx").using("gist", table.geolocation).where(sql`${table.geolocation} IS NOT NULL`),
		index("messages_deleted_at_idx")
			.on(table.deletedAt)
			.where(sql`${table.deletedAt} IS NOT NULL`),
	]
);

// ... (relations remain the same)
export const messagesRelations = relations(messages, ({ one }) => ({
	tenant: one(tenants, {
		fields: [messages.tenantId],
		references: [tenants.id],
	}),
	chat: one(chats, {
		fields: [messages.chatId],
		references: [chats.id],
	}),
	fromUser: one(users, {
		fields: [messages.fromUserId],
		references: [users.id],
		relationName: "messageSender",
	}),
	toUser: one(users, {
		fields: [messages.toUserId],
		references: [users.id],
		relationName: "messageReceiver",
	}),
	repliedTo: one(messages, {
		fields: [messages.repliedToMessageId],
		references: [messages.id],
		relationName: "originalMessage",
	}),
}));

export type Message = typeof messages.$inferSelect;
export type NewMessage = typeof messages.$inferInsert;