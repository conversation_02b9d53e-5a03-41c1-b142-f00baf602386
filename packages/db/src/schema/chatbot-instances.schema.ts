import { relations } from "drizzle-orm";
import {
	boolean,
	jsonb,
	pgTable,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { chatbotProviders } from "./chatbot-providers.schema.js";
import { tenants } from "./tenants.schema.js"; // Ensure this path is correct

export const chatbotInstances = pgTable("chatbot_instance", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(),
	providerId: uuid("provider_id")
		.references(() => chatbotProviders.id)
		.notNull(), // Consider onDelete: "cascade" or "set null"
	name: varchar("name", { length: 100 }).notNull(),
	botUsername: varchar("bot_username", { length: 100 }),
	webhookUrl: varchar("webhook_url", { length: 1024 }),
	config: jsonb("config"),
	isActive: boolean("is_active").notNull().default(false),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
	metadata: jsonb("metadata").$type<{
		group_id?: string;
		bot_token?: string;
	}>(),
});

export const chatbotInstancesRelations = relations(
	chatbotInstances,
	({ one }) => ({
		tenant: one(tenants, {
			fields: [chatbotInstances.tenantId],
			references: [tenants.id],
		}),
		provider: one(chatbotProviders, {
			fields: [chatbotInstances.providerId],
			references: [chatbotProviders.id],
		}),
	}),
);
