import { relations, sql } from "drizzle-orm";
import { index, jsonb, pgTable, text, timestamp, uniqueIndex, uuid, varchar } from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js"; // Assuming tenants schema exists for FK

export const systemSettings = pgTable(
	"system_settings", // Pluralized table name
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id").references(() => tenants.id, { onDelete: "cascade" }), // Nullable for global settings
		key: varchar("key", { length: 255 }).notNull(),
		value: jsonb("value").$type<any>().notNull(), // Changed to jsonb for flexibility
		description: text("description"), // Changed to text for longer descriptions
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull().$onUpdate(() => new Date()),
		deletedAt: timestamp("deleted_at"), // For soft deletes
	},
	(table) => [
		// Unique key per tenant, or globally if tenantId is NULL.
		// Note: PostgreSQL treats NULLs as distinct in unique constraints.
		// If you need key to be unique globally OR unique per tenant (but not both at once for the same key),
		// you might need more complex constraints or application logic.
		// This index ensures (key, tenant_id) pairs are unique.
		uniqueIndex("system_settings_key_tenant_id_idx").on(table.key, table.tenantId),
		index("system_settings_tenant_id_idx").on(table.tenantId),
		index("system_settings_key_idx").on(table.key),
		index("system_settings_deleted_at_idx")
			.on(table.deletedAt)
			.where(sql`${table.deletedAt} IS NOT NULL`),
	]
);

export const systemSettingsRelations = relations(systemSettings, ({ one }) => ({
	tenant: one(tenants, {
		fields: [systemSettings.tenantId],
		references: [tenants.id],
		relationName: "tenantSystemSettings",
	}),
}));

/*
Note: A `type` column (e.g., 'string', 'number', 'boolean', 'json') could be added
to help with validation and UI rendering if `value` stores different primitive types.
*/

export type SystemSetting = typeof systemSettings.$inferSelect;
export type NewSystemSetting = typeof systemSettings.$inferInsert;
