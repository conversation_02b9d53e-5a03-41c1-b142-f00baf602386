import { jsonb, pgTable, uuid, varchar, timestamp, index } from "drizzle-orm/pg-core";
import { geometry } from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

export const areas = pgTable("areas", { // Renamed table to plural
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id").notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	geom: geometry("geom", { srid: 4326, mode: "xy", type: "polygon" }).notNull(), // Changed mode to "xy"
	city: varchar("city", { length: 255 }),
	country: varchar("country", { length: 2 }),
	osmTags: jsonb("osm_tags").$type<Record<string, string | number | boolean>>(), // Added .$type with an example
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull().$onUpdate(() => new Date()),
	deletedAt: timestamp("deleted_at"),
}, (table) => [
	index("areas_tenant_id_idx").on(table.tenantId),
	index("areas_deleted_at_idx")
		.on(table.deletedAt)
		.where(sql`${table.deletedAt} IS NOT NULL`),
]);

export type Area = typeof areas.$inferSelect;
export type NewArea = typeof areas.$inferInsert;

/*
Example usage:

const polygon = ST_GeomFromText('POLYGON((...)', 4326);
await db
  .insert(areas)
  .values({ tenantId: tenantUUID, name: 'My Zone', geom: polygon })
  .execute();
*/
