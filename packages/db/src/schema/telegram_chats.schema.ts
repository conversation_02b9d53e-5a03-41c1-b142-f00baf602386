import { bigint, pgTable, timestamp, varchar } from "drizzle-orm/pg-core";

export const telegramChats = pgTable("telegram_chats", {
	id: bigint("id", { mode: "number" }).primaryKey(),
	type: varchar("type", { length: 32 }).notNull(), // "private", "group", "supergroup", "channel"
	title: varchar("title", { length: 256 }),
	username: varchar("username", { length: 32 }),
	createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
	updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
	lastSeen: timestamp("last_seen", { withTimezone: true }),
});
