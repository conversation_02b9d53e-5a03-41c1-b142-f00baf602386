import { relations } from "drizzle-orm";
import {
	index,
	jsonb,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
} from "drizzle-orm/pg-core";
import { onboardingStatusDbEnum } from "../enums/onboarding-status.enum.js";
import { tenants } from "./tenants.schema.js"; // Ensure this file exists
import { users } from "./users.schema.js";

export const userOnboarding = pgTable(
	"user_onboarding",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: uuid("user_id")
			.references(() => users.id)
			.notNull(),
		tenantId: uuid("tenant_id").references(() => tenants.id),
		completedSteps: jsonb("completed_steps"),
		status: onboardingStatusDbEnum("status").notNull(),
		completedAt: timestamp("completed_at"),
		metadata: jsonb("metadata"),
		deletedAt: timestamp("deleted_at"),
	},
	(table) => ({
		indexes: [
			uniqueIndex("user_onboarding_user_tenant_idx").on(
				table.userId,
				table.tenantId,
			),
			index("user_onboarding_deleted_at_idx").on(table.deletedAt),
		],
	}),
);

export const userOnboardingRelations = relations(userOnboarding, ({ one }) => ({
	user: one(users, {
		fields: [userOnboarding.userId],
		references: [users.id],
	}),
	tenant: one(tenants, {
		fields: [userOnboarding.tenantId],
		references: [tenants.id],
	}),
}));
