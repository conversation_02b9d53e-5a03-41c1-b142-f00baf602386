import { relations } from "drizzle-orm";
import {
	boolean,
	integer,
	numeric,
	pgEnum,
	pgTable,
	timestamp,
	uuid,
	varchar, 
} from "drizzle-orm/pg-core"; 
import { tenants } from "./tenants.schema.js";

export const promotionTypeEnum = pgEnum("promotion_type", [
	"percentage",
	"value",
	"free_ride",
]);

export const promotions = pgTable("promotion", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id)
		.notNull(),
	code: varchar("code", { length: 255 }).notNull(),
	type: promotionTypeEnum("type").notNull(),
	value: numeric("value", { precision: 10, scale: 2 }).notNull(),
	usageLimit: integer("usage_limit"),
	validFrom: timestamp("valid_from"),
	validTo: timestamp("valid_to"),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const promotionsRelations = relations(promotions, ({ one }) => ({
	tenant: one(tenants, {
		fields: [promotions.tenantId],
		references: [tenants.id],
	}),
}));

export type Promotion = typeof promotions.$inferSelect;
export type NewPromotion = typeof promotions.$inferInsert;
