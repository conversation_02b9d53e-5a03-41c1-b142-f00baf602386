import { relations } from "drizzle-orm";
import {
	integer,
	pgTable,
	timestamp,
	uuid, // Moved uuid import
	varchar, // Moved varchar import
} from "drizzle-orm/pg-core"; // Group imports
import { callDirectionDbEnum } from "../enums/call-direction.enum.js"; // Ensure this file exports callDirectionEnum only
import { callStatusDbEnum } from "../enums/call-status.enum.js";
import { operators } from "./operators.schema.js";
import { rides } from "./rides.schema.js"; // Ensure this file exists
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";

export const pbxCalls = pgTable("pbx_call", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id)
		.notNull(),
	userId: uuid("user_id").references(() => users.id),
	operatorId: uuid("operator_id").references(() => operators.id),
	rideId: uuid("ride_id").references(() => rides.id),
	direction: callDirectionDbEnum("direction").notNull(),
	status: callStatusDbEnum("status").notNull(),
	duration: integer("duration"),
	recordingUrl: varchar("recording_url", { length: 1024 }),
	startedAt: timestamp("started_at").notNull(),
	endedAt: timestamp("ended_at"),
});

export const pbxCallsRelations = relations(pbxCalls, ({ one }) => ({
	tenant: one(tenants, {
		fields: [pbxCalls.tenantId],
		references: [tenants.id],
	}),
	user: one(users, {
		fields: [pbxCalls.userId],
		references: [users.id],
	}),
	operator: one(operators, {
		fields: [pbxCalls.operatorId],
		references: [operators.id],
	}),
	ride: one(rides, {
		fields: [pbxCalls.rideId],
		references: [rides.id],
	}),
}));
