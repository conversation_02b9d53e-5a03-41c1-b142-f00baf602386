import { relations } from "drizzle-orm";
import { pgTable, timestamp, uuid, varchar } from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js"; // Ensure this path is correct

export const chats = pgTable("chat", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(),
	type: varchar("type", { length: 50 }).notNull(), // e.g., "one-on-one", "group", "support"
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const chatsRelations = relations(chats, ({ one }: { one: any }) => ({
	tenant: one(tenants, {
		fields: [chats.tenantId],
		references: [tenants.id],
	}),
}));
