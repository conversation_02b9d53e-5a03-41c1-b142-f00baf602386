import { relations } from "drizzle-orm";
import { jsonb, pgTable, timestamp, uuid, varchar } from "drizzle-orm/pg-core";
import { providerEnum } from "../enums/provider.enum.js";
import { tenants } from "./tenants.schema.js";

export const chatbots = pgTable("chatbot", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(),
	provider: providerEnum("provider").notNull(), // e.g., "telegram", "viber"
	botId: varchar("bot_id", { length: 255 }).notNull(),
	config: jsonb("config"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const chatbotsRelations = relations(chatbots, ({ one }) => ({
	tenant: one(tenants, {
		fields: [chatbots.tenantId],
		references: [tenants.id],
	}),
}));
