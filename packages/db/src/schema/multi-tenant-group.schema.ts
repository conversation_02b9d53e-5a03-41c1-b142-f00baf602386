import { relations } from "drizzle-orm";
import {
	jsonb,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar, // Moved varchar import
} from "drizzle-orm/pg-core"; // Group imports
import { groupStatusEnum } from "../enums/group-status.enum.js";
import { groupTypeEnum } from "../enums/group-type.enum.js";

export const multiTenantGroup = pgTable(
	"multi_tenant_group",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		name: varchar("name", { length: 255 }).notNull(),
		type: groupTypeEnum("type").notNull(),
		contactEmail: varchar("contact_email", { length: 255 }),
		contactPhone: varchar("contact_phone", { length: 50 }),
		parentGroupId: uuid("parent_group_id").references(
			(): any => multiTenantGroup.id,
		),
		settings: jsonb("settings"),
		status: groupStatusEnum("status").default("PENDING").notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
		metadata: jsonb("metadata"),
	},
	(table) => ({
		nameUniquePerParent: uniqueIndex("name_unique_per_parent").on(
			table.parentGroupId,
			table.name,
		),
		statusIndex: uniqueIndex("status_idx").on(table.status),
	}),
);

export const multiTenantGroupRelations = relations(
	multiTenantGroup,
	({ one, many }) => ({
		parentGroup: one(multiTenantGroup, {
			fields: [multiTenantGroup.parentGroupId],
			references: [multiTenantGroup.id],
		}),
		childGroups: many(multiTenantGroup),
	}),
);
