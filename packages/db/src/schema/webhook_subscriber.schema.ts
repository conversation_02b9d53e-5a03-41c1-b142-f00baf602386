import { relations } from "drizzle-orm";
import { pgTable, text, timestamp, uuid, varchar } from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";

export const webhook_subscriber = pgTable("webhook_subscriber", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenant_id: uuid("tenant_id")
		.references(() => tenants.id)
		.notNull(),
	url: varchar("url", { length: 255 }).notNull(),
	events: text("events").notNull(), // Consider using JSONB for structured events
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const webhook_subscriberRelations = relations(
	webhook_subscriber,
	({ one }) => ({
		tenant: one(tenants, {
			fields: [webhook_subscriber.tenant_id],
			references: [tenants.id],
		}),
	}),
);
