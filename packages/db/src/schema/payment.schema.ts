import { relations } from "drizzle-orm";
import {
	jsonb,
	numeric,
	pgTable,
	timestamp,
	uniqueIndex, // Moved uniqueIndex import
	uuid, // Moved uuid import
	varchar, // Moved varchar import
} from "drizzle-orm/pg-core"; // Group imports
import { paymentMethodDbEnum } from "../enums/payment-method.enum.js";
import { paymentStatusEnum } from "../enums/payment-status.enum.js";
import { rides } from "./rides.schema.js"; // Ensure this file exists
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";

export const payments = pgTable(
	"payment",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id)
			.notNull(),
		rideId: uuid("ride_id").references(() => rides.id), // Optional for wallet top-ups
		userId: uuid("user_id")
			.references(() => users.id)
			.notNull(),
		amount: numeric("amount", { precision: 10, scale: 2 }).notNull(), // Changed default value to string
		currency: varchar("currency", { length: 3 }).notNull(),
		method: paymentMethodDbEnum("method").notNull(),
		processorRef: varchar("processor_ref", { length: 255 }),
		status: paymentStatusEnum("status").default("pending").notNull(),
		processedAt: timestamp("processed_at"),
		metadata: jsonb("metadata"),
	},
	(table) => ({
		indexes: [
			uniqueIndex("payment_processor_ref_idx").on(table.processorRef),
			uniqueIndex("payment_user_status_idx").on(table.userId, table.status),
		],
	}),
);

export const paymentsRelations = relations(payments, ({ one }) => ({
	tenant: one(tenants, {
		fields: [payments.tenantId],
		references: [tenants.id],
	}),
	ride: one(rides, {
		fields: [payments.rideId],
		references: [rides.id],
	}),
	user: one(users, {
		fields: [payments.userId],
		references: [users.id],
	}),
}));
