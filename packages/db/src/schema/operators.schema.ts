import { relations } from "drizzle-orm";
import { index, pgTable, timestamp, uuid, varchar } from "drizzle-orm/pg-core";
import { operatorStatusEnum } from "../enums/operator-status.enum.js";
import { userTenants } from "./user-tenant.schema.js";
import { tenants } from "./tenants.schema.js";

export const operators = pgTable("operators", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id") // Added for direct scoping
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(),
	userTenantId: uuid("user_tenant_id")
		.notNull()
		.references(() => userTenants.id, { onDelete: "cascade" }),
	firstName: varchar("first_name", { length: 100 }).notNull(),
	lastName: varchar("last_name", { length: 100 }).notNull(),
	phone: varchar("phone", { length: 20 }).notNull(),
	status: operatorStatusEnum("status").default("ACTIVE").notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at")
		.defaultNow()
		.$onUpdate(() => new Date())
		.notNull(),
}, (table) => [
	index("operators_tenant_id_idx").on(table.tenantId)
]);

export const operatorsRelations = relations(operators, ({ one }) => ({
	tenant: one(tenants, {
		fields: [operators.tenantId],
		references: [tenants.id],
	}),
	userTenant: one(userTenants, {
		fields: [operators.userTenantId],
		references: [userTenants.id],
	}),
}));

export type Operator = typeof operators.$inferSelect;
export type NewOperator = typeof operators.$inferInsert;
