import { relations } from "drizzle-orm";
import {
	boolean,
	jsonb,
	pgTable,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";

export const chatbotProviders = pgTable("chatbot_provider", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(), // Ensure this path is correct
	name: varchar("name", { length: 50 }).notNull(),
	description: varchar("description", { length: 500 }),
	logoUrl: varchar("logo_url", { length: 1024 }),
	features: jsonb("features"),
	enabled: boolean("enabled").notNull().default(false),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const chatbotProvidersRelations = relations(chatbotProviders, () => ({
	// Relations will be added here if needed
}));
