import { relations } from "drizzle-orm";
import {
	jsonb,
	numeric,
	pgTable,
	timestamp, // Keep timestamp import
	uniqueIndex, // Moved uniqueIndex import
	uuid, // Moved uuid import
	varchar, // Moved varchar import
} from "drizzle-orm/pg-core"; // Group imports
import { paymentStatusEnum } from "../enums/payment-status.enum.js";
import { tenants } from "./tenants.schema.js"; // Ensure this file exists

export const invoices = pgTable( // Renamed table to plural
	"invoices",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" }) // Added onDelete
			.notNull(),
		amount: numeric("amount", { precision: 10, scale: 2 }).notNull(),
		currency: varchar("currency", { length: 3 }).notNull(),
		status: paymentStatusEnum("status").default("pending").notNull(), // Ensure enum name is generic or specific like "invoices_status_enum"
		dueDate: timestamp("due_date").notNull(),
		issuedAt: timestamp("issued_at").defaultNow().notNull(),
		paidAt: timestamp("paid_at"),
		metadata: jsonb("metadata").$type<Record<string, any>>(), // Added .$type
		createdAt: timestamp("created_at").defaultNow().notNull(), // Added createdAt
		updatedAt: timestamp("updated_at").defaultNow().notNull().$onUpdate(() => new Date()), // Added updatedAt
		deletedAt: timestamp("deleted_at"), // Added deletedAt
	},
	(table) => [ // Changed from object to array
		uniqueIndex("invoices_tenant_id_due_date_idx").on(table.tenantId, table.dueDate), // Prefixed index name
		// Add other relevant indexes, e.g., on status
		// index("invoices_status_idx").on(table.status),
	]
);

export const invoicesRelations = relations(invoices, ({ one }) => ({
	tenant: one(tenants, {
		fields: [invoices.tenantId],
		references: [tenants.id],
		relationName: "invoiceTenant", // Optional: more descriptive relation name
	}),
}));

export type Invoice = typeof invoices.$inferSelect;
export type NewInvoice = typeof invoices.$inferInsert;
