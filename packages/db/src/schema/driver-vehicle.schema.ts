import { index, pgTable, timestamp, uuid } from "drizzle-orm/pg-core";
import { users } from "./users.schema.js"; // Corrected import
import { vehicles } from "./vehicle.schema.js";
import { tenants } from "./tenants.schema.js";

export const driverVehicles = pgTable("driver_vehicle", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id")
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(),
	driverId: uuid("driver_id")
		.references(() => users.id)
		.notNull(),
	vehicleId: uuid("vehicle_id")
		.references(() => vehicles.id)
		.notNull(),
	fromTime: timestamp("from_time").notNull(),
	toTime: timestamp("to_time").notNull(),
}, (table) => [
	index("driver_vehicles_tenant_id_idx").on(table.tenantId)
]);
import { relations } from "drizzle-orm";
export const driverVehicleRelations = relations(driverVehicles, ({ one }) => ({
	tenant: one(tenants, { fields: [driverVehicles.tenantId], references: [tenants.id] }),
	driver: one(users, {
		fields: [driverVehicles.driverId],
		references: [users.id],
	}),
	vehicle: one(vehicles, {
		fields: [driverVehicles.vehicleId],
		references: [vehicles.id],
	}),
}));
