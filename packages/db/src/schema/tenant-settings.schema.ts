import { relations } from "drizzle-orm";
import {
	jsonb,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { settingTypeEnum } from "../enums/setting-type.enum.js";
import { tenants } from "./tenants.schema.js"; // Assuming this schema exists

export const tenantSettings = pgTable(
	"tenant_settings",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id)
			.notNull(),
		key: varchar("key", { length: 255 }).notNull(),
		value: jsonb("value"),
		type: settingTypeEnum("type").notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => ({
		uniqueKeyPerTenant: uniqueIndex("tenant_settings_tenant_id_key_unique").on(
			table.tenantId,
			table.key,
		),
		tenantIdIndex: uniqueIndex("tenant_settings_tenant_id_idx").on(
			table.tenantId,
		),
		keyIndex: uniqueIndex("tenant_settings_key_idx").on(table.key),
	}),
);

export const tenantSettingsRelations = relations(tenantSettings, ({ one }) => ({
	tenant: one(tenants, {
		fields: [tenantSettings.tenantId],
		references: [tenants.id],
	}),
}));
