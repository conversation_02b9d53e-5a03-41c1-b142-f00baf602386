import { relations } from "drizzle-orm";
import { index, pgTable, timestamp, uuid } from "drizzle-orm/pg-core";
import { operatorStatusEnum } from "../enums/operator-status.enum.js";
import { userTenants } from "./user-tenant.schema.js";
import { tenants } from "./tenants.schema.js";

/**
 * The operator_extensions table stores operator-specific data.
 * It extends the user_tenant table for users with the OPERATOR role.
 */
export const operatorExtensions = pgTable("operator_extensions", {
	id: uuid("id").primaryKey().defaultRandom(),
	tenantId: uuid("tenant_id") // Added for direct scoping
		.references(() => tenants.id, { onDelete: "cascade" })
		.notNull(),
	userTenantId: uuid("user_tenant_id")
		.notNull()
		.references(() => userTenants.id, { onDelete: "cascade" }),

	// Operator-specific fields
	status: operatorStatusEnum("status").default("ACTIVE"),

	// Timestamps
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at")
		.defaultNow()
		.$onUpdate(() => new Date())
		.notNull(),
}, (table) => [
	index("operator_extensions_tenant_id_idx").on(table.tenantId)
]);

export const operatorExtensionsRelations = relations(
	operatorExtensions,
	({ one }) => ({
		tenant: one(tenants, {
			fields: [operatorExtensions.tenantId],
			references: [tenants.id],
		}),
		userTenant: one(userTenants, {
			fields: [operatorExtensions.userTenantId],
			references: [userTenants.id],
		}),
	}),
);

export type OperatorExtension = typeof operatorExtensions.$inferSelect;
export type NewOperatorExtension = typeof operatorExtensions.$inferInsert;
