import postgres from "postgres";
import { drizzle, type PostgresJsDatabase } from "drizzle-orm/postgres-js";
import { config } from "./config.js";
import * as schema from "./schema/index.js"; // Import all schemas

// Optionally re-export common Drizzle functions for convenience
export { 
    sql, eq, and, or, asc, desc, like, ilike, not, isNull, isNotNull, 
    inArray, notInArray, between, notBetween, count, countDistinct, 
    avg, sum, min, max, exists, placeholder, getTableColumns, getTableName
} from "drizzle-orm";
// Create a postgres client instance
const queryClient = postgres(config.DATABASE_URL);

// Create a Drizzle instance with proper typing
export const db: PostgresJsDatabase<typeof schema> = drizzle(queryClient, { schema });

// Export the fully typed db instance for consumers
export type DbType = typeof db;

// Re-export all schemas and individual schema objects
export * from "./schema/index.js";

// Re-export types
export * from "./types/database.js";

// Re-export utils
export * from "./utils/bot-token.js";
