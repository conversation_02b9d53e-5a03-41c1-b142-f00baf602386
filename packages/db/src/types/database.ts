/**
 * Database types for the multi-tenant bot system
 */

/**
 * Bot configuration from the database
 */
export interface BotConfigFromDB {
	id: string; // Corresponds to bots.id (UUID)
	tenantId: string; // Corresponds to tenants.id (UUID)
	token: string; // Decrypted token
	webhookPathSegment: string;
	botUsername?: string | null;
	isEnabled: boolean;
	config?: any; // Parsed JSONB from bots.config
}

/**
 * Tenant information from the database
 */
export interface TenantFromDB {
	id: string;
	name: string;
	createdAt: Date;
	updatedAt: Date;
}

/**
 * Role information from the database
 */
export interface RoleFromDB {
	id: string;
	tenantId: string;
	name: string;
	description?: string | null;
	permissions: string[];
	isDefault: boolean;
	isSystem: boolean;
}

/**
 * User bot role information from the database
 */
export interface UserBotRoleFromDB {
	telegramUserId: number;
	botId: string;
	roleId: string;
}
