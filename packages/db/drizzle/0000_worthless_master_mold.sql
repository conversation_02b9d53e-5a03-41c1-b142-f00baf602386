CREATE TYPE "public"."message_direction" AS ENUM('in', 'out');--> statement-breakpoint
CREATE TYPE "public"."promotion_type" AS ENUM('percentage', 'value', 'free_ride');--> statement-breakpoint
CREATE TYPE "public"."status" AS ENUM('ACTIVE', 'DISABLED', 'DECOMMISSIONED', 'REPAIR');--> statement-breakpoint
CREATE TYPE "public"."verification_method" AS ENUM('sms', 'email', 'phone_call', 'other');--> statement-breakpoint
CREATE TYPE "public"."verification_status" AS ENUM('initiated', 'code_sent', 'verified', 'failed', 'expired');--> statement-breakpoint
CREATE TYPE "public"."actor_type" AS ENUM('user', 'operator', 'system');--> statement-breakpoint
CREATE TYPE "public"."direction" AS ENUM('inbound', 'outbound');--> statement-breakpoint
CREATE TYPE "public"."event_type" AS ENUM('message_received', 'message_sent', 'user_linked', 'user_unlinked', 'consent_granted', 'consent_revoked', 'verification_requested', 'verification_succeeded', 'verification_failed', 'session_started', 'session_ended', 'error', 'webhook_received');--> statement-breakpoint
CREATE TYPE "public"."group_status" AS ENUM('ACTIVE', 'PENDING', 'INACTIVE');--> statement-breakpoint
CREATE TYPE "public"."group_type" AS ENUM('FRANCHISE', 'AGGREGATOR', 'BRAND', 'CORPORATE');--> statement-breakpoint
CREATE TYPE "public"."message_type" AS ENUM('text', 'image', 'voice', 'location', 'verification', 'document', 'sticker', 'button', 'template', 'interactive', 'carousel', 'group_chat', 'unknown');--> statement-breakpoint
CREATE TYPE "public"."operator_status" AS ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING');--> statement-breakpoint
CREATE TYPE "public"."payment_method" AS ENUM('CARD', 'WIRE', 'CRYPTO', 'INVOICE');--> statement-breakpoint
CREATE TYPE "public"."change_type" AS ENUM('onboarding', 'phone_change', 'admin_update');--> statement-breakpoint
CREATE TYPE "public"."promo_status" AS ENUM('ACTIVE', 'INACTIVE', 'EXPIRED', 'LIMIT_REACHED');--> statement-breakpoint
CREATE TYPE "public"."promo_type" AS ENUM('PERCENTAGE', 'FIXED', 'FREE_RIDE');--> statement-breakpoint
CREATE TYPE "public"."provider" AS ENUM('telegram', 'viber', 'facebook', 'google', 'apple', 'phone');--> statement-breakpoint
CREATE TYPE "public"."ride_rating" AS ENUM('ONE', 'TWO', 'THREE', 'FOUR', 'FIVE');--> statement-breakpoint
CREATE TYPE "public"."order_type" AS ENUM('ride', 'delivery', 'pooled', 'shared');--> statement-breakpoint
CREATE TYPE "public"."setting_type" AS ENUM('SYSTEM', 'PAYMENT', 'I18N', 'UI');--> statement-breakpoint
CREATE TYPE "public"."source" AS ENUM('telegram', 'android', 'mqtt', 'web', 'manual');--> statement-breakpoint
CREATE TYPE "public"."support_ticket_status" AS ENUM('open', 'closed', 'pending');--> statement-breakpoint
CREATE TYPE "public"."plan" AS ENUM('FREE', 'PRO', 'ENTERPRISE');--> statement-breakpoint
CREATE TYPE "public"."role" AS ENUM('PASSENGER', 'DRIVER', 'OPERATOR', 'MANAGER', 'ADMIN', 'SUPPORT');--> statement-breakpoint
CREATE TABLE "area" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(255) NOT NULL,
	"geom" geometry(point) NOT NULL,
	"city" varchar(255),
	"country" varchar(2),
	"osm_tags" jsonb
);
--> statement-breakpoint
CREATE TABLE "audit_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"actor_id" uuid NOT NULL,
	"actor_type" "actor_type" NOT NULL,
	"event_type" varchar(255) NOT NULL,
	"target_table" varchar(255) NOT NULL,
	"target_id" uuid,
	"description" text NOT NULL,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "audit_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"actor_id" uuid NOT NULL,
	"actor_type" "actor_type" NOT NULL,
	"event_type" varchar(255) NOT NULL,
	"target_table" varchar(255) NOT NULL,
	"target_id" uuid,
	"description" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "bots" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"token" text NOT NULL,
	"webhook_path_segment" varchar(100) NOT NULL,
	"bot_username" varchar(255),
	"is_enabled" boolean DEFAULT true NOT NULL,
	"config" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "bots_webhook_path_segment_unique" UNIQUE("webhook_path_segment")
);
--> statement-breakpoint
CREATE TABLE "chat" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"type" varchar(50) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_config" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider_id" uuid NOT NULL,
	"settings" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_event" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"deleted_at" timestamp,
	"chatbot_instance_id" uuid NOT NULL,
	"event_type" "event_type" NOT NULL,
	"user_id" uuid,
	"session_id" uuid,
	"message_id" uuid,
	"details" jsonb NOT NULL,
	"occurred_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_instance" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider_id" uuid NOT NULL,
	"name" varchar(100) NOT NULL,
	"bot_username" varchar(100),
	"webhook_url" varchar(1024),
	"config" jsonb,
	"is_active" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot_message" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_session_id" uuid NOT NULL,
	"chatbot_user_id" uuid NOT NULL,
	"direction" "message_direction" NOT NULL,
	"sent_at" timestamp NOT NULL,
	"message_type" varchar(50) NOT NULL,
	"content" text NOT NULL,
	"phone_verified" boolean DEFAULT false NOT NULL,
	"verification_reference" varchar(255),
	"replied_to_id" uuid,
	"error_flag" boolean DEFAULT false NOT NULL,
	"provider_message_id" varchar(255),
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot_message_with_geolocation" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"message_id" uuid NOT NULL,
	"location" geometry(point) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_provider" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(50) NOT NULL,
	"description" varchar(500),
	"logo_url" varchar(1024),
	"features" jsonb,
	"enabled" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chatbot_session" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_user_id" uuid NOT NULL,
	"tenant_id" uuid NOT NULL,
	"started_at" timestamp NOT NULL,
	"ended_at" timestamp,
	"verified" boolean DEFAULT false NOT NULL,
	"geolocation" geometry(point),
	"context" varchar(100),
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot_users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"chatbot_instance_id" uuid NOT NULL,
	"provider_user_id" varchar(255),
	"phone_verified" boolean DEFAULT false NOT NULL,
	"verification_date" timestamp NOT NULL,
	"consent" boolean DEFAULT false NOT NULL,
	"consent_date" timestamp,
	"consent_revoked_at" timestamp,
	"blocked" boolean DEFAULT false NOT NULL,
	"verified_at" timestamp,
	"locale" varchar(10),
	"joined_at" timestamp DEFAULT now() NOT NULL,
	"last_seen_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "chatbot" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider" "provider" NOT NULL,
	"bot_id" varchar(255) NOT NULL,
	"config" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "dispatch_assignment" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"operator_id" uuid NOT NULL,
	"ride_id" uuid NOT NULL,
	"assigned_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "driver_vehicle" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"driver_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"from_time" timestamp NOT NULL,
	"to_time" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "geodata" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"country" varchar(2) NOT NULL,
	"city" varchar(255) NOT NULL,
	"address" varchar(255) NOT NULL,
	"geom" geometry(point) NOT NULL,
	"latitude" double precision NOT NULL,
	"longitude" double precision NOT NULL,
	"source" "source" NOT NULL,
	"user_id" uuid,
	"accuracy" double precision,
	"area_id" uuid,
	"osm_tags" jsonb,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "i18n_translation" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"key" varchar(255) NOT NULL,
	"value" text NOT NULL,
	"locale" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "invoice" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"currency" varchar(3) NOT NULL,
	"status" "status" DEFAULT 'pending' NOT NULL,
	"due_date" timestamp NOT NULL,
	"issued_at" timestamp DEFAULT now() NOT NULL,
	"paid_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "map_provider" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"provider_name" varchar(50) NOT NULL,
	"access_token" varchar(255) NOT NULL,
	"refresh_token" varchar(255),
	"expires_at" timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"config" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "message" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"chat_id" uuid NOT NULL,
	"from_user_id" uuid NOT NULL,
	"to_user_id" uuid,
	"via_channel" "provider" NOT NULL,
	"type" "message_type" NOT NULL,
	"content" varchar(4096) NOT NULL,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "multi_tenant_group" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"type" "group_type" NOT NULL,
	"contact_email" varchar(255),
	"contact_phone" varchar(50),
	"parent_group_id" uuid,
	"settings" jsonb,
	"status" "group_status" DEFAULT 'PENDING' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "operator_extensions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_tenant_id" uuid NOT NULL,
	"status" "operator_status" DEFAULT 'ACTIVE',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "operator_shift" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"operator_id" uuid NOT NULL,
	"shift_start" timestamp NOT NULL,
	"shift_end" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "operators" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_tenant_id" uuid NOT NULL,
	"first_name" varchar(100) NOT NULL,
	"last_name" varchar(100) NOT NULL,
	"phone" varchar(20) NOT NULL,
	"status" "operator_status" DEFAULT 'ACTIVE' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "payment" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"ride_id" uuid,
	"user_id" uuid NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"currency" varchar(3) NOT NULL,
	"method" "payment_method" NOT NULL,
	"processor_ref" varchar(255),
	"status" "status" DEFAULT 'pending' NOT NULL,
	"processed_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "pbx_call" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_id" uuid,
	"operator_id" uuid,
	"ride_id" uuid,
	"direction" "direction" NOT NULL,
	"status" "status" NOT NULL,
	"duration" integer,
	"recording_url" varchar(1024),
	"started_at" timestamp NOT NULL,
	"ended_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "promos" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"code" varchar(20) NOT NULL,
	"description" varchar(255),
	"discount_value" integer NOT NULL,
	"discount_type" "promo_type" NOT NULL,
	"max_uses" integer,
	"uses" integer DEFAULT 0 NOT NULL,
	"start_date" timestamp NOT NULL,
	"end_date" timestamp NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"status" "promo_status" DEFAULT 'ACTIVE' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "promotion" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"code" varchar(255) NOT NULL,
	"type" "promotion_type" NOT NULL,
	"value" numeric(10, 2) NOT NULL,
	"usage_limit" integer,
	"valid_from" timestamp,
	"valid_to" timestamp,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ride_event" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"ride_id" uuid NOT NULL,
	"event_type" varchar(50) NOT NULL,
	"details" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ride_order" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"passenger_id" uuid,
	"driver_id" uuid,
	"vehicle_id" uuid,
	"pickup_address" varchar(255),
	"pickup_latlng" jsonb,
	"dropoff_address" varchar(255),
	"dropoff_latlng" jsonb,
	"scheduled_time" timestamp,
	"confirmed_time" timestamp,
	"start_time" timestamp,
	"end_time" timestamp,
	"status" "status" DEFAULT 'searching' NOT NULL,
	"order_type" "order_type" DEFAULT 'ride' NOT NULL,
	"estimated_fare" numeric(10, 2),
	"currency" varchar(3),
	"payment_method" varchar(50),
	"paid" boolean DEFAULT false,
	"promo_id" uuid,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ride_ratings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"ride_id" uuid NOT NULL,
	"from_user_id" uuid NOT NULL,
	"to_user_id" uuid NOT NULL,
	"rating" integer NOT NULL,
	"feedback" text,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "ride_rating" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"ride_id" uuid NOT NULL,
	"from_user_id" uuid NOT NULL,
	"to_user_id" uuid NOT NULL,
	"rating" integer NOT NULL,
	"feedback" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "rides" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"passenger_id" uuid NOT NULL,
	"driver_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"status" "status" DEFAULT 'searching' NOT NULL,
	"order_type" "order_type" DEFAULT 'ride' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "roles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(50) NOT NULL,
	"description" varchar(1024),
	"permissions" jsonb,
	"is_default" boolean DEFAULT false NOT NULL,
	"is_system" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE "support_tickets" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"subject" varchar(256) NOT NULL,
	"details" text NOT NULL,
	"status" "support_ticket_status" NOT NULL,
	"assigned_to_id" uuid,
	"created_at" timestamp DEFAULT now(),
	"closed_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "support_ticket" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"subject" varchar(255) NOT NULL,
	"details" text NOT NULL,
	"status" "support_ticket_status" NOT NULL,
	"assigned_to_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"closed_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "system_setting" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"key" varchar(255) NOT NULL,
	"value" text NOT NULL,
	"description" varchar(255),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "telegram_chat_members" (
	"chat_id" bigint NOT NULL,
	"user_id" integer NOT NULL,
	CONSTRAINT "telegram_chat_members_chat_id_user_id_pk" PRIMARY KEY("chat_id","user_id")
);
--> statement-breakpoint
CREATE TABLE "telegram_chats" (
	"id" bigint PRIMARY KEY NOT NULL,
	"type" varchar(32) NOT NULL,
	"title" varchar(256),
	"username" varchar(32),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"last_seen" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "telegram_messages" (
	"id" bigint PRIMARY KEY NOT NULL,
	"chat_id" bigint NOT NULL,
	"user_id" integer,
	"message_type" varchar(32),
	"text" varchar(4096),
	"file_id" varchar(256),
	"caption" varchar(1024),
	"reply_to_message_id" bigint,
	"forward_from_user_id" integer,
	"forward_from_chat_id" bigint,
	"edit_date" timestamp with time zone,
	"entities" json,
	"command" varchar(64),
	"payload" json,
	"service_type" varchar(32),
	"date" timestamp with time zone NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"raw_data" json
);
--> statement-breakpoint
CREATE TABLE "telegram_users" (
	"id" bigint PRIMARY KEY NOT NULL,
	"first_name" varchar(255),
	"last_name" varchar(255),
	"username" varchar(255)
);
--> statement-breakpoint
CREATE TABLE "telegram_user_settings" (
	"user_id" integer PRIMARY KEY NOT NULL,
	"language" varchar(8),
	"notifications" boolean DEFAULT true
);
--> statement-breakpoint
CREATE TABLE "tenant_billing_profile" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"name" varchar(255) NOT NULL,
	"billing_email" varchar(255),
	"billing_phone" varchar(50),
	"tax_id" varchar(100),
	"address" varchar(512),
	"country" varchar(100),
	"currency" varchar(10),
	"payment_method" "payment_method" NOT NULL,
	"default_profile" boolean DEFAULT false NOT NULL,
	"status" "status" DEFAULT 'ACTIVE' NOT NULL,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tenant_bots" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"bot_name" varchar(100),
	"telegram_bot_token" varchar(255) NOT NULL,
	"webhook_domain" varchar(255),
	"webhook_path" varchar(255),
	"webhook_secret_token" varchar(255),
	"status" "status" DEFAULT 'STOPPED' NOT NULL,
	"last_status_update" timestamp with time zone DEFAULT now() NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tenant_localization" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"default_locale" varchar(10) NOT NULL,
	"supported_locales" jsonb NOT NULL,
	"timezone" varchar(64) NOT NULL,
	"currency_symbol" varchar(10) NOT NULL,
	"labels" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "tenant_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"key" varchar(255) NOT NULL,
	"value" jsonb,
	"type" "setting_type" NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "tenants" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_bot_roles" (
	"telegram_user_id" bigint NOT NULL,
	"bot_id" uuid NOT NULL,
	"role_id" uuid NOT NULL,
	CONSTRAINT "user_bot_roles_telegram_user_id_bot_id_role_id_pk" PRIMARY KEY("telegram_user_id","bot_id","role_id")
);
--> statement-breakpoint
CREATE TABLE "user_consent" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid,
	"consent_type" varchar(50) NOT NULL,
	"granted" boolean NOT NULL,
	"granted_at" timestamp DEFAULT now() NOT NULL,
	"revoked_at" timestamp,
	"deleted_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "user_identity" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"provider" "role" NOT NULL,
	"external_id" varchar(255) NOT NULL,
	"display" varchar(255) NOT NULL,
	"avatar_url" varchar(1024),
	"metadata" json DEFAULT '{}'::json NOT NULL,
	"linked_at" timestamp DEFAULT now() NOT NULL,
	"unlinked_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_kyc" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"provider" varchar(255) NOT NULL,
	"submitted_at" timestamp DEFAULT now() NOT NULL,
	"status" "status" NOT NULL,
	"document_type" varchar(255) NOT NULL,
	"document_number" varchar(255) NOT NULL,
	"country" varchar(100) NOT NULL,
	"expiry_date" date,
	"rejection_reason" varchar(1024),
	"metadata" json
);
--> statement-breakpoint
CREATE TABLE "user_onboarding" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid,
	"completed_steps" jsonb,
	"status" "status" NOT NULL,
	"completed_at" timestamp,
	"metadata" jsonb,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_profile_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"changed_by_id" uuid,
	"change_type" "change_type" NOT NULL,
	"old_value" json NOT NULL,
	"new_value" json NOT NULL,
	"changed_at" timestamp DEFAULT now() NOT NULL,
	"context" varchar(50) NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_tenant" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"tenant_id" uuid NOT NULL,
	"role" "role" NOT NULL,
	"status" "status" DEFAULT 'ACTIVE' NOT NULL,
	"is_primary" boolean DEFAULT false NOT NULL,
	"last_used" timestamp,
	"registered_at" timestamp DEFAULT now() NOT NULL,
	"invited_by_id" uuid,
	"metadata" json DEFAULT '{}'::json NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"phone" varchar(20) NOT NULL,
	"email" varchar(255),
	"external_id" varchar(255),
	"verified" boolean DEFAULT false NOT NULL,
	"registered_at" timestamp DEFAULT now() NOT NULL,
	"last_login_at" timestamp,
	"legal_name" varchar(255),
	"display_name" varchar(255),
	"date_of_birth" timestamp,
	"language" varchar(10),
	"avatar_url" varchar(1024),
	"communication_opt_in" json,
	"privacy_flags" json,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	"deleted_at" timestamp,
	"metadata" json,
	CONSTRAINT "user_phone_unique" UNIQUE("phone"),
	CONSTRAINT "user_email_unique" UNIQUE("email"),
	CONSTRAINT "user_external_id_unique" UNIQUE("external_id")
);
--> statement-breakpoint
CREATE TABLE "vehicle" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"license_plate" varchar(20) NOT NULL,
	"make" varchar(100) NOT NULL,
	"model" varchar(100) NOT NULL,
	"color" varchar(50),
	"year" integer,
	"registration_id" varchar(100),
	"status" "status" DEFAULT 'ACTIVE' NOT NULL,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "verification_event" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatbot_user_id" uuid NOT NULL,
	"method" "verification_method" NOT NULL,
	"status" "verification_status" NOT NULL,
	"reference" varchar(255),
	"initiated_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp,
	"expires_at" timestamp,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "wallet" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"currency" varchar(3) NOT NULL,
	"balance" numeric(10, 2) DEFAULT '0.0' NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "webhook_subscriber" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"url" varchar(255) NOT NULL,
	"events" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "bots" ADD CONSTRAINT "bots_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat" ADD CONSTRAINT "chat_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_config" ADD CONSTRAINT "chatbot_config_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_config" ADD CONSTRAINT "chatbot_config_provider_id_chatbot_provider_id_fk" FOREIGN KEY ("provider_id") REFERENCES "public"."chatbot_provider"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_event" ADD CONSTRAINT "chatbot_event_chatbot_instance_id_chatbot_instance_id_fk" FOREIGN KEY ("chatbot_instance_id") REFERENCES "public"."chatbot_instance"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_event" ADD CONSTRAINT "chatbot_event_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_event" ADD CONSTRAINT "chatbot_event_session_id_chatbot_session_id_fk" FOREIGN KEY ("session_id") REFERENCES "public"."chatbot_session"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_event" ADD CONSTRAINT "chatbot_event_message_id_chatbot_message_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."chatbot_message"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_instance" ADD CONSTRAINT "chatbot_instance_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_instance" ADD CONSTRAINT "chatbot_instance_provider_id_chatbot_provider_id_fk" FOREIGN KEY ("provider_id") REFERENCES "public"."chatbot_provider"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_message" ADD CONSTRAINT "chatbot_message_chatbot_session_id_chatbot_session_id_fk" FOREIGN KEY ("chatbot_session_id") REFERENCES "public"."chatbot_session"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_message" ADD CONSTRAINT "chatbot_message_chatbot_user_id_chatbot_users_id_fk" FOREIGN KEY ("chatbot_user_id") REFERENCES "public"."chatbot_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_message_with_geolocation" ADD CONSTRAINT "chatbot_message_with_geolocation_message_id_chatbot_message_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."chatbot_message"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_provider" ADD CONSTRAINT "chatbot_provider_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_session" ADD CONSTRAINT "chatbot_session_chatbot_user_id_chatbot_users_id_fk" FOREIGN KEY ("chatbot_user_id") REFERENCES "public"."chatbot_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_session" ADD CONSTRAINT "chatbot_session_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_users" ADD CONSTRAINT "chatbot_users_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot_users" ADD CONSTRAINT "chatbot_users_chatbot_instance_id_chatbot_instance_id_fk" FOREIGN KEY ("chatbot_instance_id") REFERENCES "public"."chatbot_instance"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chatbot" ADD CONSTRAINT "chatbot_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispatch_assignment" ADD CONSTRAINT "dispatch_assignment_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispatch_assignment" ADD CONSTRAINT "dispatch_assignment_ride_id_rides_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."rides"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "driver_vehicle" ADD CONSTRAINT "driver_vehicle_driver_id_user_id_fk" FOREIGN KEY ("driver_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "driver_vehicle" ADD CONSTRAINT "driver_vehicle_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invoice" ADD CONSTRAINT "invoice_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "map_provider" ADD CONSTRAINT "map_provider_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "message" ADD CONSTRAINT "message_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "message" ADD CONSTRAINT "message_chat_id_chat_id_fk" FOREIGN KEY ("chat_id") REFERENCES "public"."chat"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "message" ADD CONSTRAINT "message_from_user_id_users_id_fk" FOREIGN KEY ("from_user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "message" ADD CONSTRAINT "message_to_user_id_users_id_fk" FOREIGN KEY ("to_user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "multi_tenant_group" ADD CONSTRAINT "multi_tenant_group_parent_group_id_multi_tenant_group_id_fk" FOREIGN KEY ("parent_group_id") REFERENCES "public"."multi_tenant_group"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_extensions" ADD CONSTRAINT "operator_extensions_user_tenant_id_user_tenant_id_fk" FOREIGN KEY ("user_tenant_id") REFERENCES "public"."user_tenant"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_shift" ADD CONSTRAINT "operator_shift_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operators" ADD CONSTRAINT "operators_user_tenant_id_user_tenant_id_fk" FOREIGN KEY ("user_tenant_id") REFERENCES "public"."user_tenant"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment" ADD CONSTRAINT "payment_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment" ADD CONSTRAINT "payment_ride_id_rides_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."rides"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment" ADD CONSTRAINT "payment_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_ride_id_rides_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."rides"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promos" ADD CONSTRAINT "promos_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotion" ADD CONSTRAINT "promotion_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "ride_event" ADD CONSTRAINT "ride_event_ride_id_ride_order_id_fk" FOREIGN KEY ("ride_id") REFERENCES "public"."ride_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_passenger_id_user_id_fk" FOREIGN KEY ("passenger_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_driver_id_user_id_fk" FOREIGN KEY ("driver_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "rides" ADD CONSTRAINT "rides_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "roles" ADD CONSTRAINT "roles_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_billing_profile" ADD CONSTRAINT "tenant_billing_profile_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_bots" ADD CONSTRAINT "tenant_bots_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_localization" ADD CONSTRAINT "tenant_localization_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_settings" ADD CONSTRAINT "tenant_settings_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_bot_roles" ADD CONSTRAINT "user_bot_roles_bot_id_bots_id_fk" FOREIGN KEY ("bot_id") REFERENCES "public"."bots"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_bot_roles" ADD CONSTRAINT "user_bot_roles_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_consent" ADD CONSTRAINT "user_consent_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_consent" ADD CONSTRAINT "user_consent_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_identity" ADD CONSTRAINT "user_identity_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_kyc" ADD CONSTRAINT "user_kyc_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_onboarding" ADD CONSTRAINT "user_onboarding_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_onboarding" ADD CONSTRAINT "user_onboarding_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_profile_history" ADD CONSTRAINT "user_profile_history_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_profile_history" ADD CONSTRAINT "user_profile_history_changed_by_id_user_id_fk" FOREIGN KEY ("changed_by_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_tenant" ADD CONSTRAINT "user_tenant_invited_by_id_user_id_fk" FOREIGN KEY ("invited_by_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle" ADD CONSTRAINT "vehicle_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "verification_event" ADD CONSTRAINT "verification_event_chatbot_user_id_chatbot_users_id_fk" FOREIGN KEY ("chatbot_user_id") REFERENCES "public"."chatbot_users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "wallet" ADD CONSTRAINT "wallet_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "webhook_subscriber" ADD CONSTRAINT "webhook_subscriber_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "webhook_path_segment_idx" ON "bots" USING btree ("webhook_path_segment");--> statement-breakpoint
CREATE UNIQUE INDEX "chatbot_message_session_id_idx" ON "chatbot_message" USING btree ("chatbot_session_id");--> statement-breakpoint
CREATE UNIQUE INDEX "chatbot_message_user_id_idx" ON "chatbot_message" USING btree ("chatbot_user_id");--> statement-breakpoint
CREATE UNIQUE INDEX "chatbot_message_sent_at_idx" ON "chatbot_message" USING btree ("sent_at");--> statement-breakpoint
CREATE INDEX "chatbot_message_geolocation_idx" ON "chatbot_message_with_geolocation" USING gist ("location");--> statement-breakpoint
CREATE INDEX "chatbot_session_chatbot_user_id_idx" ON "chatbot_session" USING btree ("chatbot_user_id");--> statement-breakpoint
CREATE INDEX "chatbot_session_tenant_id_idx" ON "chatbot_session" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "chatbot_session_geolocation_idx" ON "chatbot_session" USING gist ("geolocation");--> statement-breakpoint
CREATE UNIQUE INDEX "chatbot_users_provider_user_id_chatbot_instance_id_index" ON "chatbot_users" USING btree ("provider_user_id","chatbot_instance_id") WHERE "chatbot_users"."provider_user_id" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "chatbot_users_user_id_chatbot_instance_id_index" ON "chatbot_users" USING btree ("user_id","chatbot_instance_id");--> statement-breakpoint
CREATE UNIQUE INDEX "name_unique_per_parent" ON "multi_tenant_group" USING btree ("parent_group_id","name");--> statement-breakpoint
CREATE UNIQUE INDEX "status_idx" ON "multi_tenant_group" USING btree ("status");--> statement-breakpoint
CREATE UNIQUE INDEX "promo_code_idx" ON "promos" USING btree ("code");--> statement-breakpoint
CREATE UNIQUE INDEX "roles_tenant_id_name_idx" ON "roles" USING btree ("tenant_id","name");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_billing_profile_tenant_id_idx" ON "tenant_billing_profile" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_billing_profile_tax_id_unique" ON "tenant_billing_profile" USING btree ("tax_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_billing_profile_default_per_tenant" ON "tenant_billing_profile" USING btree ("tenant_id","default_profile");--> statement-breakpoint
CREATE UNIQUE INDEX "telegram_bot_token_idx" ON "tenant_bots" USING btree ("telegram_bot_token");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_localization_tenant_id_index" ON "tenant_localization" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_settings_tenant_id_key_unique" ON "tenant_settings" USING btree ("tenant_id","key");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_settings_tenant_id_idx" ON "tenant_settings" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "tenant_settings_key_idx" ON "tenant_settings" USING btree ("key");--> statement-breakpoint
CREATE UNIQUE INDEX "user_consent_user_consent_type_idx" ON "user_consent" USING btree ("user_id","consent_type");--> statement-breakpoint
CREATE UNIQUE INDEX "user_consent_deleted_at_idx" ON "user_consent" USING btree ("deleted_at");