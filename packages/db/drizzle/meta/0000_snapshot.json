{"id": "32558340-65a3-4e6a-b99e-301d0015f0b8", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.area": {"name": "area", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "geom": {"name": "geom", "type": "geometry(point)", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": false}, "osm_tags": {"name": "osm_tags", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "actor_id": {"name": "actor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "actor_type": {"name": "actor_type", "type": "actor_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "target_table": {"name": "target_table", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "target_id": {"name": "target_id", "type": "uuid", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_log": {"name": "audit_log", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "actor_id": {"name": "actor_id", "type": "uuid", "primaryKey": false, "notNull": true}, "actor_type": {"name": "actor_type", "type": "actor_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "target_table": {"name": "target_table", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "target_id": {"name": "target_id", "type": "uuid", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bots": {"name": "bots", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "webhook_path_segment": {"name": "webhook_path_segment", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "bot_username": {"name": "bot_username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_enabled": {"name": "is_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"webhook_path_segment_idx": {"name": "webhook_path_segment_idx", "columns": [{"expression": "webhook_path_segment", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bots_tenant_id_tenants_id_fk": {"name": "bots_tenant_id_tenants_id_fk", "tableFrom": "bots", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"bots_webhook_path_segment_unique": {"name": "bots_webhook_path_segment_unique", "nullsNotDistinct": false, "columns": ["webhook_path_segment"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat": {"name": "chat", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_tenant_id_tenants_id_fk": {"name": "chat_tenant_id_tenants_id_fk", "tableFrom": "chat", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_config": {"name": "chatbot_config", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "uuid", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chatbot_config_tenant_id_tenants_id_fk": {"name": "chatbot_config_tenant_id_tenants_id_fk", "tableFrom": "chatbot_config", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_config_provider_id_chatbot_provider_id_fk": {"name": "chatbot_config_provider_id_chatbot_provider_id_fk", "tableFrom": "chatbot_config", "tableTo": "chatbot_provider", "columnsFrom": ["provider_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_event": {"name": "chatbot_event", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "chatbot_instance_id": {"name": "chatbot_instance_id", "type": "uuid", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "event_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": false}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": true}, "occurred_at": {"name": "occurred_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chatbot_event_chatbot_instance_id_chatbot_instance_id_fk": {"name": "chatbot_event_chatbot_instance_id_chatbot_instance_id_fk", "tableFrom": "chatbot_event", "tableTo": "chatbot_instance", "columnsFrom": ["chatbot_instance_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_event_user_id_users_id_fk": {"name": "chatbot_event_user_id_users_id_fk", "tableFrom": "chatbot_event", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_event_session_id_chatbot_session_id_fk": {"name": "chatbot_event_session_id_chatbot_session_id_fk", "tableFrom": "chatbot_event", "tableTo": "chatbot_session", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_event_message_id_chatbot_message_id_fk": {"name": "chatbot_event_message_id_chatbot_message_id_fk", "tableFrom": "chatbot_event", "tableTo": "chatbot_message", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_instance": {"name": "chatbot_instance", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "bot_username": {"name": "bot_username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "webhook_url": {"name": "webhook_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"chatbot_instance_tenant_id_tenants_id_fk": {"name": "chatbot_instance_tenant_id_tenants_id_fk", "tableFrom": "chatbot_instance", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_instance_provider_id_chatbot_provider_id_fk": {"name": "chatbot_instance_provider_id_chatbot_provider_id_fk", "tableFrom": "chatbot_instance", "tableTo": "chatbot_provider", "columnsFrom": ["provider_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_message": {"name": "chatbot_message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatbot_session_id": {"name": "chatbot_session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "chatbot_user_id": {"name": "chatbot_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "direction": {"name": "direction", "type": "message_direction", "typeSchema": "public", "primaryKey": false, "notNull": true}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "message_type": {"name": "message_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "phone_verified": {"name": "phone_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "verification_reference": {"name": "verification_reference", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "replied_to_id": {"name": "replied_to_id", "type": "uuid", "primaryKey": false, "notNull": false}, "error_flag": {"name": "error_flag", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "provider_message_id": {"name": "provider_message_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"chatbot_message_session_id_idx": {"name": "chatbot_message_session_id_idx", "columns": [{"expression": "chatbot_session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "chatbot_message_user_id_idx": {"name": "chatbot_message_user_id_idx", "columns": [{"expression": "chatbot_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "chatbot_message_sent_at_idx": {"name": "chatbot_message_sent_at_idx", "columns": [{"expression": "sent_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chatbot_message_chatbot_session_id_chatbot_session_id_fk": {"name": "chatbot_message_chatbot_session_id_chatbot_session_id_fk", "tableFrom": "chatbot_message", "tableTo": "chatbot_session", "columnsFrom": ["chatbot_session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_message_chatbot_user_id_chatbot_users_id_fk": {"name": "chatbot_message_chatbot_user_id_chatbot_users_id_fk", "tableFrom": "chatbot_message", "tableTo": "chatbot_users", "columnsFrom": ["chatbot_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_message_with_geolocation": {"name": "chatbot_message_with_geolocation", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "geometry(point)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chatbot_message_geolocation_idx": {"name": "chatbot_message_geolocation_idx", "columns": [{"expression": "location", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gist", "with": {}}}, "foreignKeys": {"chatbot_message_with_geolocation_message_id_chatbot_message_id_fk": {"name": "chatbot_message_with_geolocation_message_id_chatbot_message_id_fk", "tableFrom": "chatbot_message_with_geolocation", "tableTo": "chatbot_message", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_provider": {"name": "chatbot_provider", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chatbot_provider_tenant_id_tenants_id_fk": {"name": "chatbot_provider_tenant_id_tenants_id_fk", "tableFrom": "chatbot_provider", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_session": {"name": "chatbot_session", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatbot_user_id": {"name": "chatbot_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ended_at": {"name": "ended_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "verified": {"name": "verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "geolocation": {"name": "geolocation", "type": "geometry(point)", "primaryKey": false, "notNull": false}, "context": {"name": "context", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"chatbot_session_chatbot_user_id_idx": {"name": "chatbot_session_chatbot_user_id_idx", "columns": [{"expression": "chatbot_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_session_tenant_id_idx": {"name": "chatbot_session_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_session_geolocation_idx": {"name": "chatbot_session_geolocation_idx", "columns": [{"expression": "geolocation", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gist", "with": {}}}, "foreignKeys": {"chatbot_session_chatbot_user_id_chatbot_users_id_fk": {"name": "chatbot_session_chatbot_user_id_chatbot_users_id_fk", "tableFrom": "chatbot_session", "tableTo": "chatbot_users", "columnsFrom": ["chatbot_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_session_tenant_id_tenants_id_fk": {"name": "chatbot_session_tenant_id_tenants_id_fk", "tableFrom": "chatbot_session", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_users": {"name": "chatbot_users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "chatbot_instance_id": {"name": "chatbot_instance_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider_user_id": {"name": "provider_user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone_verified": {"name": "phone_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "verification_date": {"name": "verification_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "consent": {"name": "consent", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "consent_date": {"name": "consent_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "consent_revoked_at": {"name": "consent_revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "blocked": {"name": "blocked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "locale": {"name": "locale", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_seen_at": {"name": "last_seen_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"chatbot_users_provider_user_id_chatbot_instance_id_index": {"name": "chatbot_users_provider_user_id_chatbot_instance_id_index", "columns": [{"expression": "provider_user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "chatbot_instance_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"chatbot_users\".\"provider_user_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "chatbot_users_user_id_chatbot_instance_id_index": {"name": "chatbot_users_user_id_chatbot_instance_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "chatbot_instance_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chatbot_users_user_id_users_id_fk": {"name": "chatbot_users_user_id_users_id_fk", "tableFrom": "chatbot_users", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_users_chatbot_instance_id_chatbot_instance_id_fk": {"name": "chatbot_users_chatbot_instance_id_chatbot_instance_id_fk", "tableFrom": "chatbot_users", "tableTo": "chatbot_instance", "columnsFrom": ["chatbot_instance_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot": {"name": "chatbot", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "bot_id": {"name": "bot_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chatbot_tenant_id_tenants_id_fk": {"name": "chatbot_tenant_id_tenants_id_fk", "tableFrom": "chatbot", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.dispatch_assignment": {"name": "dispatch_assignment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "operator_id": {"name": "operator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"dispatch_assignment_operator_id_operators_id_fk": {"name": "dispatch_assignment_operator_id_operators_id_fk", "tableFrom": "dispatch_assignment", "tableTo": "operators", "columnsFrom": ["operator_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "dispatch_assignment_ride_id_rides_id_fk": {"name": "dispatch_assignment_ride_id_rides_id_fk", "tableFrom": "dispatch_assignment", "tableTo": "rides", "columnsFrom": ["ride_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.driver_vehicle": {"name": "driver_vehicle", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "driver_id": {"name": "driver_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "from_time": {"name": "from_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "to_time": {"name": "to_time", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"driver_vehicle_driver_id_user_id_fk": {"name": "driver_vehicle_driver_id_user_id_fk", "tableFrom": "driver_vehicle", "tableTo": "user", "columnsFrom": ["driver_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "driver_vehicle_vehicle_id_vehicle_id_fk": {"name": "driver_vehicle_vehicle_id_vehicle_id_fk", "tableFrom": "driver_vehicle", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.geodata": {"name": "geodata", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "geom": {"name": "geom", "type": "geometry(point)", "primaryKey": false, "notNull": true}, "latitude": {"name": "latitude", "type": "double precision", "primaryKey": false, "notNull": true}, "longitude": {"name": "longitude", "type": "double precision", "primaryKey": false, "notNull": true}, "source": {"name": "source", "type": "source", "typeSchema": "public", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "accuracy": {"name": "accuracy", "type": "double precision", "primaryKey": false, "notNull": false}, "area_id": {"name": "area_id", "type": "uuid", "primaryKey": false, "notNull": false}, "osm_tags": {"name": "osm_tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.i18n_translation": {"name": "i18n_translation", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invoice": {"name": "invoice", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "issued_at": {"name": "issued_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "paid_at": {"name": "paid_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"invoice_tenant_id_tenants_id_fk": {"name": "invoice_tenant_id_tenants_id_fk", "tableFrom": "invoice", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.map_provider": {"name": "map_provider", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider_name": {"name": "provider_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "config": {"name": "config", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"map_provider_tenant_id_tenants_id_fk": {"name": "map_provider_tenant_id_tenants_id_fk", "tableFrom": "map_provider", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.message": {"name": "message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "from_user_id": {"name": "from_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "to_user_id": {"name": "to_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "via_channel": {"name": "via_channel", "type": "provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "message_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "<PERSON><PERSON><PERSON>(4096)", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"message_tenant_id_tenants_id_fk": {"name": "message_tenant_id_tenants_id_fk", "tableFrom": "message", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "message_chat_id_chat_id_fk": {"name": "message_chat_id_chat_id_fk", "tableFrom": "message", "tableTo": "chat", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "message_from_user_id_users_id_fk": {"name": "message_from_user_id_users_id_fk", "tableFrom": "message", "tableTo": "users", "columnsFrom": ["from_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "message_to_user_id_users_id_fk": {"name": "message_to_user_id_users_id_fk", "tableFrom": "message", "tableTo": "users", "columnsFrom": ["to_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.multi_tenant_group": {"name": "multi_tenant_group", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "group_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "contact_email": {"name": "contact_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "contact_phone": {"name": "contact_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "parent_group_id": {"name": "parent_group_id", "type": "uuid", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "group_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"name_unique_per_parent": {"name": "name_unique_per_parent", "columns": [{"expression": "parent_group_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "status_idx": {"name": "status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"multi_tenant_group_parent_group_id_multi_tenant_group_id_fk": {"name": "multi_tenant_group_parent_group_id_multi_tenant_group_id_fk", "tableFrom": "multi_tenant_group", "tableTo": "multi_tenant_group", "columnsFrom": ["parent_group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.operator_extensions": {"name": "operator_extensions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_tenant_id": {"name": "user_tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "operator_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"operator_extensions_user_tenant_id_user_tenant_id_fk": {"name": "operator_extensions_user_tenant_id_user_tenant_id_fk", "tableFrom": "operator_extensions", "tableTo": "user_tenant", "columnsFrom": ["user_tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.operator_shift": {"name": "operator_shift", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "operator_id": {"name": "operator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "shift_start": {"name": "shift_start", "type": "timestamp", "primaryKey": false, "notNull": true}, "shift_end": {"name": "shift_end", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"operator_shift_operator_id_operators_id_fk": {"name": "operator_shift_operator_id_operators_id_fk", "tableFrom": "operator_shift", "tableTo": "operators", "columnsFrom": ["operator_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.operators": {"name": "operators", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_tenant_id": {"name": "user_tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "operator_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"operators_user_tenant_id_user_tenant_id_fk": {"name": "operators_user_tenant_id_user_tenant_id_fk", "tableFrom": "operators", "tableTo": "user_tenant", "columnsFrom": ["user_tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment": {"name": "payment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "payment_method", "typeSchema": "public", "primaryKey": false, "notNull": true}, "processor_ref": {"name": "processor_ref", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"payment_tenant_id_tenants_id_fk": {"name": "payment_tenant_id_tenants_id_fk", "tableFrom": "payment", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payment_ride_id_rides_id_fk": {"name": "payment_ride_id_rides_id_fk", "tableFrom": "payment", "tableTo": "rides", "columnsFrom": ["ride_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payment_user_id_user_id_fk": {"name": "payment_user_id_user_id_fk", "tableFrom": "payment", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pbx_call": {"name": "pbx_call", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "operator_id": {"name": "operator_id", "type": "uuid", "primaryKey": false, "notNull": false}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": false}, "direction": {"name": "direction", "type": "direction", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "recording_url": {"name": "recording_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ended_at": {"name": "ended_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"pbx_call_tenant_id_tenants_id_fk": {"name": "pbx_call_tenant_id_tenants_id_fk", "tableFrom": "pbx_call", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pbx_call_user_id_users_id_fk": {"name": "pbx_call_user_id_users_id_fk", "tableFrom": "pbx_call", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pbx_call_operator_id_operators_id_fk": {"name": "pbx_call_operator_id_operators_id_fk", "tableFrom": "pbx_call", "tableTo": "operators", "columnsFrom": ["operator_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pbx_call_ride_id_rides_id_fk": {"name": "pbx_call_ride_id_rides_id_fk", "tableFrom": "pbx_call", "tableTo": "rides", "columnsFrom": ["ride_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promos": {"name": "promos", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "discount_value": {"name": "discount_value", "type": "integer", "primaryKey": false, "notNull": true}, "discount_type": {"name": "discount_type", "type": "promo_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "max_uses": {"name": "max_uses", "type": "integer", "primaryKey": false, "notNull": false}, "uses": {"name": "uses", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "status": {"name": "status", "type": "promo_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"promo_code_idx": {"name": "promo_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"promos_tenant_id_tenants_id_fk": {"name": "promos_tenant_id_tenants_id_fk", "tableFrom": "promos", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promotion": {"name": "promotion", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "promotion_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "usage_limit": {"name": "usage_limit", "type": "integer", "primaryKey": false, "notNull": false}, "valid_from": {"name": "valid_from", "type": "timestamp", "primaryKey": false, "notNull": false}, "valid_to": {"name": "valid_to", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"promotion_tenant_id_tenants_id_fk": {"name": "promotion_tenant_id_tenants_id_fk", "tableFrom": "promotion", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ride_event": {"name": "ride_event", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ride_event_ride_id_ride_order_id_fk": {"name": "ride_event_ride_id_ride_order_id_fk", "tableFrom": "ride_event", "tableTo": "ride_order", "columnsFrom": ["ride_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ride_order": {"name": "ride_order", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "passenger_id": {"name": "passenger_id", "type": "uuid", "primaryKey": false, "notNull": false}, "driver_id": {"name": "driver_id", "type": "uuid", "primaryKey": false, "notNull": false}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": false}, "pickup_address": {"name": "pickup_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "pickup_latlng": {"name": "pickup_latlng", "type": "jsonb", "primaryKey": false, "notNull": false}, "dropoff_address": {"name": "dropoff_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "dropoff_latlng": {"name": "dropoff_latlng", "type": "jsonb", "primaryKey": false, "notNull": false}, "scheduled_time": {"name": "scheduled_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "confirmed_time": {"name": "confirmed_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'searching'"}, "order_type": {"name": "order_type", "type": "order_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ride'"}, "estimated_fare": {"name": "estimated_fare", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "paid": {"name": "paid", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "promo_id": {"name": "promo_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ride_ratings": {"name": "ride_ratings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": true}, "from_user_id": {"name": "from_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "to_user_id": {"name": "to_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ride_rating": {"name": "ride_rating", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": true}, "from_user_id": {"name": "from_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "to_user_id": {"name": "to_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rides": {"name": "rides", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "passenger_id": {"name": "passenger_id", "type": "uuid", "primaryKey": false, "notNull": true}, "driver_id": {"name": "driver_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'searching'"}, "order_type": {"name": "order_type", "type": "order_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ride'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"rides_passenger_id_user_id_fk": {"name": "rides_passenger_id_user_id_fk", "tableFrom": "rides", "tableTo": "user", "columnsFrom": ["passenger_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "rides_driver_id_user_id_fk": {"name": "rides_driver_id_user_id_fk", "tableFrom": "rides", "tableTo": "user", "columnsFrom": ["driver_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "rides_vehicle_id_vehicle_id_fk": {"name": "rides_vehicle_id_vehicle_id_fk", "tableFrom": "rides", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"roles_tenant_id_name_idx": {"name": "roles_tenant_id_name_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"roles_tenant_id_tenants_id_fk": {"name": "roles_tenant_id_tenants_id_fk", "tableFrom": "roles", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.support_tickets": {"name": "support_tickets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "support_ticket_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "assigned_to_id": {"name": "assigned_to_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "closed_at": {"name": "closed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.support_ticket": {"name": "support_ticket", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "support_ticket_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "assigned_to_id": {"name": "assigned_to_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "closed_at": {"name": "closed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.system_setting": {"name": "system_setting", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.telegram_chat_members": {"name": "telegram_chat_members", "schema": "", "columns": {"chat_id": {"name": "chat_id", "type": "bigint", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"telegram_chat_members_chat_id_user_id_pk": {"name": "telegram_chat_members_chat_id_user_id_pk", "columns": ["chat_id", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.telegram_chats": {"name": "telegram_chats", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "last_seen": {"name": "last_seen", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.telegram_messages": {"name": "telegram_messages", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "chat_id": {"name": "chat_id", "type": "bigint", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "message_type": {"name": "message_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>(4096)", "primaryKey": false, "notNull": false}, "file_id": {"name": "file_id", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "caption": {"name": "caption", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "reply_to_message_id": {"name": "reply_to_message_id", "type": "bigint", "primaryKey": false, "notNull": false}, "forward_from_user_id": {"name": "forward_from_user_id", "type": "integer", "primaryKey": false, "notNull": false}, "forward_from_chat_id": {"name": "forward_from_chat_id", "type": "bigint", "primaryKey": false, "notNull": false}, "edit_date": {"name": "edit_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "entities": {"name": "entities", "type": "json", "primaryKey": false, "notNull": false}, "command": {"name": "command", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "payload": {"name": "payload", "type": "json", "primaryKey": false, "notNull": false}, "service_type": {"name": "service_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "raw_data": {"name": "raw_data", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.telegram_users": {"name": "telegram_users", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.telegram_user_settings": {"name": "telegram_user_settings", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": true, "notNull": true}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(8)", "primaryKey": false, "notNull": false}, "notifications": {"name": "notifications", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenant_billing_profile": {"name": "tenant_billing_profile", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "billing_email": {"name": "billing_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "billing_phone": {"name": "billing_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "tax_id": {"name": "tax_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "payment_method": {"name": "payment_method", "type": "payment_method", "typeSchema": "public", "primaryKey": false, "notNull": true}, "default_profile": {"name": "default_profile", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"tenant_billing_profile_tenant_id_idx": {"name": "tenant_billing_profile_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "tenant_billing_profile_tax_id_unique": {"name": "tenant_billing_profile_tax_id_unique", "columns": [{"expression": "tax_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "tenant_billing_profile_default_per_tenant": {"name": "tenant_billing_profile_default_per_tenant", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "default_profile", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tenant_billing_profile_tenant_id_tenants_id_fk": {"name": "tenant_billing_profile_tenant_id_tenants_id_fk", "tableFrom": "tenant_billing_profile", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenant_bots": {"name": "tenant_bots", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "bot_name": {"name": "bot_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "telegram_bot_token": {"name": "telegram_bot_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "webhook_domain": {"name": "webhook_domain", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "webhook_path": {"name": "webhook_path", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "webhook_secret_token": {"name": "webhook_secret_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'STOPPED'"}, "last_status_update": {"name": "last_status_update", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"telegram_bot_token_idx": {"name": "telegram_bot_token_idx", "columns": [{"expression": "telegram_bot_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tenant_bots_tenant_id_tenants_id_fk": {"name": "tenant_bots_tenant_id_tenants_id_fk", "tableFrom": "tenant_bots", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenant_localization": {"name": "tenant_localization", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "default_locale": {"name": "default_locale", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "supported_locales": {"name": "supported_locales", "type": "jsonb", "primaryKey": false, "notNull": true}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "currency_symbol": {"name": "currency_symbol", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "labels": {"name": "labels", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"tenant_localization_tenant_id_index": {"name": "tenant_localization_tenant_id_index", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tenant_localization_tenant_id_tenants_id_fk": {"name": "tenant_localization_tenant_id_tenants_id_fk", "tableFrom": "tenant_localization", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenant_settings": {"name": "tenant_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "setting_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"tenant_settings_tenant_id_key_unique": {"name": "tenant_settings_tenant_id_key_unique", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "tenant_settings_tenant_id_idx": {"name": "tenant_settings_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "tenant_settings_key_idx": {"name": "tenant_settings_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tenant_settings_tenant_id_tenants_id_fk": {"name": "tenant_settings_tenant_id_tenants_id_fk", "tableFrom": "tenant_settings", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenants": {"name": "tenants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_bot_roles": {"name": "user_bot_roles", "schema": "", "columns": {"telegram_user_id": {"name": "telegram_user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "bot_id": {"name": "bot_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_bot_roles_bot_id_bots_id_fk": {"name": "user_bot_roles_bot_id_bots_id_fk", "tableFrom": "user_bot_roles", "tableTo": "bots", "columnsFrom": ["bot_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_bot_roles_role_id_roles_id_fk": {"name": "user_bot_roles_role_id_roles_id_fk", "tableFrom": "user_bot_roles", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_bot_roles_telegram_user_id_bot_id_role_id_pk": {"name": "user_bot_roles_telegram_user_id_bot_id_role_id_pk", "columns": ["telegram_user_id", "bot_id", "role_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_consent": {"name": "user_consent", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "consent_type": {"name": "consent_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "granted": {"name": "granted", "type": "boolean", "primaryKey": false, "notNull": true}, "granted_at": {"name": "granted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "revoked_at": {"name": "revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"user_consent_user_consent_type_idx": {"name": "user_consent_user_consent_type_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "consent_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_consent_deleted_at_idx": {"name": "user_consent_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_consent_user_id_users_id_fk": {"name": "user_consent_user_id_users_id_fk", "tableFrom": "user_consent", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_consent_tenant_id_tenants_id_fk": {"name": "user_consent_tenant_id_tenants_id_fk", "tableFrom": "user_consent", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_identity": {"name": "user_identity", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "external_id": {"name": "external_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "display": {"name": "display", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "avatar_url": {"name": "avatar_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": true, "default": "'{}'::json"}, "linked_at": {"name": "linked_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "unlinked_at": {"name": "unlinked_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_identity_user_id_user_id_fk": {"name": "user_identity_user_id_user_id_fk", "tableFrom": "user_identity", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_kyc": {"name": "user_kyc", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "document_type": {"name": "document_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "document_number": {"name": "document_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "expiry_date": {"name": "expiry_date", "type": "date", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_kyc_user_id_user_id_fk": {"name": "user_kyc_user_id_user_id_fk", "tableFrom": "user_kyc", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_onboarding": {"name": "user_onboarding", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "completed_steps": {"name": "completed_steps", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_onboarding_user_id_users_id_fk": {"name": "user_onboarding_user_id_users_id_fk", "tableFrom": "user_onboarding", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_onboarding_tenant_id_tenants_id_fk": {"name": "user_onboarding_tenant_id_tenants_id_fk", "tableFrom": "user_onboarding", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_profile_history": {"name": "user_profile_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "changed_by_id": {"name": "changed_by_id", "type": "uuid", "primaryKey": false, "notNull": false}, "change_type": {"name": "change_type", "type": "change_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "old_value": {"name": "old_value", "type": "json", "primaryKey": false, "notNull": true}, "new_value": {"name": "new_value", "type": "json", "primaryKey": false, "notNull": true}, "changed_at": {"name": "changed_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "context": {"name": "context", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_profile_history_user_id_user_id_fk": {"name": "user_profile_history_user_id_user_id_fk", "tableFrom": "user_profile_history", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_profile_history_changed_by_id_user_id_fk": {"name": "user_profile_history_changed_by_id_user_id_fk", "tableFrom": "user_profile_history", "tableTo": "user", "columnsFrom": ["changed_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_tenant": {"name": "user_tenant", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "registered_at": {"name": "registered_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "invited_by_id": {"name": "invited_by_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": true, "default": "'{}'::json"}}, "indexes": {}, "foreignKeys": {"user_tenant_user_id_user_id_fk": {"name": "user_tenant_user_id_user_id_fk", "tableFrom": "user_tenant", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_tenant_tenant_id_tenants_id_fk": {"name": "user_tenant_tenant_id_tenants_id_fk", "tableFrom": "user_tenant", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_tenant_invited_by_id_user_id_fk": {"name": "user_tenant_invited_by_id_user_id_fk", "tableFrom": "user_tenant", "tableTo": "user", "columnsFrom": ["invited_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "external_id": {"name": "external_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "verified": {"name": "verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "registered_at": {"name": "registered_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "legal_name": {"name": "legal_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "communication_opt_in": {"name": "communication_opt_in", "type": "json", "primaryKey": false, "notNull": false}, "privacy_flags": {"name": "privacy_flags", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_phone_unique": {"name": "user_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}, "user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "user_external_id_unique": {"name": "user_external_id_unique", "nullsNotDistinct": false, "columns": ["external_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle": {"name": "vehicle", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "license_plate": {"name": "license_plate", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "make": {"name": "make", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": false}, "registration_id": {"name": "registration_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"vehicle_tenant_id_tenants_id_fk": {"name": "vehicle_tenant_id_tenants_id_fk", "tableFrom": "vehicle", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification_event": {"name": "verification_event", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatbot_user_id": {"name": "chatbot_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "verification_method", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "verification_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "reference": {"name": "reference", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "initiated_at": {"name": "initiated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"verification_event_chatbot_user_id_chatbot_users_id_fk": {"name": "verification_event_chatbot_user_id_chatbot_users_id_fk", "tableFrom": "verification_event", "tableTo": "chatbot_users", "columnsFrom": ["chatbot_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.wallet": {"name": "wallet", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true}, "balance": {"name": "balance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0.0'"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"wallet_user_id_user_id_fk": {"name": "wallet_user_id_user_id_fk", "tableFrom": "wallet", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_subscriber": {"name": "webhook_subscriber", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "events": {"name": "events", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"webhook_subscriber_tenant_id_tenants_id_fk": {"name": "webhook_subscriber_tenant_id_tenants_id_fk", "tableFrom": "webhook_subscriber", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.message_direction": {"name": "message_direction", "schema": "public", "values": ["in", "out"]}, "public.promotion_type": {"name": "promotion_type", "schema": "public", "values": ["percentage", "value", "free_ride"]}, "public.status": {"name": "status", "schema": "public", "values": ["ACTIVE", "DISABLED", "DECOMMISSIONED", "REPAIR"]}, "public.verification_method": {"name": "verification_method", "schema": "public", "values": ["sms", "email", "phone_call", "other"]}, "public.verification_status": {"name": "verification_status", "schema": "public", "values": ["initiated", "code_sent", "verified", "failed", "expired"]}, "public.actor_type": {"name": "actor_type", "schema": "public", "values": ["user", "operator", "system"]}, "public.direction": {"name": "direction", "schema": "public", "values": ["inbound", "outbound"]}, "public.event_type": {"name": "event_type", "schema": "public", "values": ["message_received", "message_sent", "user_linked", "user_unlinked", "consent_granted", "consent_revoked", "verification_requested", "verification_succeeded", "verification_failed", "session_started", "session_ended", "error", "webhook_received"]}, "public.group_status": {"name": "group_status", "schema": "public", "values": ["ACTIVE", "PENDING", "INACTIVE"]}, "public.group_type": {"name": "group_type", "schema": "public", "values": ["FRANCHISE", "AGGREGATOR", "BRAND", "CORPORATE"]}, "public.message_type": {"name": "message_type", "schema": "public", "values": ["text", "image", "voice", "location", "verification", "document", "sticker", "button", "template", "interactive", "carousel", "group_chat", "unknown"]}, "public.operator_status": {"name": "operator_status", "schema": "public", "values": ["ACTIVE", "INACTIVE", "SUSPENDED", "PENDING"]}, "public.payment_method": {"name": "payment_method", "schema": "public", "values": ["CARD", "WIRE", "CRYPTO", "INVOICE"]}, "public.change_type": {"name": "change_type", "schema": "public", "values": ["onboarding", "phone_change", "admin_update"]}, "public.promo_status": {"name": "promo_status", "schema": "public", "values": ["ACTIVE", "INACTIVE", "EXPIRED", "LIMIT_REACHED"]}, "public.promo_type": {"name": "promo_type", "schema": "public", "values": ["PERCENTAGE", "FIXED", "FREE_RIDE"]}, "public.provider": {"name": "provider", "schema": "public", "values": ["telegram", "viber", "facebook", "google", "apple", "phone"]}, "public.ride_rating": {"name": "ride_rating", "schema": "public", "values": ["ONE", "TWO", "THREE", "FOUR", "FIVE"]}, "public.order_type": {"name": "order_type", "schema": "public", "values": ["ride", "delivery", "pooled", "shared"]}, "public.setting_type": {"name": "setting_type", "schema": "public", "values": ["SYSTEM", "PAYMENT", "I18N", "UI"]}, "public.source": {"name": "source", "schema": "public", "values": ["telegram", "android", "mqtt", "web", "manual"]}, "public.support_ticket_status": {"name": "support_ticket_status", "schema": "public", "values": ["open", "closed", "pending"]}, "public.plan": {"name": "plan", "schema": "public", "values": ["FREE", "PRO", "ENTERPRISE"]}, "public.role": {"name": "role", "schema": "public", "values": ["PASSENGER", "DRIVER", "OPERATOR", "MANAGER", "ADMIN", "SUPPORT"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}