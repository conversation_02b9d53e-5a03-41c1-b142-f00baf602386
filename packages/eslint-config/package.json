{"name": "@repo/eslint-config", "version": "0.1.0", "private": true, "main": "library.js", "files": ["library.js", "next.js", "react-internal.js"], "scripts": {"lint": "bunx eslint .", "typecheck": "bunx tsc --noEmit -p tsconfig.json"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-solid": "^0.14.0", "typescript": "^5.x"}}