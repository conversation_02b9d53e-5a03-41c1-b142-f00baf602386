**Step-by-Step Plan for Multi-Tenant Foundation (Phase 1)**

**Pre-requisites:**

*   Drizzle ORM setup with PostgreSQL.
*   Basic NestJS project structure.
*   GramIO installed.

---



Here's how to create schema and enum files in a Drizzle/TypeScript project based on the provided examples:

---



**Step 1: Database Schema Design (Drizzle ORM)**

*   Define schemas for `tenants`, `bots` (with encrypted tokens, webhook path segments), `roles`, `user_bot_roles`.
*   Ensure foreign keys and necessary indexes.

**`src/db/schema.ts` (Conceptual Drizzle Schema):**
```typescript
import { pgTable, uuid, text, varchar, boolean, timestamp, jsonb, bigint, primaryKey } from 'drizzle-orm/pg-core';

export const tenants = pgTable('tenants', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  // other tenant-specific fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const bots = pgTable('bots', {
  id: uuid('id').defaultRandom().primaryKey(),
  tenantId: uuid('tenant_id').references(() => tenants.id, { onDelete: 'cascade' }).notNull(),
  token: text('token').notNull(), // Store encrypted
  webhookPathSegment: varchar('webhook_path_segment', { length: 100 }).unique().notNull(),
  botUsername: varchar('bot_username', { length: 255 }),
  isEnabled: boolean('is_enabled').default(true).notNull(),
  config: jsonb('config'), // For bot-specific settings
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const roles = pgTable('roles', {
    id: uuid('id').defaultRandom().primaryKey(),
    tenantId: uuid('tenant_id').references(() => tenants.id, { onDelete: 'cascade' }).notNull(),
    name: varchar('name', { length: 50 }).notNull(), // e.g., 'admin', 'member'
    permissions: jsonb('permissions'), // e.g., ['read_data', 'write_data']
    // Unique constraint for role name per tenant
}, (table) => ({
    unq_tenant_role_name: primaryKey({columns: [table.tenantId, table.name]}) // Or unique index
}));

export const userBotRoles = pgTable('user_bot_roles', {
    telegramUserId: bigint('telegram_user_id', {mode: 'number'}).notNull(), // Telegram user ID
    botId: uuid('bot_id').references(() => bots.id, { onDelete: 'cascade' }).notNull(),
    roleId: uuid('role_id').references(() => roles.id, { onDelete: 'cascade' }).notNull(),
    // Primary key to ensure a user has a role only once per bot
}, (table) => ({
    pk_user_bot_role: primaryKey({columns: [table.telegramUserId, table.botId, table.roleId]})
}));

// Other application-specific tables should include tenantId or botId for data scoping
// export const someData = pgTable('some_data', {
//   id: uuid('id').defaultRandom().primaryKey(),
//   botId: uuid('bot_id').references(() => bots.id).notNull(),
//   content: text('content'),
// });
```
*   Generate migrations and apply them: `npx drizzle-kit generate:pg` and `npx drizzle-kit push:pg`.

---

**Step 2: Define Core Types/Interfaces (DTOs can come later for API layers)**

**`src/types/database.ts` (Example):**
```typescript
export interface BotConfigFromDB {
  id: string; // Corresponds to bots.id (UUID)
  tenantId: string; // Corresponds to tenants.id (UUID)
  token: string; // Decrypted token
  webhookPathSegment: string;
  botUsername?: string | null;
  isEnabled: boolean;
  config?: any; // Parsed JSONB from bots.config
}

export interface TenantFromDB {
    id: string;
    name: string;
    // ... other fields
}
```

**`src/types/bot-context.ts` (Update as discussed previously):**
```typescript
import { Context as GramioContext, Bot as GramioBot, MessageContext as GramioMessageContext /* etc. */ } from 'gramio';
import type { BotConfigFromDB, TenantFromDB } from './database';
import type { i18n as I18nInstance } from '../shared/locales'; // Assuming i18n type

// 1. App-specific Extensions (like i18n)
export interface AppContextExtensions {
  readonly t: ReturnType<typeof I18nInstance['buildT']>;
}

// 2. Tenant and Bot Info for Context
export interface TenantInfo extends Pick<TenantFromDB, 'id' | 'name'> {
  // Add other pre-loaded tenant data if needed
}
export interface BotInstanceInfo extends Pick<BotConfigFromDB, 'id' | 'botUsername'> {
  // Add other pre-loaded bot config data if needed
}

// 3. Tenant Context Extension (the core of multi-tenancy in context)
export interface TenantContextExtension {
  tenant?: TenantInfo;
  botInfo?: BotInstanceInfo;
  userRoles?: string[]; // Example for roles
  tenantBotApiClient?: GramioBot['client']; // The API client for the specific tenant's bot
}

// 4. Error Definitions for GramIO Bot generic
export type AppBotErrorDefinitions = { /* ... as defined before ... */ };

// 5. The base AppBot type (using GramIO's Bot)
// The second generic is the fully augmented context type handlers will receive.
export type AppBotBase = GramioBot<AppBotErrorDefinitions, AppBaseContext>;

// 6. The fully augmented Base Context Type (used by the universal processor)
// This type is what `universalGramioProcessor.derive` will produce and handlers will consume.
export type AppBaseContext = GramioContext<AppBotBase> & AppContextExtensions & TenantContextExtension;

// 7. Specific Context Types (extending AppBaseContext)
// Example for MessageContext:
export type AppMessageContext = GramioMessageContext<AppBotBase> & AppContextExtensions & TenantContextExtension & {
  // Add other message-specific extensions if any (like mediaGroup from plugin)
  scene?: any; // From scenes plugin, adjust type
  mediaGroup?: AppMessageContext[];
  mediaGroupId?: string;
};

export type AppCommandContext = AppMessageContext & { // Commands are usually messages
  args: string | null;
};

export type AppCallbackQueryContext = GramioCallbackQueryContext<AppBotBase> & AppContextExtensions & TenantContextExtension & {
    scene?: any; // From scenes plugin
    // queryData from CallbackData helper if used
};

// ... define other specific context types (InlineQuery, ChosenInlineResult, etc.) extending AppBaseContext
```
*   **Note:** `AppBotBase` is the type for the GramIO `Bot` instance. `AppBaseContext` is the fully augmented context type. Your handlers will be typed with specific contexts like `AppMessageContext`, `AppCommandContext`, etc., which all inherit these core extensions.

---

**Step 3: NestJS Modules (Drizzle, Config, Redis)**

*   Ensure you have `DrizzleModule` (providing `DrizzleService`), `ConfigModule` (providing `AppConfigService`), and `RedisModule` (providing `RedisService`). They should export their respective services.

---

**Step 4: `BotProcessingService` (The Core Multi-Tenant Logic)**

*   This service will manage the single `universalGramioProcessor` and the `tenantBotClients`.
*   Implement `onModuleInit`, `loadActiveBotConfigsAndInitializeClients`, and `processUpdateForSegment`.

**`src/gramiobot/bot-processing.service.ts` (Structure from previous detailed example):**
```typescript
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Bot as GramioBotClass, Context as GramioContext, TelegramUpdate } from 'gramio';
import { DrizzleService } from '../db/drizzle.service';
import { bots as botsTable, tenants as tenantsTable } from '../db/schema'; // Import schema
import { eq } from 'drizzle-orm';
import { AppConfigService } from '../config/app-config.service';
import { RedisService } from '../services/redis.service';
import { redisStorage } from '@gramio/storage-redis';
import { scenes } from '@gramio/scenes'; // Import Scene if you use it
import { i18n } from '../shared/locales'; // Your i18n instance

import type {
  AppBotErrorDefinitions,
  AppContextExtensions,
  TenantContextExtension,
  TenantInfo,
  BotInstanceInfo,
  AppBaseContext // Use this for the universal processor's context type
} from '../types/bot-context';
import type { BotConfigFromDB } from '../types/database';

// Handler Services
import { CommandHandlersService } from '../commands/command-handlers.service';
import { CallbackQueryHandlersService } from '../commands/callback-query-handlers.service';
// ... import all other handler services (Start, InlineQuery, Reaction, ChosenInline, SceneCommand)

@Injectable()
export class BotProcessingService implements OnModuleInit {
  private readonly logger = new Logger(BotProcessingService.name);
  // Universal processor uses AppBaseContext as its augmented context type
  public universalGramioProcessor!: GramioBotClass<AppBotErrorDefinitions, AppBaseContext>;
  private tenantBotClients = new Map<string, GramioBotClass['client']>();
  private activeBotConfigs = new Map<string, BotConfigFromDB>(); // webhookPathSegment -> BotConfig

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly appConfigService: AppConfigService,
    private readonly redisService: RedisService,
    // Inject ALL handler services
    private readonly commandHandlers: CommandHandlersService,
    private readonly callbackQueryHandlers: CallbackQueryHandlersService,
    private readonly startCommandHandlers: StartCommandHandlersService,
    private readonly inlineQueryHandlers: InlineQueryHandlersService,
    private readonly reactionHandlers: ReactionHandlersService,
    private readonly chosenInlineResultHandlers: ChosenInlineResultHandlersService,
    private readonly sceneCommandHandlers: SceneCommandHandlersService,
  ) {}

  async onModuleInit() {
    await this.loadActiveBotConfigsAndInitializeClients();

    const placeholderToken = this.appConfigService.primaryBotToken || 'INVALID_PLACEHOLDER_TOKEN';
    const storage = redisStorage(this.redisService.client);

    this.universalGramioProcessor = new GramioBotClass<AppBotErrorDefinitions, AppBaseContext>(placeholderToken)
      .extend(scenes([], { storage })) // Initialize scenes; actual scenes might be context-dependent
      // Add other global GramIO plugins (autoRetry, mediaGroup, etc.)
      .derive(async (ctx: GramioContext<any>) // ctx here is a base context from GramIO
        : Promise<Partial<AppContextExtensions & TenantContextExtension>> => { // Return type for derive
        
        const botConfigForThisUpdate = (ctx.update as any)._botConfig as BotConfigFromDB | undefined;
        const derivedProps: Partial<AppContextExtensions & TenantContextExtension> = {
            t: i18n.buildT(ctx.from?.languageCode) // Always provide i18n
        };

        if (!botConfigForThisUpdate) {
          this.logger.warn(`[derive] No botConfig in update for derivation. Update ID: ${ctx.updateId}`);
          return derivedProps; // Return with t, but no tenant client
        }

        const tenantApiClient = this.tenantBotClients.get(botConfigForThisUpdate.id);
        if (!tenantApiClient) {
          this.logger.error(`[derive] No API client for bot ID: ${botConfigForThisUpdate.id}.`);
          return derivedProps;
        }
        
        // Fetch tenant name (can be cached or joined when loading botConfig)
        const tenantRecord = await this.drizzle.db.query.tenants.findFirst({
            where: eq(tenantsTable.id, botConfigForThisUpdate.tenantId)
        });

        derivedProps.tenant = { id: botConfigForThisUpdate.tenantId, name: tenantRecord?.name || 'Unknown Tenant' };
        derivedProps.botInfo = { id: botConfigForThisUpdate.id, username: botConfigForThisUpdate.botUsername || undefined };
        derivedProps.tenantBotApiClient = tenantApiClient;
        
        // TODO: Fetch user roles for ctx.from.id and botConfigForThisUpdate.id
        // derivedProps.userRoles = await this.fetchUserRoles(ctx.from?.id, botConfigForThisUpdate.id);

        return derivedProps;
      })
      .onError((errCtx: AppBaseContext) => { // errCtx is now AppBaseContext
        this.logger.error(
          `GramIO Error (Tenant: ${errCtx.tenant?.id}, Bot: ${errCtx.botInfo?.id}, Update: ${errCtx.updateId}):`,
          errCtx.error?.message,
        );
      });

    // Initialize ALL handler services with the universalGramioProcessor
    this.commandHandlers.initialize(this.universalGramioProcessor);
    this.callbackQueryHandlers.initialize(this.universalGramioProcessor);
    this.startCommandHandlers.initialize(this.universalGramioProcessor);
    this.inlineQueryHandlers.initialize(this.universalGramioProcessor);
    this.reactionHandlers.initialize(this.universalGramioProcessor);
    this.chosenInlineResultHandlers.initialize(this.universalGramioProcessor);
    this.sceneCommandHandlers.initialize(this.universalGramioProcessor);
    this.logger.log('Handler services initialized with universal processor.');

    // Register handlers ONCE on the universalGramioProcessor
    this.commandHandlers.registerHandlers();
    this.callbackQueryHandlers.registerHandlers();
    // ... register all other handlers
    this.logger.log('All handlers registered on universal processor.');

    await this.universalGramioProcessor.start();
    this.logger.log('Universal GramIO bot processor started.');
  }

  async loadActiveBotConfigsAndInitializeClients() {
    this.logger.log('Loading active bot configurations and API clients...');
    const botsFromDB = await this.drizzle.db.query.bots.findMany({
        where: eq(botsTable.isEnabled, true),
        // with: { tenant: true } // If you have relations set up in Drizzle
    });
    this.activeBotConfigs.clear();
    this.tenantBotClients.clear();

    for (const botData of botsFromDB) {
      const botConfig = botData as BotConfigFromDB; // Cast
      // IMPORTANT: Decrypt botConfig.token from DB here!
      const decryptedToken = this.decryptToken(botConfig.token); // Implement decryptToken

      this.activeBotConfigs.set(botConfig.webhookPathSegment, { ...botConfig, token: decryptedToken });

      const tenantBotInstance = new GramioBotClass(decryptedToken);
      // Optional: await tenantBotInstance.init(); // If it fetches botUsername etc.
      // You might want to store tenantBotInstance.info if init() is called
      this.tenantBotClients.set(botConfig.id, tenantBotInstance.client);
      this.logger.log(`Initialized API client for bot ID: ${botConfig.id} (Segment: ${botConfig.webhookPathSegment})`);
    }
    this.logger.log(`Loaded ${this.activeBotConfigs.size} active bot configurations, ${this.tenantBotClients.size} API clients.`);
  }

  async processUpdateForSegment(webhookPathSegment: string, update: TelegramUpdate) {
    let botConfig = this.activeBotConfigs.get(webhookPathSegment);

    if (!botConfig) {
      this.logger.warn(`No cached config for segment: ${webhookPathSegment}. Reloading all configs.`);
      await this.loadActiveBotConfigsAndInitializeClients(); // Attempt a full reload
      botConfig = this.activeBotConfigs.get(webhookPathSegment);
      if (!botConfig) {
        throw new Error(`Bot configuration not found or not enabled for segment: ${webhookPathSegment} after reload.`);
      }
    }
    
    // Attach the resolved (and decrypted token) botConfig to the update object
    // so it can be picked up by the `derive` method.
    (update as any)._botConfig = botConfig;

    if (!this.universalGramioProcessor) {
        throw new Error("Universal GramIO processor not initialized.");
    }
    await this.universalGramioProcessor.processUpdate(update);
  }
  
  // Implement proper encryption/decryption for bot tokens
  private encryptToken(token: string): string { /* ... use crypto module ... */ return token; }
  private decryptToken(encryptedToken: string): string { /* ... use crypto module ... */ return encryptedToken; }

  // Placeholder for role fetching
  // private async fetchUserRoles(telegramUserId?: number, botId?: string): Promise<string[]> {
  //   if (!telegramUserId || !botId) return [];
  //   // Query user_bot_roles and roles table
  //   return ['member']; // Example
  // }
}
```
*   **Token Encryption:** Added placeholders for `encryptToken`/`decryptToken`. **This is critical for security.** Use Node.js `crypto` module (e.g., AES-256-GCM). The encryption key should be a secret managed by your application (e.g., via environment variables).
*   **Handler Initialization:** All handler services are injected into `BotProcessingService` and their `initialize(this.universalGramioProcessor)` and `registerHandlers()` methods are called once in `onModuleInit`.

---

**Step 5: NestJS Webhook Controller (`BotController.ts`)**

*   As defined previously, it takes `:webhookPathSegment`, finds the bot, and calls `botProcessingService.processUpdateForSegment`.

**`src/gramiobot/gramio.controller.ts`:**
```typescript
import { Controller, Post, Body, Param, Res, HttpStatus, Logger, Headers } from '@nestjs/common';
import { FastifyReply } from 'fastify'; // Or Express types
import { BotProcessingService } from './bot-processing.service';
import { AppConfigService } from '../config/app-config.service'; // For secret header validation

@Controller('webhook')
export class BotController {
  private readonly logger = new Logger(BotController.name);

  constructor(
    private readonly botProcessingService: BotProcessingService,
    private readonly appConfigService: AppConfigService, // If validating X-Telegram-Bot-Api-Secret-Token
  ) {}

  @Post(':webhookPathSegment')
  async handleUpdate(
    @Param('webhookPathSegment') webhookPathSegment: string,
    @Body() update: any, // Telegram Update object
    @Headers('x-telegram-bot-api-secret-token') secretTokenHeader?: string,
    @Res() reply: FastifyReply,
  ) {
    this.logger.debug(`Webhook received for segment: ${webhookPathSegment}`);

    // Optional: Validate X-Telegram-Bot-Api-Secret-Token
    // const expectedSecret = this.appConfigService.getBotSecretTokenForSegment(webhookPathSegment); // You'd need to store/manage this
    // if (expectedSecret && secretTokenHeader !== expectedSecret) {
    //   this.logger.warn(`Invalid secret token for segment ${webhookPathSegment}`);
    //   reply.status(HttpStatus.UNAUTHORIZED).send();
    //   return;
    // }

    try {
      await this.botProcessingService.processUpdateForSegment(webhookPathSegment, update);
      reply.status(HttpStatus.OK).send('OK');
    } catch (error) {
      this.logger.error(`Error processing update for ${webhookPathSegment}: ${error.message}`, error.stack);
      reply.status(HttpStatus.INTERNAL_SERVER_ERROR).send('Error');
    }
  }
}
```

---

**Step 6: Modify Handler Services**

*   **Constructor:** Remove `bot: AppBot` injection.
*   **`initialize` method:** Add `public initialize(processor: GramioBotClass<AppBotErrorDefinitions, AppBaseContext>): void { this.processorInstance = processor; }` (and a `private processorInstance` property).
*   **`registerHandlers` method:** Use `this.processorInstance.command(...)`, `this.processorInstance.on(...)`, etc.
*   **Handler methods (e.g., `handleStart(ctx: AppCommandContext)`):**
    *   Use `ctx.tenant`, `ctx.botInfo` for tenant/bot specific logic.
    *   **Crucially, use `await ctx.tenantBotApiClient.sendMessage(...)`, `await ctx.tenantBotApiClient.answerCallbackQuery(...)` etc., for ALL Telegram API calls.**
    *   Scope database queries using `ctx.tenant.id` or `ctx.botInfo.id`.

**Example `src/commands/command-handlers.service.ts`:**
```typescript
import { Injectable, Logger } from '@nestjs/common';
import type { Bot as GramioBotClass } from 'gramio'; // For processorInstance type
import type { AppCommandContext, AppBaseContext, AppBotErrorDefinitions } from '../types/bot-context'; // Your specific context types

@Injectable()
export class CommandHandlersService {
  private readonly logger = new Logger(CommandHandlersService.name);
  private processorInstance!: GramioBotClass<AppBotErrorDefinitions, AppBaseContext>;

  constructor() {} // No bot injection

  public initialize(processor: GramioBotClass<AppBotErrorDefinitions, AppBaseContext>): void {
    this.logger.log('Initializing CommandHandlersService with universal processor.');
    this.processorInstance = processor;
  }

  registerHandlers(): void {
    if (!this.processorInstance) {
      throw new Error('CommandHandlersService not initialized with processor instance!');
    }
    this.logger.log('Registering command handlers on universal processor.');
    this.processorInstance.command('start', this.handleStartCommand.bind(this));
    // ... other command registrations
  }

  private async handleStartCommand(ctx: AppCommandContext): Promise<void> { // ctx is now fully augmented
    if (!ctx.tenant || !ctx.botInfo || !ctx.tenantBotApiClient) {
      this.logger.error('Tenant/Bot context or API client missing in handleStartCommand!');
      // Potentially send a generic error if possible, or just log and return
      return;
    }

    this.logger.log(`[start] Tenant: ${ctx.tenant.id}, Bot: ${ctx.botInfo.id}, User: ${ctx.from?.id}`);
    const userName = ctx.from?.firstName || ctx.t('generic.user');
    const greetingMessage = ctx.t("greetings.welcome", userName);

    // Use the tenant-specific API client
    try {
      await ctx.tenantBotApiClient.sendMessage({
        chat_id: ctx.chatId,
        text: greetingMessage,
        // reply_markup: if you have a keyboard
      });
    } catch (error) {
        this.logger.error(`Failed to send start message for bot ${ctx.botInfo.id}:`, error);
    }
  }
  // ... other command handler methods using ctx.tenantBotApiClient
}
```
*   **Repeat this pattern for ALL handler services.**

---

**Step 7: Main `BotModule` (`gramiobot.module.ts`)**

*   Provide `BotProcessingService` and all handler services.
*   Import necessary modules like `DrizzleModule`, `ConfigModule`, `RedisModule`.

**`src/gramiobot/gramio.module.ts`:**
```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '../config/config.module';
import { RedisModule } from '../services/redis.module';
import { DrizzleModule } from '../db/drizzle.module'; // Your Drizzle module

import { BotController } from './gramio.controller';
import { BotProcessingService } from './bot-processing.service';

// Import ALL handler services
import { CommandHandlersService } from '../commands/command-handlers.service';
import { CallbackQueryHandlersService } from '../commands/callback-query-handlers.service';
import { StartCommandHandlersService } from '../commands/start-handlers.service';
import { InlineQueryHandlersService } from '../commands/inline-query-handlers.service';
import { ReactionHandlersService } from '../commands/reaction-handlers.service';
import { ChosenInlineResultHandlersService } from '../commands/chosen-inline-result-handlers.service';
import { SceneCommandHandlersService } from '../scenes/scenes-command-handlers.service';
// import { SceneRegistryService } from '../scenes/scene-registry.service'; // If you create one

@Module({
  imports: [
    ConfigModule,
    RedisModule,
    DrizzleModule,
    // ScenesModule, // If you have global scene definitions or a scene registry
  ],
  controllers: [BotController],
  providers: [
    BotProcessingService,
    // All Handler Services are providers here, injected into BotProcessingService
    CommandHandlersService,
    CallbackQueryHandlersService,
    StartCommandHandlersService,
    InlineQueryHandlersService,
    ReactionHandlersService,
    ChosenInlineResultHandlersService,
    SceneCommandHandlersService,
    // SceneRegistryService,
  ],
  // exports: [BotProcessingService], // If other modules need to interact with it
})
export class BotModule {}
```

---

**Step 8: (Later) DTOs and Strict Typing for API Layers**

*   When you build an admin API (e.g., for tenants to manage their bots, or for super-admins to manage tenants), you'll use NestJS DTOs with validation (e.g., `class-validator`, `class-transformer`).
    ```typescript
    // src/admin/dto/create-bot.dto.ts
    import { IsString, IsNotEmpty, IsUUID, IsOptional, IsBoolean, IsObject } from 'class-validator';
    export class CreateBotDto {
      @IsUUID()
      @IsNotEmpty()
      tenantId: string;

      @IsString()
      @IsNotEmpty()
      token: string; // Raw token, will be encrypted by service

      @IsString()
      @IsNotEmpty()
      webhookPathSegment: string;

      @IsOptional()
      @IsBoolean()
      isEnabled?: boolean;

      @IsOptional()
      @IsObject()
      config?: any;
    }
    ```
*   Your AdminController methods would use these DTOs:
    ```typescript
    // src/admin/admin.controller.ts
    // @Post('bots')
    // async createBot(@Body() createBotDto: CreateBotDto) {
    //   return this.adminService.createBot(createBotDto);
    // }
    ```
*   This makes your API layer robust and self-documenting (especially with Swagger). This is separate but complementary to the bot's internal multi-tenant logic.

---

This is a detailed plan. Take it step by step. The most complex parts are:
1.  Getting the `derive` logic in `BotProcessingService` to correctly identify the tenant and attach the right `tenantBotApiClient`.
2.  Modifying all your handler methods to consistently use `ctx.tenantBotApiClient` and scope data by `ctx.tenant.id` / `ctx.botInfo.id`.
3.  Implementing secure token encryption/decryption.

This setup provides a very scalable and maintainable multi-tenant bot architecture.
