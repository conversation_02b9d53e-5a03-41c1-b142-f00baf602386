# Tenancy & Organization Schema Deep Dive

## 1. Introduction

The Tenancy & Organization domain provides the foundational model for running a true multi-tenant taxi/delivery SaaS platform. It establishes the data boundaries, organizational hierarchy, and extensibility points enabling:
- Partitioned, isolated operation of many different companies ("tenants") on a single shared infrastructure.
- Franchise/group/brand logic for larger aggregators, holding companies, or platform models.
- Per-tenant configuration of business rules, appearance, billing, and localization.

*Boundaries*:  
This domain includes all schemas directly required for managing tenant identities and structure, their group affiliations, per-tenant settings, billing profiles, and tenant-centric localization and branding. It does **not** cover end users, rides, payments, vehicles, or zones—though those are always tenant-scoped via foreign keys.

---

## 2. Table Overviews & Column Rationale

### 2.1. `tenant`
| Column               | Type         | Constraints / Indexes                     | Rationale / Notes                                                          |
|----------------------|--------------|--------------------------------------------|----------------------------------------------------------------------------|
| id                   | UUID (PK)    | Primary, indexed                           | Global uniqueness, safe for partition/sharding.                            |
| name                 | String       | Unique                                    | Public-facing brand; uniqueness for UX/domain.                             |
| legal_name           | String       |                                            | For compliance, invoicing, contracts.                                      |
| email                | String       |                                            | Billing and platform/system contact.                                       |
| phone                | String       |                                            | Official support/verification.                                             |
| logo_url             | String       |                                            | Branding/white-label.                                                      |
| address              | String       |                                            | HQ/office address.                                                         |
| country              | String       |                                            | Locale, tax, legal.                                                        |
| timezone             | String       |                                            | Default for reporting, scheduling.                                         |
| plan                 | Enum         |                                            | Controls SaaS features and pricing.                                        |
| status               | Enum         | Indexed                                   | ACTIVE/SUSPENDED/CANCELLED, for operational logic.                         |
| multi_tenant_group   | UUID (FK, ⬚) | FK → multi_tenant_group                   | Optional, enables franchise/brand grouping.                                |
| created_at           | Timestamp    |                                            | Audit and compliance.                                                      |
| updated_at           | Timestamp    |                                            | Audit, track config changes.                                               |
| metadata             | JSONB        |                                            | Per-tenant extensibility; custom integration/settings structure.            |

⬚ = Nullable/optional

---

### 2.2. `multi_tenant_group`
| Column              | Type         | Constraints / Indexes                | Rationale / Notes                                                        |
|---------------------|--------------|--------------------------------------|--------------------------------------------------------------------------|
| id                  | UUID (PK)    | Primary                              | Unique global group identity.                                            |
| name                | String       | Unique per parent_group_id           | Aggregator/franchise/brand display name.                                 |
| type                | Enum         |                                      | Franchise, aggregator, brand, corporate, etc.                            |
| contact_email       | String       |                                      | Group-level support or billing.                                          |
| contact_phone       | String       |                                      |                                                                          |
| parent_group_id     | UUID ⬚       | FK → multi_tenant_group              | Enables nested group hierarchies.                                        |
| settings            | JSONB        |                                      | Group-level settings, overrides for sub-tenants.                         |
| status              | Enum         | Indexed                             | ACTIVE, PENDING, INACTIVE, etc.                                          |
| created_at          | Timestamp    |                                      |                                                                          |
| updated_at          | Timestamp    |                                      |                                                                          |
| metadata            | JSONB        |                                      | For integrations, custom logic, future-proofing the group model.         |

⬚ = Nullable/optional

---

### 2.3. `tenant_settings`
| Column              | Type         | Constraints / Indexes                | Rationale / Notes                                                        |
|---------------------|--------------|--------------------------------------|--------------------------------------------------------------------------|
| id                  | UUID (PK)    | Primary                              | Standard practice.                                                       |
| tenant_id           | UUID (FK)    | Unique+Indexed (if 1:1), or FK index | Link to the tenant.                                                      |
| key                 | String       | Indexed                              | Setting identifier (e.g., "billing.mode", "default_locale").             |
| value               | JSONB        |                                      | Stores arbitrary or structured setting value.                            |
| type                | Enum         |                                      | Classifies setting: SYSTEM, PAYMENT, I18N, UI, etc.                      |
| created_at          | Timestamp    |                                      |                                                                          |
| updated_at          | Timestamp    |                                      |                                                                          |

*Pattern can be "K/V per row" or, for dense config, all settings packed as JSONB per tenant.*

---

### 2.4. `tenant_billing_profile`
| Column              | Type         | Constraints / Indexes                | Rationale / Notes                                                           |
|---------------------|--------------|--------------------------------------|-----------------------------------------------------------------------------|
| id                  | UUID (PK)    | Primary                              |                                                                             |
| tenant_id           | UUID (FK)    | Indexed                              | Many profiles per tenant possible (HQ & branches).                          |
| name                | String       |                                      | Unique label for the profile.                                               |
| billing_email       | String       |                                      | Invoice delivery/alerts.                                                    |
| billing_phone       | String       |                                      |                                                                             |
| tax_id              | String       | Indexed/unique (if regulation)       | For VAT or fiscal compliance.                                               |
| address             | String       |                                      |                                                                            |
| country             | String       |                                      |                                                                            |
| currency            | String       |                                      | Default for financial ops, reporting.                                       |
| payment_method      | Enum         |                                      | Preferred method (card, wire, crypto, etc).                                 |
| default_profile     | Boolean      | Indexed                              | Only one per tenant (constraint).                                           |
| status              | Enum         |                                      | ACTIVE/INACTIVE/DEPRECATED, supports compliance/archival logic.             |
| metadata            | JSONB        |                                      | To support national settings, integration secrets, or attachment links.     |
| created_at          | Timestamp    |                                      |                                                                            |
| updated_at          | Timestamp    |                                      |                                                                            |

---

### 2.5. `tenant_localization`
| Column              | Type         | Constraints / Indexes                | Rationale / Notes                                                           |
|---------------------|--------------|--------------------------------------|-----------------------------------------------------------------------------|
| id                  | UUID (PK)    | Primary                              |                                                                             |
| tenant_id           | UUID (FK)    | Unique/indexed                       | One (or more) per tenant; allows regional branches.                         |
| default_locale      | String       |                                      | ISO code ("en", "mk", "sq", etc.)                                           |
| supported_locales   | String[] or JSONB |                                  | Enumerates tenant's available languages.                                    |
| timezone            | String       |                                      | For local UX.                                                               |
| currency_symbol     | String       |                                      | Brand/region-specific.                                                      |
| labels              | JSONB        |                                      | Custom label/branding KV pairs for UI, chatbot, receipt messages, etc.      |
| created_at          | Timestamp    |                                      |                                                                            |
| updated_at          | Timestamp    |                                      |                                                                            |
| metadata            | JSONB        |                                      | i18n extensions: links to per-tenant help, legal texts, etc.                |

---

## 3. Relationships Diagram

```mermaid
erDiagram
    multi_tenant_group ||--|{ tenant : "owns"
    tenant ||--o{ tenant_settings : "has settings"
    tenant ||--o{ tenant_billing_profile : "has billing profiles"
    tenant ||--|| tenant_localization : "localization config"
    multi_tenant_group ||--o| multi_tenant_group : "parent"
```

---

## 4. Multi-Tenancy Enforcement & Isolation

- **tenant_id** is present on all actionable (user, vehicle, ride, payment, etc.) tables, acting as the ultimate partition key. All queries and service logic are strictly tenant-scoped.
- **multi_tenant_group** supports franchises/brand aggregators, which own multiple tenants, but tenants are always isolated at the data and resource (user, vehicle, ride) level.
- **Postgres Row-Level Security (RLS):** For enterprise deployments, Row Security Policies can enforce tenant boundaries at the DB level.
- **No cross-tenant leakage:** All business logic, API, and batch jobs must check/validate tenant_id; group-level settings are never allowed to leak data between tenants, except by explicit admin relationship.
- **Index enforcement:** Indexes on tenant_id, status, keys/roles, and on all foreign keys, compound indices when required for uniqueness or efficient lookup.

---

## 5. Extensibility & Drizzle/Postgres Best Practices

- **UUIDs everywhere:** Primary and foreign keys use UUIDs—partition-tolerant, globally unique.
- **Partitioning:** For very large deployments, partition tables by tenant_id.
- **ENUM usage:** Status/type columns use Postgres enums for strictness, with careful migration planning.
- **Extensibility via JSONB:** metadata fields allow per-tenant or per-group custom fields, configuration, and experimentation with minimal friction; encourage strong validation at the application layer for these.
- **Localization/i18n:** Use tenant_localization for default locale, supported languages, branded UI labels; Postgres joins can enrich UX context for all tenant-generated UIs/emails/messages.
- **Audit/compliance:** created_at/updated_at required on all mutable entities for traceability.
- **Constraints & Validation:** Application layer enforces unique/singleton constraints (e.g., only one default billing profile, unique key/value per tenant setting) using DB constraints and application checks.

---

## 6. Table & Column Listing Reference

Each table above includes:
- Column name
- Type (Postgres logical, not Drizzle code type)
- Optionality and indexes/constraints
- Rationale for inclusion; extensibility strategies

---

## 7. Summary

This schema ensures robust tenant isolation and extensibility for any taxi/delivery SaaS operating at scale. It accommodates simple companies, sophisticated brands, and international franchises, using strong relational design and extensibility via JSONB and modular tables for group, billing, settings, and localization.

---