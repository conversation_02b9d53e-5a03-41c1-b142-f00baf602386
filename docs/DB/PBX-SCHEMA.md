# PBX Integration Schema  
**Multi-Tenant Taxi/Delivery Platform: Asterisk/FreePBX Domain Blueprint**

---

## Introduction

This document thoroughly defines the PBX/telephony/integration domain for a multi-tenant taxi/delivery platform. It supports both per-tenant (“bring your own” on-prem/cloud PBX) and shared platform-wide Asterisk/FreePBX deployments. The schema enables structured management of SIP trunks, DIDs, dispatch centers, operator assignment, and full event auditing via AMI, ARI, and FreePBX GraphQL API. It aligns real-time telephony (calls, SMS, bridges) with ride/order flows, role management, privacy/bridging, and advanced reporting.

---

## PBX Architecture & Tenant Mapping

- **Tenant PBX Topologies:**  
  1. Per-tenant: tenant operates its own PBX or SIP trunk, manages call flows locally or via the cloud.
  2. Shared platform: multiple tenants leverage (partitioned) cloud PBX, with virtual trunks/DIDs, isolated by tenant_id.
- All trunks, numbers (DIDs), and operator dispatch centers map to a tenant.
- AMI/ARI/GraphQL APIs enable live monitoring, order creation, bridge control, CDR/sms flow parsing, operator dashboards, and advanced privacy masking.

---

## Schema: Core PBX/Telecom Domain Tables

### 1. pbx_trunk

*Tenant SIP trunk (inbound/outbound definition)*

| Column        | Type      | Description                          |
|---------------|-----------|--------------------------------------|
| id            | UUID      | PK                                   |
| tenant_id     | UUID      | FK to tenant                         |
| provider      | String    | SIP provider/carrier                 |
| name          | String    | Human friendly                       |
| trunk_type    | Enum      | sip/pjsip/iax2/etc.                  |
| region        | String    | Country/region                       |
| state         | Enum      | active/suspended/disabled            |
| endpoint      | String    | SIP URI or connection string         |
| direction     | Enum      | inbound/outbound/both                |
| failover      | JSONB     | failover endpoints/routes            |
| config        | JSONB     | Protocol/FreePBX/Asterisk specifics  |
| created_at    | Timestamp | Registered in platform               |
| updated_at    | Timestamp |                                      |

---

### 2. pbx_did

*Per-tenant allocated phone number & metadata*

| Column        | Type      | Description                           |
|---------------|-----------|---------------------------------------|
| id            | UUID      | PK                                    |
| trunk_id      | UUID      | FK to pbx_trunk                       |
| tenant_id     | UUID      | Redundant FK                          |
| number        | String    | E.164 format, unique                  |
| label         | String    | Admin note                            |
| type          | Enum      | landline/mobile/tollfree/etc.         |
| routing       | JSONB     | FreePBX/Asterisk routing Ctrl         |
| status        | Enum      | enabled/disabled/inbound-only         |
| assigned_at   | Timestamp | Allocated to tenant                   |
| released_at   | Timestamp | On deallocation                       |

---

### 3. pbx_center

*Dispatch center: mapping tenants, trunks, DIDs, and config*

| Column        | Type      | Description                          |
|---------------|-----------|--------------------------------------|
| id            | UUID      | PK                                   |
| tenant_id     | UUID      | FK                                   |
| name          | String    | Admin label                          |
| is_cloud      | Boolean   | Cloud vs local                       |
| trunks        | JSONB     | Array of trunk IDs, override logic   |
| dids          | JSONB     | Array of DID IDs, override logic     |
| config        | JSONB     | FreePBX-style configs, schedules     |
| api_mode      | Enum      | AMI/ARI/GraphQL                      |
| created_at    | Timestamp |                                      |

---

### 4. pbx_operator

*Operator/Dispatcher assignment per center*

| Column        | Type      | Description                                            |
|---------------|-----------|-------------------------------------------------------|
| id            | UUID      | PK                                                    |
| pbx_center_id | UUID      | FK                                                    |
| user_id       | UUID      | FK to user (must have role=operator)                  |
| assigned_at   | Timestamp | Assignment start                                      |
| unassigned_at | Timestamp | Assignment release/termination                        |
| shift         | JSONB     | Operator shift info                                   |
| onboarding    | JSONB     | Permission/verification flows                         |
| is_active     | Boolean   | Active on shift                                       |
| metadata      | JSONB     | Extensibility for center-specific permissions         |

---

### 4a. pbx_extension

*PBX extension assignment and endpoint tracking for operators/dispatchers*

| Column        | Type      | Description                                      |
|---------------|-----------|--------------------------------------------------|
| id            | UUID      | PK                                               |
| tenant_id     | UUID      | FK                                               |
| pbx_center_id | UUID      | FK to pbx_center                                 |
| extension_num | String    | Extension number/string (e.g. "601", "dispatch1")|
| user_id       | UUID      | Assigned operator or user FK                     |
| type          | Enum      | sip/pjsip/softphone/iax2/etc.                    |
| device_desc   | String    | Device details—make or software                  |
| mac_address   | String    | For hardware endpoints, optional                 |
| online_status | Enum      | registered/unregistered/offline/disconnected     |
| last_seen     | Timestamp | Most recent registration/activity                |
| location      | String    | Operator location (office/home/<USER>
| assigned_at   | Timestamp | Assignment start                                 |
| unassigned_at | Timestamp | Assignment end                                   |
| notes         | String    | Admin notes                                      |
| metadata      | JSONB     | Advanced endpoint, config, or integration tags   |

- Extensions are not tied to physical location—operators can answer calls from any SIP/soft endpoint, from anywhere.

---

### 5. pbx_cdr

*Call Detail Record (CDR), key for reporting/audit/integration*

| Column         | Type      | Description                                       |
|----------------|-----------|---------------------------------------------------|
| id             | UUID      | PK                                                |
| tenant_id      | UUID      | FK                                                |
| pbx_center_id  | UUID      | FK                                                |
| trunk_id       | UUID      | FK                                                |
| did_id         | UUID      | FK                                                |
| direction      | Enum      | inbound/outbound/internal                         |
| from_number    | String    | E.164, caller/callee                              |
| to_number      | String    | E.164                                             |
| user_id        | UUID      | FK to matched/created user, nullable              |
| driver_id      | UUID      | If call relates to driver (bridge/support/dispatch)|
| operator_id    | UUID      | If answered by operator (dispatch)                |
| duration       | Integer   | Seconds, entire call                              |
| wait_time      | Integer   | Seconds, pre-answer/queue                         |
| answered_at    | Timestamp |                                                   |
| ended_at       | Timestamp |                                                   |
| disposition    | Enum      | answered/busy/failed/missed                       |
| order_id       | UUID      | Ride/delivery order FK if logic triggers          |
| is_bridged     | Boolean   | PBX bridge privacy-mode (user-driver masking)     |
| bridge_id      | UUID      | pbx_bridge FK if bridging, else null              |
| privacy_flags  | JSONB     | e.g., masked/blocked/mute, expiration rules       |
| recording_url  | String    | Audio ref if call recorded                        |
| metadata       | JSONB     | Full CDR payload (for future regulatory analysis) |
| created_at     | Timestamp |                                                   |

---

### 6. pbx_sms

*SMS/Message activity via PBX trunk/DID*

| Column         | Type      | Description                                 |
|----------------|-----------|---------------------------------------------|
| id             | UUID      | PK                                          |
| tenant_id      | UUID      | FK                                          |
| pbx_center_id  | UUID      | FK                                          |
| trunk_id       | UUID      | FK                                          |
| did_id         | UUID      | FK                                          |
| from_number    | String    | Sender DID                                  |
| to_number      | String    | Receiver (user/driver/operator)             |
| user_id        | UUID      | Targeted/matched user FK, nullable          |
| driver_id      | UUID      | If message to driver                        |
| order_id       | UUID      | Order/ride context for message, nullable    |
| direction      | Enum      | inbound/outbound                            |
| reason         | String    | registration/ride update/notify/support     |
| status         | Enum      | delivered/failed/pending                    |
| sent_at        | Timestamp |                                             |
| received_at    | Timestamp | Delivery time confirmation, nullable        |
| metadata       | JSONB     | Full payload/sms gateway/FreePBX meta       |

---

### 8. pbx_blacklist

*Blacklist entries for numbers, users, or patterns (fraud, spam, abuse, regulatory blocks)*

| Column         | Type      | Description                                                                       |
|----------------|-----------|-----------------------------------------------------------------------------------|
| id             | UUID      | PK, unique blacklist entry                                                        |
| tenant_id      | UUID      | FK if tenant-specific, nullable for global                                         |
| pbx_center_id  | UUID      | FK, restrict to a specific dispatch center                                        |
| trunk_id       | UUID      | FK (optional), scope to trunk                                                     |
| did_id         | UUID      | FK (optional), scope to DID/number                                                |
| user_id        | UUID      | FK, for user-specific block                                                       |
| number_pattern | String    | E.164 or glob/pattern                                                             |
| blacklist_type | Enum      | incoming, outgoing, sms, all                                                      |
| reason         | String    | Operator/admin rationale                                                          |
| status         | Enum      | active / expired / disabled                                                       |
| operator_id    | UUID      | FK to operator who set/unset                                                      |
| action_taken   | Enum      | block, flag, quarantine, report                                                   |
| created_at     | Timestamp | Entry creation                                                                    |
| expires_at     | Timestamp | Optional expiry for auto-unblock                                                  |
| removed_at     | Timestamp | Entry deletion/removal timestamp                                                  |
| notes          | JSONB     | Admin/context notes                                                               |
| metadata       | JSONB     | Arbitrary context; fraud/case info                                                |

#### Audit, Compliance, and Privacy Guidance
- All actions are tracked (who/when/why) for compliance and auditability.
- Context fields allow linkage to fraud cases or regulatory actions.
- Expiry field allows for automatic or reviewed unblocking.
- Operators must provide rationale for entries; every change is logged.

#### PBX Enforcement & Integration
- Blacklist should be checked at call/SMS ingress before mapping or automated orders.
- Enforcement actions (`block`, `flag`, etc.) are reflected in dashboards and logs.
- Expired/removed entries are reviewable but not enforced; recommend periodic audit.
- Applies for automated and manual flows, including chatbot-initiated, operator, or PBX logic.

#### Best Practices
- Favor specific E.164 numbers, use patterns with caution.
- Require operator notes and escalation for severe actions.
- Auto-notify on block/unblock for audit and compliance.
- UI/dashboard should show block status, expiry, and actions available for review.

#### Call/SMS Handling Reference
- On call/message receipt, check blacklist; if matched, reject, flag, or hold per policy.
- Blacklist integration point enhances security, spam/fraud defense, regulatory standing.

```mermaid
flowchart TD
    A[Call/SMS arrives] --> B{Blacklist match?}
    B -- Yes --> C[Apply action: Block/Flag/Quarantine]
    B -- No --> D[Proceed to Matching & Order Flow]
    C --> E[Operator Dashboard/Reporting]
```

---



### 7. pbx_bridge

*Privacy-preserving temporary user-driver comm bridge (masking)*

| Column         | Type      | Description                                |
|----------------|-----------|--------------------------------------------|
| id             | UUID      | PK                                         |
| tenant_id      | UUID      | FK                                         |
| cdr_id         | UUID      | Linked CDR FK                              |
| user_id        | UUID      |                                          |
| driver_id      | UUID      |                                          |
| order_id       | UUID      | Ride/order context                        |
| bridge_start   | Timestamp | When bridge created/active                |
| bridge_expiry  | Timestamp | TTL, after which bridge is destroyed      |
| was_used       | Boolean   | Was the bridge used (call/message completed)|
| is_sms_bridge  | Boolean   | Masked SMS bridge?                        |
| privacy_flags  | JSONB     | E.g. show_number=false, one_time, block_recall |
| created_by     | UUID      | Dispatcher/service/process who created     |
| metadata       | JSONB     | Supplement/extension                      |

---

## User Mapping, Verification & Privacy Patterns

- When a call arrives, the platform matches incoming number (`from_number`) to the `user` table by E.164. If missing, a new user is created with a UUID and the number + “pending verification”. Once verified by any channel (chatbot, SMS, operator), the record is attached and phone_verified flag is updated.
- Operator in dispatch center can match/unmatch numbers, with auditing in metadata (who/when).
- All bridge operations (user-driver or driver-user call/message) are subject to explicit permission checks (user opt-in, account status) and are auditable.
- Bridges (voice/SMS) automatically expire per order/rule, are strictly masked, and have recall blocked after window.

---

## Unified Order/Dispatch Integration

- Voice/SMS triggers (CDRs) that are converted into orders use same distribution and driver assignment logic as chatbot/app. Orders are always associated with operator (if call) and user (by number).
- All reporting aggregates calls, messages, rides, operators, drivers per tenant and per platform, enabling full dashboarding, support and audit logs.
- Bridged/anonymous interactions (masked numbers/calls) are flagged in order, CDR, and bridge tables for regulatory reports.

---

## Audit, Compliance & Dashboarding

- Retain all CDR, SMS, bridge, and session records for audit (with `created_at` and privacy flags).
- All matching/unmatching, operator assignments, and bridge events are tied to user_id and operator_id for full traceability.
- Permission, consent, and privacy decisions (user permissions for bridge, masking rules) must be json-logged for compliance.

---

## Architecture Rationale

- All schema designed for strict multi-tenant isolation: every key table indexed on `tenant_id`.
- Modeling assumes event-driven AMI/ARI/GraphQL integrations; supports log re-ingest and batch reporting.
- Orders from PBX and chatbot side converge to identical dispatch and reporting models.
- Designed for open extensibility: new bridge types, recording, regional privacy can be enabled by adding columns/expanding JSONB.
- All sensitive transitions (e.g., bridge creation, call handoff, operator mapping) are tracked in metadata for security and compliance review.

---

## Conclusion

This schema comprehensively supports Asterisk/FreePBX PBX integration for multi-tenant ride-hailing and delivery SaaS: secure, audit-ready, privacy-preserving, and flexible for any deployment model.