# Roles & Access Schema  
**Multi-Tenant Taxi/Delivery Platform: RBAC and Domain Role Modeling Blueprint**

---

## Introduction

Role-based access control (RBAC) is fundamental for secure, privacy-compliant, and extensible multi-tenant SaaS. This schema blueprint defines role models, assignment patterns, visibility rules, extensibility strategies (ENUM/table-driven), and best practices for supporting complex, evolving business logic securely and with minimal friction.

---

## Core Roles: Privileges & Business Mapping

| Role          | Privileges                                          | Visibility (non-PII)   | Domain Functions                          |
|---------------|-----------------------------------------------------|------------------------|--------------------------------------------|
| Passenger     | Order/ride booking, track own rides, feedback       | Ride stats, anonymized | Place order, see driver stats/successful rides, rating, partial feedback     |
| Driver        | Accept rides, vehicle mgmt, update status, history  | Own orders, vehicle    | Driver profile, stats, own dashboard, can see passenger anonymized stats    |
| Dispatcher    | Answer calls, assign rides, interact via PBX/chat   | Driver/passenger order | Dashboard, PBX endpoint, call/CALL log, queue mgmt, escalation              |
| Manager       | Asset view, operator stats, financial/performance   | Team asset, ride stats | Analytics, vehicle pool, operator manage/reports, limited user/ride access  |
| Tenant Admin  | All user/asset mgmt, assign roles, global audit     | Tenant-wide            | Add/remove all users/roles, finance, full dashboard, compliance reporting   |

_**Franchise Owner/Brand Owner roles and others may exist as extensions; model provides for this.**_

---

## Role Assignment Model

- Users can have multiple roles, possibly across multiple tenants.  
- A user → tenant → role mapping enables per-company, per-instance assignments.
- Only admins and managers with “elevated” permissions may assign role upgrades/downgrades or make managerial changes.
- Passengers: role assigned at order/interaction or via admin onboarding.
- Privileges and domain data are always filtered by both `tenant_id` and `role`.

**ER Diagram:**

```mermaid
erDiagram
    USER ||--o{ USER_TENANT_ROLE : has
    USER_TENANT_ROLE }o--|| TENANT : belongs_to
    USER_TENANT_ROLE }o--|| ROLE : assigned_as
```

---

## Schema Patterns: ENUM vs Table Roles

### ENUM Roles (Simple/Performance, Drizzle-Prone)

- Roles like passenger, driver, dispatcher, manager, tenant_admin are hard-coded in an enum in code and the DB (e.g., `role: 'passenger'|'driver'|...`).
- Adding a role requires modifying the enum, schema migration, and code update.
- Pros: Fast, simple, efficient; minimal table joins.
- Cons: Less extensible; can’t store per-role config or metadata.

### Role Table (Extensible/Enterprise)

- Model `role` as a table: id, name, description, permissions (JSONB), tenant_id=null (global) or per-tenant (custom).
- Arbitrary roles can be added, with descriptive config, UI labels, granular permissions (e.g., `can_edit_assets`, `can_assign_rides`).
- Pros: Extensible, upgradeable at runtime, greater customization per-tenant.
- Cons: More joins, slightly slower, more migration if core code expects ENUM.

**Practical Best Practice:**
- Use ENUM for core global roles, switch to a role table for growing platforms requiring advanced RBAC or tenant-specific add-ons.

---

## RBAC Best Practices

- **Mapping Table:**  
  Use a join table (e.g., user_tenant_role) with user_id, tenant_id, role_id (or role ENUM), and status fields. Needs index for fast role/user lookup.
- **Visibility Matrices:**  
  Control what each role can see/modify; use filters at query and API layer (Drizzle/ORM: query scoping).
- **Row-/Attribute-level Security:**  
  Leverage database policies or code (e.g., Postgres RLS) to enforce cross-domain access per role.
- **Admin-Only Modifications:**  
  Restrict role upgrades/downgrades, user assignment, and critical changes to tenant admins/managers only.
- **Domain Function Extensions:**  
  Role-specific tables (driver_vehicle, driver_stats, dispatcher_dashboard, call_log) store data only for relevant users/roles mapped via user_tenant_role.
- **Audit & Traceability:**  
  Every role assignment/change is logged (by whom, when, from where). This supports security and compliance.
- **Role Expansion:**  
  When new role added, update ENUM/table, add required per-role tables, and expand permission/assertion logic at both DB and app layer.
- **Privilege Inheritance (Advanced):**  
  Support for role hierarchies or inheritance may be modeled via parent_role_id or permission arrays.

---

## Example: Adding a New Role

**Step 1:** Add new value to ENUM or insert new row in roles table (with relevant permissions).

**Step 2:** Migrate schema and code to handle new role-specific data tables/fields if needed.

**Step 3:** Update `user_tenant_role` mapping assignments/admin dashboards.

**Step 4:** Test privilege assignments, visibility, and role-controlled business logic for correctness.

---

## Role-Controlled Data/Views

- **Passenger**: Non-PII stats exposed to drivers and dispatch (e.g., rating, ride count). User PII never shown to non-admins/operators except where permitted (e.g. during a ride, in anonymized form).
- **Driver:** Vehicle stats, history, basic passenger stats, never passenger PII (phone/address) except during active ride/pickup.
- **Dispatcher:** Can interact with any ride/user/driver in current tenant, view all call/ride logs, but not PII attribute except where needed for order.
- **Manager/Admin:** Full asset view, analytics, financial and compliance dashboards; may edit roles, assets, all.
- **Extension to New Roles:** Table-driven roles may have custom reporting, dashboards, or business rules mapped by role/permission fields.

---

## Implementation Guidance: Drizzle & Modern ORM

- Drizzle, and most modern ORMs, allow for easy ENUM/table mapping, join-based querying, and per-role query scoping.
- Always index user, tenant, role columns for performance on lookup-heavy queries.
- Role privilege matrices may live in code (for simple ENUM) or in the database (for advanced, tenant config).
- Enforce permissions both in DB/policy and app/service logic—never rely on the UI alone.

---

## Conclusion

This schema and role modeling approach provides durable, extensible, and secure RBAC for your ride-hailing SaaS. It separates core global roles from domain/tenant-specific ones, minimizes friction for platform evolution, and ensures a robust architectural foundation for future growth and compliance.