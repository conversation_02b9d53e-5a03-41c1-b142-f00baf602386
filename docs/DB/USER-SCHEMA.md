# User, Role, and Identity Schema  
**Multi-Tenant Taxi/Delivery Platform: Domain Blueprint**

---

## Introduction

This document details the user, role, account, and identity domain for a multi-tenant SaaS taxi and delivery platform. Its aim is to offer a robust, extensible foundation for authentication, authorization, audit, privacy, KYC requirements, and support for multi-provider/social/bot identity mapping—serving as both a schema reference and onboarding guide for implementors, privacy officers, and architects.

---

## Domain Overview & Boundaries

- **Users** are globally unique (by phone, with email/UUID as fallback) and may interact with any number of tenants.
- **Roles** are scoped per tenant: a user may simultaneously act as driver in one tenant, passenger in another, and operator/manager in a third.
- **Identity** section supports cross-channel OAuth, chatbots, messaging, and mobile/web login.
- **Audit and Privacy** needs dictate fine-grained history, data ownership, right-to-be-forgotten and consent structures, plus compliance support (GDPR etc).
- **All data access and modification is tenant-aware and privacy-focused.**

---

## Entity Relationship Diagram (mermaid)

```mermaid
erDiagram
    USER ||--o{ USER_TENANT : has
    USER_TENANT }o--|| TENANT : belongs_to
    USER ||--o{ USER_IDENTITY : has
    USER ||--o{ USER_PROFILE_HISTORY : changes
    USER ||--o{ USER_KYC : undergoes
    USER ||--o{ USER_CONSENT : gives
    USER ||--o{ USER_ONBOARDING : experiences
    USER_TENANT }o--|| ROLE : assigns
```

---

## Table Listing & Deep Explanations

### 1. user

| Column         | Type      | Description                                                     |
|----------------|-----------|-----------------------------------------------------------------|
| id             | UUID      | Primary Key. Global user identity                               |
| phone          | String    | Unique globally. Main login & notification credential            |
| email          | String    | May be null, alternate recovery                                 |
| external_ref   | String    | Optional, system or 3rd party ID                                |
| legal_name     | String    | For billing/identification; optional per privacy laws           |
| display_name   | String    | App display/preferred name                                      |
| date_of_birth  | Date      | For age- or insurance-restricted tenants                        |
| language       | String    | Preferred locale (i18n)                                         |
| avatar_url     | String    | CDN link for photo                                              |
| communication_opt_in | JSONB  | Per-channel notification consent                              |
| privacy_flags  | JSONB     | E.g. GDPR explicit consent, marketing, withdrawal flag          |
| created_at     | Timestamp | Account registration                                           |
| updated_at     | Timestamp | Last change                                                    |
| deleted_at     | Timestamp | For soft deletes/right-to-be-forgotten                         |
| metadata       | JSONB     | Open extension: custom profile fields, AD integration, etc.     |

- **Indexes:** Unique (phone), (email), (external_ref), partial index (deleted_at is null)
- **Constraints:** At least one credential (phone, email, external_ref) must be present and unique.

---

### 2. user_tenant

| Column          | Type      | Description                                          |
|-----------------|-----------|-----------------------------------------------------|
| id              | UUID      | PK                                                  |
| user_id         | UUID      | FK to user.id                                       |
| tenant_id       | UUID      | FK to tenant.id                                     |
| role_id         | UUID      | FK to role.id (tenant-scoped)                       |
| active          | Boolean   | If user currently belongs to this tenant            |
| date_joined     | Timestamp | First joined this tenant                            |
| date_left       | Timestamp | Last revoked/left                                   |
| is_primary      | Boolean   | Default tenant for app context                      |
| invitation_id   | UUID      | FK (inviter user, optional)                         |
| onboarding_state| String    | Enum: not_started, in_progress, complete            |
| driver_license  | String    | Optional, drivers only                              |
| verified        | Boolean   | Onboarding/KYC passed                               |
| metadata        | JSONB     | Per-tenant, per-role attributes, permissions, etc.  |
| last_activity_at| Timestamp | For reporting/support                               |

- **Unique constraint:** (user_id, tenant_id)  
- **Candidate keys:** role-based composite indexes for efficient permission checks.

---

### 3. role

| Column      | Type    | Description                                  |
|-------------|---------|----------------------------------------------|
| id          | UUID    | PK                                           |
| tenant_id   | UUID    | Null = system/global, else tenant-specific   |
| name        | String  | driver, passenger, admin, manager, operator, support, etc. |
| description | String  | Optional, UI/docs role description           |
| permissions | JSONB   | Scopes, as array/string, for fine-grained auth |
| is_default  | Boolean | Default role assignment when joining         |
| is_system   | Boolean | If true, cannot be deleted/changed by tenant |

---

### 4. user_identity

| Column      | Type    | Description                                 |
|-------------|---------|---------------------------------------------|
| id          | UUID    | PK                                          |
| user_id     | UUID    | FK                                          |
| provider    | Enum    | telegram, viber, facebook, google, apple, phone, etc. |
| external_id | String  | Provider-side UID                           |
| display     | String  | Profile display name                        |
| avatar_url  | String  | Optional                                    |
| metadata    | JSONB   | Channel tokens, refresh token, etc          |
| linked_at   | Timestamp | When linked                               |
| unlinked_at | Timestamp | For audit/compliance                      |

- **Composite Unique:** (provider, external_id)
- **Indexes:** (user_id), (provider)

---

### 5. user_profile_history

| Column         | Type      | Description                              |
|----------------|-----------|------------------------------------------|
| id             | UUID      | PK                                       |
| user_id        | UUID      | FK to user.id                            |
| changed_by_id  | UUID      | FK to user.id/operator, null if self     |
| change_type    | Enum      | onboarding, phone_change, admin_update…  |
| old_value      | JSONB     | Pre-change snapshot                      |
| new_value      | JSONB     | Post-change snapshot                     |
| changed_at     | Timestamp | Time of change                           |
| context        | String    | Source (web, api, admin, mobile, etc)    |

---

### 6. user_kyc

| Column         | Type      | Description                              |
|----------------|-----------|------------------------------------------|
| id             | UUID      | PK                                       |
| user_id        | UUID      | FK to user.id                            |
| provider       | String    | Doc verifier (Jumio, Stripe, native, ...)|
| submitted_at   | Timestamp |                                          |
| status         | Enum      | pending, approved, rejected, expired     |
| document_type  | String    | Passport, DL, ID, utility bill, etc.     |
| document_number| String    | Redacted/masked as required              |
| country        | String    | Document issuer country                  |
| expiry_date    | Date      | If present                               |
| rejection_reason | String  | Optional                                 |
| metadata       | JSONB     | Image ref, hash, OCR, etc.               |

---

### 7. user_verification

| Column         | Type      | Description                              |
|----------------|-----------|------------------------------------------|
| id             | UUID      | PK                                       |
| user_id        | UUID      | FK                                       |
| channel        | Enum      | sms, email, push, in_app                 |
| token          | String    | Hashed                                   |
| expires_at     | Timestamp |                                          |
| verified_at    | Timestamp | When successful                          |
| attempts       | Integer   | (# tries for anti-fraud)                 |
| context        | String    | registration, phone_change, reset_pwd    |
| created_at     | Timestamp |                                          |

---

### 8. user_consent

| Column            | Type      | Description                    |
|-------------------|-----------|--------------------------------|
| id                | UUID      | PK                             |
| user_id           | UUID      | FK                             |
| consent_type      | String    | marketing, data_share, etc.    |
| granted           | Boolean   | True if allowed                |
| granted_at        | Timestamp |                                |
| revoked_at        | Timestamp | Nullable                       |
| tenant_id         | UUID      | If tenant-specific policy      |
| metadata          | JSONB     |                               |

---

### 9. user_onboarding

| Column             | Type      | Description                 |
|--------------------|-----------|-----------------------------|
| id                 | UUID      | PK                          |
| user_id            | UUID      | FK                          |
| tenant_id          | UUID      | FK                          |
| completed_steps    | JSONB     | Checklist, progress         |
| status             | Enum      | incomplete, in_progress...  |
| completed_at       | Timestamp | Nullable                    |
| metadata           | JSONB     | Open extension slot         |

---

## Data Ownership, Access, and GDPR/Compliance

- **Ownership and Visibility:**
  - Each user can view and manage all their data, regardless of which tenant(s) they've joined.
  - Tenant administrators can see only user records associated with their tenant, plus historic data *created during the period of membership*.
  - Role mapping and permission edits are always tenant-scoped.
  - Historical and soft-deleted data can be surfaced only with appropriate role permissions (superadmin, DPO, etc).

- **Compliance/Privacy:**
  - Soft deletion everywhere (`deleted_at`) supports right-to-be-forgotten.
  - All consent, verification, KYC, onboarding steps recorded with time, channel, and context.
  - All write actions tracked via `user_profile_history` for auditability.

- **Implementation guidance:**
  - Index all foreign keys (`user_id`, `tenant_id`), and privacy flags.
  - Data access logic must filter via both `tenant_id` and `active=true` by default for user/tenant membership.
  - Use postgres row-level security (RLS) policies if possible for enforcement.

---

## Summary Table

| Table                | Multi-Tenant Key | Unique Constraints    | Has Metadata | GDPR/Soft Delete | Notes              |
|----------------------|------------------|----------------------|--------------|------------------|--------------------|
| user                 | -                | phone, email, ext_id | ✔            | ✔                | Global             |
| user_tenant          | tenant_id        | user+tenant          | ✔            | ✔                | Membership+Role    |
| role                 | tenant_id/null   | name+tenant(?)       | ✔            | (n/a)            | System or tenant   |
| user_identity        | -                | provider+external_id | ✔            | (Yes via unlinked_at) | External links |
| user_profile_history | -                | id                   | ✔            | (n/a)            | All change events  |
| user_kyc             | -                | user_id+provider     | ✔            | ✔                | All KYC events     |
| user_verification    | -                | id                   | ✔            | (n/a)            |                  |
| user_consent         | tenant_id        | user+consent_type    | ✔            | ✔                | Per-tenant or global |
| user_onboarding      | tenant_id        | user+tenant          | ✔            | ✔                | Steps/progress     |

---

## Rationale/Best Practices (Drizzle/Postgres)

- Use UUID PKs for auditability and sharding.
- All JSONB columns are for extension, never for core schema; use for custom fields, provider-specific data, or app-feature toggles.
- Enum fields for role, onboarding states, consent types, etc. enforce type safety and reporting.
- Audit and KYC records are immutable; append-only patterns help for compliance.
- Consider partitioning by tenant for very large deployments.
- Join/permission checks must always use tenant+role relation, never just user.
- Document explicit indexes for GDPR queries (deleted_at, consent).
- Social/bot identities should be unlinked by setting `unlinked_at` rather than hard delete.

---

## Conclusion

This schema section ensures that identity, access, privacy, and cross-tenant user logic is explicit, extensible, auditable, and compliant for complex SaaS environments.