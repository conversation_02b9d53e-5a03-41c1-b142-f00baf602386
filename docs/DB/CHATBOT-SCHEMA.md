# Chatbot & Messaging Schema  
**Multi-Tenant Taxi/Delivery Platform: Domain Blueprint**

---

## Introduction

This document details the database schema for chatbot and messaging integration in a multi-tenant taxi/delivery SaaS. It supports all major chatbot vendors (Telegram, Viber, WhatsApp, Messenger, custom) and is designed for extensibility, per-tenant configuration, cross-channel features, compliance, privacy, and audit. Mandatory geolocation and phone verification tracking are included at all relevant levels.

---

## Multi-Tenant Chatbot Integration: Key Concepts

- **Per-Tenant Bots:** Each tenant can operate multiple bots/channel integrations (branded, localized, with separate config/secrets).
- **User-Bot Linkage:** Map users to external bot/social accounts, enforcing phone verification and consent for each.
- **Session & Messaging Audit:** Track all communication with geolocation, verification, session and context details as required by channel/platform.
- **Vendor Extensibility:** Schema can be extended for new platforms or custom logic without structural rewrites.

---

## Domain Tables & Deep Structure

### 1. chatbot_provider

*Registry of supported bot vendor types and their capabilities*

| Column          | Type      | Description                         |
|-----------------|-----------|-------------------------------------|
| id              | UUID      | PK                                  |
| name            | String    | telegram / viber / whatsapp etc     |
| description     | String    | UI and docs                         |
| logo_url        | String    | Branding asset                      |
| features        | JSONB     | Supported features as JSON array     |
| enabled         | Boolean   | Platform-level toggle               |
| created_at      | Timestamp |                                     |
| updated_at      | Timestamp |                                     |

---

### 2. chatbot_instance

*One row per actual, deployable chatbot (e.g., Telegram bot for Tenant X)*

| Column         | Type      | Description                       |
|----------------|-----------|-----------------------------------|
| id             | UUID      | PK                                |
| tenant_id      | UUID      | FK                                |
| provider_id    | UUID      | FK → chatbot_provider             |
| name           | String    | Brandable display name            |
| bot_username   | String    | Username/handle (for applicable)  |
| bot_token      | String    | Encrypted/API key                 |
| webhook_url    | String    | For direct callback integration   |
| config         | JSONB     | Custom per-bot settings           |
| is_active      | Boolean   | Bot live toggle                   |
| created_at     | Timestamp |                                   |
| updated_at     | Timestamp |                                   |
| metadata       | JSONB     | Future extensions, e.g. locales   |

---

### 3. chatbot_user

*Maps an internal user to a chatbot/social account, tracks phone verification, consent, and channel claims*

| Column          | Type      | Description                             |
|-----------------|-----------|-----------------------------------------|
| id              | UUID      | PK                                      |
| user_id         | UUID      | FK → user.id (internal user)            |
| chatbot_instance_id | UUID  | FK → chatbot_instance                   |
| provider_user_id| String    | External channel user id                |
| phone_verified  | Boolean   | True if SMS/OTP verified                |
| verification_date| Timestamp| When passed                             |
| consent         | Boolean   | User opt-in/consent for bot use         |
| consent_date    | Timestamp | When consent given                      |
| blocked         | Boolean   | User blocked this bot                   |
| locale          | String    | User's channel locale                   |
| joined_at       | Timestamp | First time mapped                       |
| last_seen_at    | Timestamp | Last activity for this mapping          |
| metadata        | JSONB     | Profile/bot-channel/adaptation data     |

- **Constraints:** Unique (provider_user_id, chatbot_instance_id)
- **Indexes:** (user_id, chatbot_instance_id), (phone_verified)

---

### 4. chatbot_session

*Session/log for user↔bot interactions (for platforms with explicit session model)*

| Column          | Type      | Description                                 |
|-----------------|-----------|---------------------------------------------|
| id              | UUID      | PK                                          |
| chatbot_user_id | UUID      | FK → chatbot_user                           |
| tenant_id       | UUID      | FK                                          |
| started_at      | Timestamp |                                             |
| ended_at        | Timestamp | Nullable; for open sessions                 |
| verified        | Boolean   | Was phone verified in this session?         |
| geolocation     | Geometry  | Session start (may be updated by message)   |
| context         | String    | Ride, support, onboarding, etc.             |
| metadata        | JSONB     | OAuth tokens, session secrets, etc.         |

---

### 5. chatbot_message

*Every inbound/outbound message with channel/session/verification/geolocation tracking*

| Column            | Type      | Description                                   |
|-------------------|-----------|-----------------------------------------------|
| id                | UUID      | PK                                            |
| chatbot_session_id| UUID      | FK → chatbot_session                          |
| chatbot_user_id   | UUID      | FK → chatbot_user (redundancy for easy lookup)|
| direction         | Enum      | in/out                                        |
| provider_message_id| String   | Channel message id (if any)                   |
| sent_at           | Timestamp |                                               |
| message_type      | Enum      | text/image/voice/location/verification/...    |
| content           | Text      | Payload                                       |
| phone_verified    | Boolean   | Phone verified as of this message             |
| verification_reference| String| OTP/code/token if applicable                  |
| geolocation       | Geometry  | If message provides (mandatorily log when available)|
| replied_to_id     | UUID      | FK to chatbot_message (threading)             |
| error_flag        | Boolean   | For failed/blocked/invalid messages           |
| metadata          | JSONB     | Attachments, rich info, channel-specific      |

- **Indexes:** (chatbot_user_id, sent_at), (message_type), (phone_verified), (geolocation)
- **Mandatory fields:** All messages must record geolocation if received, and phone verification must be true for transactional actions.

---

### 6. chatbot_config

*Tenant/channel-level configuration & feature toggles*

| Column         | Type      | Description                |
|----------------|-----------|----------------------------|
| id             | UUID      | PK                         |
| tenant_id      | UUID      | FK                         |
| provider_id    | UUID      | FK                         |
| settings       | JSONB     | Webhook, keys, toggles,    |
| created_at     | Timestamp |                            |
| updated_at     | Timestamp |                            |

---

### 7. chatbot_event

*Event log for audit, error, and system tracking*

| Column         | Type      | Description                   |
|----------------|-----------|-------------------------------|
| id             | UUID      | PK                            |
| chatbot_instance_id | UUID | FK                            |
| event_type     | Enum      | error, webhook, user_block, etc|
| user_id        | UUID      | FK, nullable                  |
| session_id     | UUID      | FK, nullable                  |
| message_id     | UUID      | FK, nullable                  |
| details        | JSONB     | Full event context            |
| occurred_at    | Timestamp |                               |

---

## Mandatory Geolocation & Phone Verification Handling

- **chatbot_user:**  
  - Minimum requirement: phone_verified=true and verification_date not null for user-bot mapping to be considered valid and actionable.
  - Consent field must be true before processing transactional or account operations.

- **chatbot_message:**  
  - Whenever geolocation is included by channel, it must be stored as geometry and indexed.
  - For transactional, ride, or support messages, phone verification (`phone_verified=true`) must be enforced as precondition—log verification_reference.

- **chatbot_session:**  
  - If possible, track session geo for all cross-channel commands; update on each geolocated message.

---

## Rationale for Table and Column Structure

- **Channels evolve rapidly**; use JSONB for flexible channel-specific fields, but keep verification/geo/tenant standardized at the top level.
- **Audit/compliance:** All actions/message/links explicitly logged with time and relevant user, for later tracing even across tenants.
- **Multi-tenancy:** All key tables scoping to tenant and instance, indexed for fast queries and cross-tenant isolation.
- **Consent/verification:** Explicitly tracked per chatbot_user and per message with field status and reference to pass audits.
- **Performance:** Frequent queries (latest messages, active users, geo trends) drive index design on chatbot_user.id, sent_at, phone_verified, and geolocation.

---

## Extensibility & Postgres/Drizzle Guidance

- Use UUID PKs for all tables for global uniqueness.
- Geometry fields (PostGIS) are required for all geo-logged interactions; ensure proper types and indexing (GiST).
- Enum columns for all channel/status/type fields; allow for updatable enums as channel ecosystems evolve.
- Webhook/secret/API keys MUST be encrypted at rest in config tables.
- Events are append-only: never delete, to ensure full audit trail.
- Partitioning strategy by tenant_id or provider_id recommended for very large installations.
- All join foreign keys should have not null constraint unless explicitly optional for channel support.
- JSONB used only for channel/provider adaptations, never core schema.

---

## Privacy, Security, Multi-Tenant Controls

- Data segregation: each message/session/user mapping is scoped to a single tenant/chatbot instance.
- User can unlink/withdraw consent at any time (flagged in chatbot_user), triggering deactivation.
- Full message/event trail supports data access requests and export on demand per privacy/GDPR.
- Phone numbers and IDs used for mapping/verification should only be readable by high-trust, auditable service logic, not exposed to tenants.

---

## Channel-Specific Deep Dives

### WhatsApp Integration

WhatsApp has several unique characteristics:

- **User Identifier:** Usually the phone number (E.164 format); may be subject to hashing/anonymization (GDPR/local regulation).
- **Message Types:** text, media (image/audio/document), contacts, location, templates/interactive, verification codes.
- **Verification:** WhatsApp account itself requires phone verification, but for platform actions (orders, payments, deep links) an additional SMS-based OTP step may be required and should be captured in `chatbot_user.phone_verified`, `verification_date`, and stamped per-message in `chatbot_message`.
- **Geolocation:** User can send current location as a message (message_type=location), stored in `geolocation`.
- **Consent/Opt-In:** Some communications (especially outbound, like ride status or support) require explicit WhatsApp opt-in, which must be recorded in `chatbot_user.consent` and detailed in `metadata` (including opt-in timestamp, WhatsApp-specific consent payload).
- **Privacy:** The phone (provider_user_id) and consent state must be tracked for all WhatsApp users; do not store original numbers when hashing/anonymization is required.

**Typical WhatsApp-specific fields and adaptations:**
| Field / Table         | Example Use                                               |
|-----------------------|----------------------------------------------------------|
| chatbot_user.provider_user_id | Always the WhatsApp phone; hash or encrypt as needed |
| chatbot_message.message_type  | Whatsapp supports 'template', 'interactive', etc.      |
| chatbot_message.geolocation   | For 'location' messages (mandatory capture)           |
| chatbot_user.metadata         | WhatsApp opt-in payload, template consent details      |

_Practical Example:_
- When the user sends their location via WhatsApp, store it in chatbot_message with `message_type=location` and populate `geolocation` from the WhatsApp message payload.

---

### Viber Integration

- **User Identifier:** Viber assigns a unique user ID (`provider_user_id`), not generally reversible to phone or username.
- **Message Types:** text, rich media, stickers, files, location, contacts, carousel, group chats.
- **Verification:** Viber does not guarantee phone verification of the user account (due to portability/number reassignment) so SMS-based verification for transactional actions is required and must be captured per chatbot_user, and re-checked periodically or per critical action. Record each (re-)verification event.
- **Geolocation:** Can be sent as location message; must be captured in `chatbot_message.geolocation`.
- **Consent/Opt-In:** Outbound communications (e.g., push notifications, marketing) require user consent, should be tracked in `chatbot_user.consent` and `metadata`.
- **Privacy:** Viber user ID is opaque; avoid storing personal information in `metadata`.

**Typical Viber-specific fields and adaptations:**
| Field / Table         | Example Use                                                   |
|-----------------------|--------------------------------------------------------------|
| chatbot_user.provider_user_id | Viber user ID (not the phone!)                        |
| chatbot_message.message_type  | Viber supports 'sticker', 'carousel', etc.            |
| chatbot_message.geolocation   | If user sent location, log as geometry                |
| chatbot_user.metadata         | E.g., Viber device info, consent event log            |

_Particular patterns:_
- For group chats (driver pools, support groups) map the group id in `chatbot_instance.metadata` and link all group message traffic through appropriate schema relations.

---

### General Cross-Channel Guidance
- All unique fields for WhatsApp or Viber should be held in `metadata` (channel adaptations), unless they are common enough to warrant top-level columns.
- The schema is designed to anticipate the addition of further WhatsApp- or Viber-specific features (voice note, media audit, group events) as channel APIs evolve.
- **Never** surface personal provider_user_id or numbers to tenants without appropriate permissions/logging/audit.

- **All Channels:**
  - Core schema must be stable, all vendor adaptation in metadata.

---

## Conclusion

This schema delivers robust, deeply auditable, and extensible chatbot/messaging support for the multi-tenant taxi/delivery SaaS—fulfilling all ownership, privacy, phone verification, and geolocation requirements for current and future channels.