# Geolocation Data Architecture for Multi-Tenant Taxi/Delivery Platform

---

## Overview

This document describes a best-practice architecture for capturing, storing, and serving rich geolocation data using PostgreSQL (with PostGIS), OSM vector maps, and integrating navigation and location data from sources including Telegram, Android, and MQTT/IoT devices.

---

## 1. Geodata Schema and Storage

### Core Table Example

| Field      | Type            | Purpose                                       |
|------------|-----------------|-----------------------------------------------|
| id         | UUID            | PK                                            |
| country    | String          | ISO-3166 country code                         |
| city       | String          | City name                                     |
| address    | String          | Full address or POI label                     |
| geom       | GEOMETRY(Point) | Geospatial field, e.g. (lat, lon)             |
| latitude   | float/double    | Redundant for legacy/quick lookups            |
| longitude  | float/double    |                                               |
| source     | Enum            | telegram, android, mqtt, web, manual, etc.    |
| user_id    | UUID            | FK when geodata is tied to an account         |
| accuracy   | float           | Reported accuracy in meters                   |
| area_id    | UUID            | FK to service/zone area geometry              |
| osm_tags   | JSONB           | OSM-derived POI/context tags                  |
| metadata   | JSONB           | Raw payload from device/app                   |
| created_at | Timestamp       | Time recorded                                 |

**Indexing:**  
- `GIST` index on `geom` for fast spatial queries.
- Compound indexes for (city, country), area, and user as needed.

---

## 2. PostGIS Requirements

- Install and enable `postgis` extension in PostgreSQL for full GIS support (geometry/geography types, spatial functions, distances).
- Use standard SRID 4326 (WGS-84, lat/lon) for compatibility.

---

## 3. OpenStreetMap (OSM) Vector Map Integration

- **OSM data** can be imported via `osm2pgsql` or other loaders.
- **Tile Serving:** Run an open-source vector tile server (Tegola, TileServer GL, Tilekiln) for frontend map layers.
- **Frontend:** Use MapLibre, Mapbox GL JS, Leaflet or OpenLayers to render OSM/map tiles.

**Best Practice:**  
- Store OSM POI and area polygon geometries in dedicated tables for fast querying and service-zone management.

---

## 4. Navigation and Routing

- Integrate an open-source routing engine (OSRM, GraphHopper, Valhalla) on your OSM dataset for navigation, ETA, directions.
- Routing queries can be called from backend services and/or exposed to mini-apps, chatbots, dispatch dashboards.

---

## 5. Multi-Source Geolocation Ingestion

- **Telegram:** Receive location payloads via bot API (`location` message objects), store as point with user/reference.
- **Android/iOS Devices:** Collect via native GPS (LocationManager/Location API). Upload via REST or publish to MQTT.
- **MQTT:** Ingest real-time location and device payloads from IoT/mobile using MQTT broker and message consumers.
- **Other APIs (web, hardware, PBX, etc.):** Support extensibility via `source` and `metadata` columns.

---

## 6. Area, Zone, and Polygon Modelling

- Use polygon/multipolygon tables for service zones, restricted areas, rate boundaries, etc.
- Leverage PostGIS `ST_Within`, `ST_Contains`, `ST_Distance` for spatial queries (e.g., “Is user in zone X?”).

| Field         | Type             | Purpose                   |
|---------------|------------------|---------------------------|
| id            | UUID             | PK                        |
| tenant_id     | UUID             | Scope for multi-tenant    |
| name          | String           | Area/zone name            |
| geom          | GEOMETRY(Polygon)| Service area or boundary  |
| city, country | String           | Optional context          |
| osm_tags      | JSONB            | OSM-import metadata       |

---

## 7. Practical Features and Best Practices

- **Reverse geocoding:** Use an open-source Nominatim server to resolve coordinates to address, POI, etc.
- **Data validation:** Validate/clean incoming lat/lon, accuracy, and enforce correct SRIDs.
- **Extensible ingestion:** Design `metadata` field to keep raw geolocation payloads from all sources.
- **Compliance:** Consider GDPR/location privacy for all data; retain only as required, with consent, and support data removal on request.

---

## 8. Example Workflows

- **Ride Booking via Telegram:** User shares location → bot receives Telegram API JSON → (lat, lon) stored with user_id, source=telegram.
- **Driver App Update via MQTT:** Driver’s phone publishes location JSON to MQTT broker → backend ingests and stores/updates last known position.
- **Zone Pricing:** Order/ride start/end checked against polygons in area table for pricing/analytics.

---

## 9. Indexing and Performance

- Use GIST (geometry) and (city/country/tenant) composite indexes.
- For high-frequency updates (driver tracking), use a `current_positions` table with (upserts) for real-time queries separate from historical logs.

---

## 10. Vector Map & Navigation Integration

- Serve tiles (vector or raster) per zone/country/tenant.
- Allow flexible zone definition and join with live orders/trips for dispatch/analytics.

---

## Conclusion

With PostGIS, OSM vector tiles, and flexible table/schema design, you can support modern navigation, analytics, and deeply integrated geolocation/data flows for rides, deliveries, dispatch, and mini-apps across any connection source.