# Database Schema Blueprint: Multi-Tenant Taxi & Delivery SaaS Platform

## Introduction

This document defines a comprehensive, production-ready database schema for a multi-tenant Uber-like taxi and delivery SaaS platform. The system is architected to power taxi and delivery workflows, chatbots, dispatch centers with PBX/call center integration, and communication features—serving multiple companies (“tenants”) on a single infrastructure. It is designed for extensibility, full localization (i18n), and integration of future communication channels (Telegram, Viber, FB, etc.), with robust audit, billing, and multi-role user management.

**Note:**  
- *No code or Drizzle syntax is included; this is a logical schema and rationale document to guide ORM/model implementation (e.g., Drizzle/Typescript/Postgres).*
- All design decisions prioritize scalability, modularity, and future extensibility.

---
## Logical Domains & Table Groups

1. [Tenancy & Organization](#tenancy--organization)
2. [Users, Roles, and Identity](#users-roles-and-identity)
3. [Vehicles & Fleet Management](#vehicles--fleet-management)
4. [Rides, Orders, Delivery](#rides-orders-delivery)
5. [Payments & Billing](#payments--billing)
6. [Communication: Messaging, Chatbots, PBX](#communication-messaging-chatbots-pbx)
7. [Dispatch & Operators](#dispatch--operators)
8. [Geography, Zones, and Maps](#geography-zones-and-maps)
9. [Promotions, Rewards, Ratings](#promotions-rewards-ratings)
10. [Support, Contact, Audit](#support-contact-audit)
11. [System Meta/Config & Extensibility](#system-metaconfig--extensibility)
12. [Indexing, Enums, Scalability Notes](#indexing-enums-scalability-notes)

---

## Tenancy & Organization

### `tenant`
**Purpose:** Represents a company/operator that subscribes to the platform.

| Column             | Type        | Description                                                     |
|--------------------|------------|-----------------------------------------------------------------|
| id                 | UUID        | Primary key, unique per tenant.                                 |
| name               | String      | Public company/display name.                                    |
| legal_name         | String      | Registered company name (for billing/compliance).               |
| email              | String      | Official email (system/billing contact).                        |
| phone              | String      | Support/official phone.                                         |
| logo_url           | String      | Branding/logo.                                                  |
| address            | String      | HQ/office address.                                              |
| country            | String      | Country for locale/taxation.                                    |
| timezone           | String      | Default timezone for schedules, reports, etc.                   |
| plan               | Enum        | Subscription plan/tier.                                         |
| status             | Enum        | Active/Suspended/Cancelled/etc.                                 |
| multi_tenant_group | UUID, NULL  | For groupings (e.g., franchise or aggregator ownership).        |
| created_at         | Timestamp   | Creation time.                                                  |
| updated_at         | Timestamp   | Last modification time.                                         |
| metadata           | JSONB       | Extensibility for custom tenant configs.                        |

### `tenant_settings`
Settings or customization overrides per tenant (billing rules, localization defaults, payment provider links, etc.).

---

## Users, Roles, and Identity

### `user`
**Purpose:** All persons who interact with the system (passengers, drivers, operators, managers, admins, etc.). Uniqueness is enforced by phone number, with support for UUID/email and external (OAuth/social) identities.

| Column        | Type      | Description                                                        |
|---------------|-----------|--------------------------------------------------------------------|
| id            | UUID      | Primary key.                                                       |
| phone         | String    | Unique, cross-tenant and cross-role.                               |
| email         | String    | Optional; for alternatives/account recovery.                       |
| external_id   | String    | For OAuth/chatbot/etc. identities (Telegram/Viber/Facebook/etc.).  |
| name          | String    | Full name.                                                         |
| language      | String    | Preferred language (i18n).                                         |
| avatar_url    | String    | User picture.                                                      |
| verified      | Boolean   | Phone/email/origin verified.                                       |
| registered_at | Timestamp | Registration time.                                                 |
| last_login_at | Timestamp | For support/auditing.                                              |
| metadata      | JSONB     | Arbitrary user profile extensions.                                 |

### `user_tenant`
**Purpose:** Mapping table for user membership, roles, and status in a given tenant. Enables multi-role, multi-organization logic.

| Column             | Type      | Description                                        |
|--------------------|-----------|----------------------------------------------------|
| id                 | UUID      | Primary key.                                       |
| user_id            | UUID      | FK → user.id                                       |
| tenant_id          | UUID      | FK → tenant.id                                     |
| role               | Enum      | passenger/driver/operator/manager/admin/support    |
| status             | Enum      | active/inactive/banned/suspended                   |
| is_primary         | Boolean   | If this is user’s default/primary company.         |
| last_used          | Timestamp | When last interacted as this tenant                |
| registered_at      | Timestamp | When joined this tenant                            |
| invited_by_id      | UUID      | FK → user.id (inviter, if any)                     |
| metadata           | JSONB     | Extra data (license, badge, onboarding state, etc) |

### `role`
**Purpose:** System-wide roles (for permissions, as reference).

---

## Vehicles & Fleet Management

### `vehicle`
Represents a vehicle registered under a tenant.

| Column          | Type        | Description                                    |
|-----------------|-------------|------------------------------------------------|
| id              | UUID        | PK                                             |
| tenant_id       | UUID        | FK → tenant                                    |
| license_plate   | String      | Unique per country                             |
| make            | String      | Manufacturer/brand                             |
| model           | String      | Model                                          |
| color           | String      | Color                                          |
| year            | Integer     | Year of manufacture                            |
| registration_id | String      | National registration/insurance/etc            |
| status          | Enum        | active/disabled/decommissioned/repair etc.     |
| metadata        | JSONB       | For extension (accessibility, EV, notes, etc.) |
| created_at      | Timestamp   |                                                |

### `driver_vehicle`
Many-to-many mapping for driver<->vehicle relationships (shifts, assignments).

| Column      | Type    | Description                     |
|-------------|---------|---------------------------------|
| id          | UUID    | PK                              |
| driver_id   | UUID    | FK → user.id (with role=driver) |
| vehicle_id  | UUID    | FK → vehicle.id                 |
| from_time   | Timestamp | Shift start                    |
| to_time     | Timestamp | Shift end                      |

---

## Rides, Orders, Delivery

### `ride_order`
**Purpose:** Represents a ride (or delivery) transaction, whether booked via app, operator, or chatbot.

| Column            | Type      | Description                                       |
|-------------------|-----------|---------------------------------------------------|
| id                | UUID      | PK                                                |
| tenant_id         | UUID      | Service provider company                          |
| passenger_id      | UUID      | Could be NULL for anonymous/guest                 |
| driver_id         | UUID      | FK; assigned driver (null if searching/pooled)    |
| vehicle_id        | UUID      | FK; assigned vehicle (if assigned)                |
| pickup_address    | String    |                                                   |
| pickup_latlng     | Point     | Geolocation (PostGIS/geo type)                    |
| dropoff_address   | String    |                                                   |
| dropoff_latlng    | Point     | Geolocation                                       |
| scheduled_time    | Timestamp | Requested time (for pre-orders)                   |
| confirmed_time    | Timestamp | When accepted                                     |
| start_time        | Timestamp | When ride began                                   |
| end_time          | Timestamp | When ride ended                                   |
| status            | Enum      | searching/assigned/picked_up/completed/cancelled  |
| order_type        | Enum      | ride/delivery/pooled/shared/etc.                  |
| estimated_fare    | Numeric   | Quoted price                                      |
| currency          | String    | ISO 4217 code                                     |
| payment_method    | Enum      | card/cash/app wallet/etc.                         |
| paid              | Boolean   | Payment status                                    |
| promo_id          | UUID      | Linked promo (if any)                             |
| metadata          | JSONB     | Packages/notes/etc.                               |
| created_at        | Timestamp |                                                   |
| updated_at        | Timestamp |                                                   |

### `ride_event`
Logs ride/order state transitions, geolocation tracks, etc.

| Column      | Type      | Description             |
|-------------|-----------|-------------------------|
| id          | UUID      | PK                      |
| ride_id     | UUID      | FK → ride_order         |
| event_type  | Enum      | requested, assign, etc. |
| details     | JSONB     |                         |
| created_at  | Timestamp |                         |

---

## Payments & Billing

### `payment`
Payment transaction log (per ride/order, or wallet top-up, etc).

| Column        | Type      | Description                         |
|---------------|-----------|-------------------------------------|
| id            | UUID      | PK                                  |
| tenant_id     | UUID      | Company                             |
| ride_id       | UUID      | Associated ride/order               |
| user_id       | UUID      | Payer (passenger typically)         |
| amount        | Numeric   |                                     |
| currency      | String    |                                     |
| method        | Enum      | card/cash/wallet/etc.               |
| processor_ref | String    | External payment provider ref       |
| status        | Enum      | pending/success/failed              |
| processed_at  | Timestamp | If attempted                        |
| metadata      | JSONB     | For reconciliation/extensibility    |

### `wallet`
App-internal user currencies/balance.

### `invoice`
Billing for tenants (subscription, revenue share, etc).

---

## Communication: Messaging, Chatbots, PBX

### `message`
Chat history: App, chatbot, or web session.

| Column         | Type      | Description                           |
|----------------|-----------|---------------------------------------|
| id             | UUID      | PK                                    |
| tenant_id      | UUID      |                                       |
| chat_id        | UUID      | Conversation/group context            |
| from_user_id   | UUID      | Who sent                              |
| to_user_id     | UUID      | Recipient, if any                     |
| via_channel    | Enum      | web/app/telegram/viber/pbx            |
| type           | Enum      | text/image/voice/call/etc.            |
| content        | Text      | Message body/payload                  |
| metadata       | JSONB     | For media, attachments, bots, etc.    |
| created_at     | Timestamp |                                       |

### `chat`
Conversation context: one-on-one, group, support, ride chat, etc.

### `chatbot`
Integration registry: defines which bots (Telegram/Viber/etc.) are active for a tenant.

### `pbx_call`
PBX/call-center log: operator calls.

| Column        | Type      | Description                                    |
|---------------|-----------|------------------------------------------------|
| id            | UUID      | PK                                             |
| tenant_id     | UUID      | Company/operator                               |
| user_id       | UUID      | Initiator                                      |
| operator_id   | UUID      | FK (for inbound)                               |
| ride_id       | UUID      | Linked ride/order, if any                      |
| direction     | Enum      | inbound/outbound                               |
| status        | Enum      | ringing, answered, missed, ended, failed       |
| duration      | Integer   | In seconds, if ended                           |
| recording_url | String    | For archiving, if recorded                     |
| started_at    | Timestamp |                                                |
| ended_at      | Timestamp |                                                |

---

## Dispatch & Operators

### `dispatch_assignment`
Real-time mapping: which operator/dispatcher oversees which rides/orders.

### `operator_shift`
Operator work schedule, for reporting and support escalation.

---

## Geography, Zones, and Maps

### `zone`
Defined area (city, neighborhood, airport, etc).

| Column      | Type      | Description                  |
|-------------|-----------|------------------------------|
| id          | UUID      | PK                           |
| tenant_id   | UUID      | Company                      |
| name        | String    | Human-readable name          |
| area_geom   | Geometry  | Polygon (PostGIS)            |
| enabled     | Boolean   | Is this zone active?         |

### `map_provider`
For integration with OSM, MapLibre, Google, etc.  
Stores access tokens, preferred layers, per tenant.

---

## Promotions, Rewards, Ratings

### `promotion`
Discounts, codes, special offers.

| Column      | Type      | Description                   |
|-------------|-----------|-------------------------------|
| id          | UUID      |                               |
| tenant_id   | UUID      |                               |
| code        | String    | Promo code                    |
| type        | Enum      | Percentage, value, free ride  |
| value       | Numeric   | Amount/percentage             |
| usage_limit | Integer   | How many times can be used    |
| valid_from  | Timestamp |                               |
| valid_to    | Timestamp |                               |

### `ride_rating`
Passenger-to-driver and driver-to-passenger ratings with optional feedback.

| Column        | Type      | Description            |
|---------------|-----------|------------------------|
| id            | UUID      |                        |
| ride_id       | UUID      |                        |
| from_user_id  | UUID      |                        |
| to_user_id    | UUID      |                        |
| rating        | Integer   | 1-5 scale              |
| feedback      | Text      | Optional comment       |
| created_at    | Timestamp |                        |

---

## Support, Contact, Audit

### `support_ticket`
For user support/inquiries.

| Column         | Type      | Description            |
|----------------|-----------|------------------------|
| id             | UUID      |                        |
| tenant_id      | UUID      |                        |
| user_id        | UUID      |                        |
| subject        | String    |                        |
| details        | Text      |                        |
| status         | Enum      | open/closed/pending    |
| assigned_to_id | UUID      | FK → operator          |
| created_at     | Timestamp |                        |
| closed_at      | Timestamp |                        |

### `audit_log`
Tracks critical system/user actions for compliance and debugging.

| Column        | Type      | Description                    |
|---------------|-----------|--------------------------------|
| id            | UUID      |                                |
| actor_id      | UUID      | Who performed action           |
| actor_type    | Enum      | user/operator/system           |
| event_type    | String    | (e.g., login, delete_ride)     |
| target_table  | String    |                                |
| target_id     | UUID      |                                |
| description   | Text      | Details/context                |
| created_at    | Timestamp |                                |

---

## System Meta/Config & Extensibility

### `system_setting`
System-wide settings and flags (feature toggles, i18n, maintenance mode, etc.).

### `i18n_translation`
Key-value pairs for multi-locale support.

### `webhook_subscriber`
Webhook registries for tenant integration.

---

## Indexing, Enums, Scalability Notes

- **Multi-Tenancy:**  
  Unless otherwise noted, all “actionable” tables include a `tenant_id` column as a foreign key and index. Strict isolation per tenant is enforced at all query and business logic layers.
- **Indexes:**  
  Indexes are essential on all keys (user_id, tenant_id, status, time fields), unique constraints (license_plate, phone, email per tenant), and compound keys (user+tenant+role) as appropriate.
- **UUID:**  
  All primary keys are UUID unless dictated otherwise for external compatibility.
- **ENUMs:**  
  Use enums for all status and type fields with explicit documentation of allowed values.
- **Scalability/Partitioning:**  
  Sharding/partitioning can occur on tenant_id or time fields for very large deployments. Drizzle/Postgres can handle these patterns.
- **Extensibility:**  
  All major entities include a `metadata` (JSONB) field for custom per-tenant or per-entity settings/extensions beyond the core schema.
- **Audit/Compliance:**  
  Timestamps (created_at, updated_at, deleted_at) are present on all mutable tables for full tracking; consider soft deletes where needed for compliance.
- **Localization/I18n:**  
  The platform supports Macedonian, Albanian, English, and extensible locale entries.
- **Map Provider/Location:**  
  Geographical fields (pickup/dropoff, zone.area_geom) are PostGIS/geometry-enabled.
  
---

## Conclusion

This schema blueprint delivers complete coverage of core and advanced SaaS ride-hailing/delivery/tracking features, supports PBX/chatbot/messaging workflows, and offers safe boundaries for multi-tenant operation, billing, and extensibility. It is designed for frictionless implementation with Drizzle ORM and scalable Postgres deployments.