# Order Table Relationship Matrix  
**Taxi/Delivery Platform: Unified Order/Domains Mapping Reference**

---

## Introduction


## Table of Contents

1. [Core Order Domain Tables](#core-order-domain-tables)
2. [Relationship Mapping Matrix](#relationship-mapping-matrix)
3. [Entity-Relationship Diagram](#entity-relationship-diagram)
4. [Detailed Relationship Analysis](#detailed-relationship-analysis)
5. [Gaps, Weaknesses & Adjustments](#gaps-weaknesses--adjustments)
6. [Conclusion](#conclusion)

---

## Core Order Domain Tables

| Table         | Description                                     |
|---------------|-------------------------------------------------|
| ride_order    | Main transactional ride/delivery/order record   |
| ride_event    | State changes, audit trail, geo-track, status   |
| payment       | Payment log (per order or wallet transaction)   |
| pbx_call      | PBX/call-center call linked to order            |
| pbx_bridge    | Privacy (bridge/masking) links to order         |

---

## Relationship Mapping Matrix

| Domain / Relation          | FK / Linking Field(s)                                              | Indexed? | Privacy / Audit            | Domain-specific Notes                                                                | Recommendations / Gaps         |
|---------------------------|--------------------------------------------------------------------|----------|----------------------------|--------------------------------------------------------------------------------------|-------------------------------|
| **Passenger/User**        | `passenger_id` → user.id<br>`user_id` in user_tenant<br>Indirect: via chatbot_user/session/message | Yes      | Full KYC, consent, verified, history in user_profile_history | Nullable for guest/anonymous; phone/SMS verification tied to order flow and bot flows | Ensure audit trail for all onboarding/consent transitions |
| **Driver**                | `driver_id` → user.id (role=driver)<br>via user_tenant, ride_event | Yes      | Shift assignment/audit, history, KYC | Multiple/pooled assignments managed via driver_vehicle table         | Reference driver_vehicle for rotation/shift edge cases |
| **Vehicle**               | `vehicle_id` → vehicle.id                                          | Yes      | Full audit, assigned/pooled tracked | Surplus/multiple assignment via driver_vehicle mapping                | Pool/rotation pattern to be surfaced in applications     |
| **Tenant**                | `tenant_id` across all actionable tables                           | Yes      | Row-level security partition; full isolation | Grouped/aggregator patterns supported via multi_tenant_group         | Encourage strict tenant_id enforcement and RLS           |
| **Operator (Dispatcher)** | Indirect: operator_id in pbx_call/pbx_cdr, pbx_operator<br>dispatch_assignment table                | Yes      | Shift/audit trail; linked onboarding       | Handler of call-initiated or escalated orders; operator assignments auditable    | Ensure clear indexing on operator assignment & shift     |
| **Chatbot/Session/Message**| Indirect: chatbot_user → user; chatbot_session → chatbot_user/order<br>order_id in pbx_sms/message/event | Yes | Consent, phone verification at every stage; message audit | User/channel/locale mapping critical for automation, support flows | Consider direct order_id on chatbot_session/message (for reporting/ref) |
| **PBX Call / CDR**        | pbx_call, pbx_cdr: `order_id`, `ride_id`, `driver_id`, `operator_id`, `user_id`         | Yes      | Masking, privacy flags, event chain        | CDR links every PBX/telephony event, bridge flows, regulatory logs   | All FKs present; ensure usage is referenced where ride initiated from PBX |
| **Bridge (Privacy/Mask)** | pbx_bridge: `order_id`, `user_id`, `driver_id`, `bridge_id` in CDR | Yes      | Expiry, masking, audit-rigorous            | Temporary link, explicit expiry, privacy consent tied to user/profile| Recommend surfacing bridge details in ride UI for support/audit        |
| **Status/History**        | ride_event: `ride_id` → ride_order.id                              | Yes      | Each event has timestamp, type, audit      | Status transitions, geo, notifications                                  | Sufficient, ride_event covers all transitions           |
| **Price/Payment**         | payment: `ride_id` → ride_order.id; `estimated_fare`, `paid` fields in ride_order       | Yes      | Transactional audit, reconciliation        | Allow partial/failed payments, refund logic via status/history         | Sufficient for all normal payment scenarios             |
| **Promo/Campaign**        | `promo_id` in ride_order; separate promotion events                | Yes      | Usage rules, promotion eligibility         | Per-tenant, per-user, per-ride audit; links back for reporting         | Sufficient; recommend full usage reporting flow          |
| **Geographic/Zone**       | `pickup_latlng`, `dropoff_latlng` in ride_order;<br>zone_id via spatial query          | Yes (geo)| Pickup/dropoff, geo-audit, zone isolation  | Point type; spatial indexing for pricing, regulation, city coverage    | Sufficient; future ZOI mapping may be indexed           |
| **Metadata/Extensibility**| `metadata` (JSONB) in almost all tables                            | Yes      | Custom extensions/PII controls             | Use for custom/tenant-specific fields, proof of future-resilience      | Always validate at the app layer                        |

---

## Entity-Relationship Diagram

```mermaid
erDiagram
    USER ||--o{ RIDE_ORDER : books
    RIDE_ORDER }o--|| TENANT : per_order
    RIDE_ORDER }o--|| DRIVER : assigned
    RIDE_ORDER }o--|| VEHICLE : vehicle_for_ride
    RIDE_ORDER ||--o{ RIDE_EVENT : has_history
    RIDE_ORDER ||--o{ PAYMENT : has_payment
    RIDE_ORDER ||--o{ PBX_CALL : has_pbx_call
    RIDE_ORDER ||--o{ PBX_BRIDGE : has_privacy_bridge
    RIDE_ORDER }o--|| PROMOTION : uses_promo
    RIDE_ORDER ||--o{ CHATBOT_SESSION : chatbot_session
    VEHICLE }o--|| TENANT : owned_by_tenant
    DRIVER }o--|| USER : is_user
    OPERATOR ||--o{ PBX_CALL : handles_call
    ZONE ||--o{ RIDE_ORDER : is_in_zone
```

---

## Detailed Relationship Analysis

### Example (Partial Table, Shows Format):

| Relationship       | FK / Linking Field(s) | Indexed? | Privacy/Audit Coverage         | Domain-specific Notes          | Recommendations  |
|--------------------|----------------------|----------|-------------------------------|-------------------------------|------------------|
| Passenger/User     | passenger_id (ride_order), user_id (user_tenant),... | Yes | KYC, consent, profile/audit | Nullable for guest/anonymous  | Ensure consent cross-links for automation |
| Driver             | driver_id (ride_order), via user_tenant           | Yes | Assignment, ride_event trace | Pool/shift candidate         | Document shift patterns             |

...  
*See Matrix Above for Complete Per-Domain Analysis. Full write-up includes a row for each enumerated relationship, and highlights:*
- Whether the link is direct (FK on ride_order) or indirect (multi-step path; e.g. chatbot → session → message → user → order).
- The indexes (explicit or inherited from referenced table).
- Privacy or audit trail measures (GDPR, masking, right-to-be-forgotten).
- Any extensibility fields (metadata, JSONB, consent flags).

---

## Gaps, Weaknesses & Adjustments

### Identified Opportunities for Refinement

- **Chatbot/Session linkage:** Consider adding `order_id` as an optional field in `chatbot_session` and/or `chatbot_message` for direct reference and easier reporting, especially in high-automation bot flows (currently, linkage is only indirect via user/message context).
- **Operator mapping:** Ensure shift assignment and per-order operator visibility is always traceable, especially for support/audit requests; index assignment events and PBX flows robustly.
- **Bridge/Privacy flows:** Privacy/bridge linkage (order, driver, user) is strong; recommend showing bridge state/expiry in admin/support dashboards for post-hoc audits.
- **Extensibility:** Ensure all `metadata` fields are typed/validated at the app layer to avoid privacy/data-loss risks; flag per-table extensibility in data catalog.
- **Promotion usage flows:** Consider periodic sanity/audit checks for promo application (abuse/eligibility audit).
- **Geo/Zone mappings:** For large cities/regions, consider precomputing zone overlays for fast lookup, or more granular spatial indexing if heavy city-based regulation is to be introduced.

---

## Conclusion

The above matrix and explanations provide a comprehensive, auditable, and extensible mapping of all relevant order table relationships across the multi-tenant taxi and delivery platform. Direct and indirect domain connections, privacy and audit requirements, and edge-case flows (chatbot, PBX, bridge, operator) are all referenced, with practical recommendations for future-proofing and compliance at scale. This document should be kept up to date as the schema evolves and new product/regulatory needs arise.




# Order Table Relationship Matrix  
**Taxi/Delivery Platform: Unified Order/Domains Mapping Reference**

---

## Introduction

This document serves as a comprehensive checklist and reference for all direct and indirect relationships between the ride/delivery `order` table(s) and other major platform domains. It confirms coverage of required foreign keys, indices, privacy/audit needs, and extensibility—tying together the multi-modal, multi-tenant business flows (chatbot, PBX, operator, mobile, etc.).

---

## Relationship Matrix

| Feature/Domain       | FK/Link [Type]        | Indexed | Privacy/Audit        | Notes / Table           |
|----------------------|-----------------------|---------|----------------------|-------------------------|
| **Passenger/User**   | passenger_id [UUID]   | ✔       | ✔ (PII/consent)      | user, phone verification, user_tenant_role |
| **Driver**           | driver_id [UUID]      | ✔       | ✔ (scoped/PI)        | driver assigned, driver stats             |
| **Vehicle**          | vehicle_id [UUID]     | ✔       | -                    | Only if assigned, driver_vehicle          |
| **Tenant**           | tenant_id [UUID]      | ✔       | ✔ (isolation)        | Scopes all queries, RBAC                  |
| **Operator**         | operator_id [UUID]    | ✔       | ✔ (operator audit)   | If PBX/inbound call, from pbx_operator    |
| **Chatbot/Session**  | chatbot_session_id [UUID], chatbot_message_id [UUID] | ✔ | - | For chatbot-initiated orders          |
| **PBX/CDR**          | pbx_cdr_id [UUID]     | ✔       | ✔ (call trace)       | If PBX-originated/order                   |
| **Bridge**           | pbx_bridge_id [UUID]  | ✔       | ✔ (privacy masking)  | When privacy bridge is used (call/message)|
| **Order Status/History** | order_status [ENUM], ride_event/log/history [UUID/ref] | ✔ | ✔ (change/audit) | All transitions, driver/passenger views   |
| **Payment/Billing**  | payment_id [UUID], invoice_id [UUID], paid [bool] | ✔ | ✔ | Links to payment domain, audit trails     |
| **Promo/Campaign**   | promo_id [UUID]       | ✔       | - | Applied promo, code, campaign logic |
| **Geo/Zone**         | pickup/dropoff_geoid [UUID or geo], zone_id [UUID]  | ✔ | - | Geographic linkage (for reporting, serve zones) |
| **Extensibility**    | metadata [JSONB]      | -       | -                    | For custom integration, future needs      |

---

## Entity Relationship Diagram (mermaid)

```mermaid
erDiagram
    USER ||--o{ ORDER : places
    DRIVER ||--o{ ORDER : fulfills
    TENANT ||--o{ ORDER : owns
    OPERATOR ||--o{ ORDER : manages
    CHATBOT_SESSION ||--o{ ORDER : triggers
    PBX_CDR ||--o{ ORDER : incoming
    VEHICLE ||--o{ ORDER : assigned
    BRIDGE ||--o{ ORDER : privacy_masks
    PAYMENT ||--o{ ORDER : settles
    PROMO ||--o{ ORDER : discounts
    ZONE ||--o{ ORDER : geo
```

---

## Per-Feature Analysis & Notes

### User/Passenger
- FK: `passenger_id` — always present; verified by phone or messaging platform.
- privacy: PII is access-controlled; only non-sensitive stats shown to role-limited (e.g. driver) users.

### Driver
- FK: `driver_id` — present if/when assigned (nullable for pooled/unassigned orders).
- Driver stats, assignments, and vehicle linkage tracked via driver_assignment or driver_vehicle tables.

### Vehicle
- FK: `vehicle_id` — present if relevant (standard for classic ride; optional for delivery).

### Tenant
- FK: `tenant_id` — required for all records, indexed for strict isolation.

### Operator
- FK: `operator_id` — set when order is operator/PBX-initiated; ties to audit and user action logs.

### Chatbot/Session/Message
- FK: `chatbot_session_id` and/or `chatbot_message_id` — only for chatbot-origin orders.

### PBX/CDR
- FK: `pbx_cdr_id` — only for call-center/voice/SMS initiated orders; covers incoming/outbound logic.

### Bridge (Privacy)
- FK: `pbx_bridge_id` — for privacy-protected user-driver communication.
- Implements time-limited, permission-traced, CC-traced call/message masking.

### Status/History
- Status ENUM plus ride_event or order_status_log/history for all status changes, assignment, and transitions.

### Payment/Billing
- FKs: payment_id, invoice_id (multiple payments possible via mapping table).
- paid [bool] — immediate reporting.

### Promo/Campaign
- promo_id — links if any applied; allow for null if none.

### Geo/Zone
- pickup/dropoff_address [string]; pickup_latlng/dropoff_latlng [point/geo]; zone_id may link to serviced zones for reporting/pricing.

### Extensibility
- metadata [jsonb] in order table stores all non-standard attributes and future fields.

---

## Coverage & Recommendations

- **All required relationships are present, indexed, and cross-referenced to their owning domain tables.**
- **Audit/Traceability:** All cross-channel/integrated orders (voice, chatbot, app) can be tracked from origin (incl. operator, session, call, message); status/history fully auditable.
- **Privacy:** Sensitive references (user_id, phone) are isolated, masked, or role-filtered per RBAC/visibility logic.
- **Future-Proofing:** metadata field ensures additional domains (custom partners, campaign types, regulatory fields) can be added without migration.

**Recommendations:**  
- If needed for analytics, consider linking explicit fulfillment events (driver started, arrived, completed) via separate ride_event/fulfillment_event tables.
- To optimize for regulatory use, always index critical FKs (user/operator/driver/vehicle/tenant/order), and timestamp/order events for reporting.
- If supporting multi-modal delivery (bike, parcel, pooled ride), add union table with m2m mapping to relevant extended objects.

---

## Conclusion

The platform's order table(s) are now linked to every critical domain. All FK, audit, privacy, and extensibility needs are covered and indexed—ensuring coherent, auditable, and high-performance business/reporting flows for all integrated ride and delivery operations.