# Simple and reliable multi-stage build for main-api
FROM oven/bun:1 AS base
WORKDIR /app

# Copy workspace configuration
COPY package.json bun.lock bun-workspace.yaml ./
COPY apps/main-api/package.json ./apps/main-api/
COPY packages/db/package.json ./packages/db/
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY apps/dashboard/package.json ./apps/dashboard/
COPY apps/mini-app/package.json ./apps/mini-app/

# Install all dependencies with workspace support
RUN bun install

# Copy source code
COPY . .

# Build the database package first
RUN cd packages/db && bun run build

# Verify workspace packages are linked correctly
RUN ls -la node_modules/@monorepo/

# Build the main-api application
RUN cd apps/main-api && bun run build

# Production stage
FROM oven/bun:1-alpine AS production
WORKDIR /app

# Copy built application
COPY --from=base /app/apps/main-api/dist ./dist
COPY --from=base /app/apps/main-api/package.json ./package.json

# Copy built database package
COPY --from=base /app/packages/db/dist ./packages/db/dist
COPY --from=base /app/packages/db/package.json ./packages/db/package.json

# Copy root package.json and bun.lock for workspace resolution
COPY --from=base /app/package.json ./root-package.json
COPY --from=base /app/bun.lock ./bun.lock

# Install only production dependencies
RUN bun install --production --frozen-lockfile

# Create non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup
USER appuser

ENV NODE_ENV=production
EXPOSE 3004

CMD ["bun", "run", "start"]
