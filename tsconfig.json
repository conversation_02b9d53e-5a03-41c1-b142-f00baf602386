{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "declaration": true, "baseUrl": ".", "paths": {"@monorepo/db": ["packages/db/src/index.ts"], "@monorepo/db/*": ["packages/db/src/*"], "@repo/typescript-config/*": ["packages/typescript-config/*"]}}, "exclude": ["node_modules", "**/dist", "**/.next", "**/out"]}