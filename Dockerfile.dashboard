FROM oven/bun:latest AS base

# 1. Create a workspace for the dashboard app
WORKDIR /app

# 2. Copy only necessary package.json and lock files for dependency installation
# This leverages <PERSON><PERSON>'s layer caching more effectively.
# Copy root package.json and lockfile
COPY package.json bun.lock ./ 
# Copy dashboard specific package.json (if it exists and is used for its own deps)
COPY apps/dashboard/package.json ./apps/dashboard/
# If dashboard has its own lockfile, copy that too:
# COPY apps/dashboard/bun.lock ./apps/dashboard/

# 3. Install dependencies - scoped if possible, or root install
# If dashboard has its own deps primarily:
# RUN cd apps/dashboard && bun install --production --frozen-lockfile
# Or if you rely on root install:
RUN bun install --production --frozen-lockfile # Installs for the whole monorepo

# 4. Copy the rest of the monorepo source code
# (or better, just the dashboard app's source and any shared packages it needs)
COPY . .

# 5. Build the dashboard app (if it has a build step for production)
# Ensure your dashboard's package.json has a "build" script: "next build"
WORKDIR /app/apps/dashboard 
RUN bun run build # e.g., next build

# 6. Set up the final run stage
# Use a fresh stage from base if needed, or continue from build stage
FROM base AS final 
WORKDIR /app/apps/dashboard

# Copy built artifacts from the build stage and necessary node_modules
# This depends on how Next.js structures its output with Bun.
# Typically, you'd copy the .next folder (standalone output if configured), public, package.json.
# Assuming Bun build outputs to .next
COPY --from=0 /app/apps/dashboard/.next ./.next 
COPY --from=0 /app/apps/dashboard/public ./public
COPY --from=0 /app/apps/dashboard/package.json ./package.json
# If you did a scoped install earlier, you might need to copy node_modules
# COPY --from=0 /app/apps/dashboard/node_modules ./node_modules
# Or if relying on root node_modules and they are needed for runtime:
# COPY --from=0 /app/node_modules ./node_modules # This can make image large

ENV NODE_ENV=production
EXPOSE 3000

# Command to run the dashboard app
# Ensure your dashboard's package.json has a "start" script: "next start"
CMD ["bun", "run", "start"] 