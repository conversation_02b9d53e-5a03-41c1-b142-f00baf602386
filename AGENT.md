# AGENT.md

## Build/Test/Lint Commands
- Build: `bun run build` (all apps), `turbo run build`
- Test: `bun run test` (dashboard), `turbo run test`
- Test single file: `bun test test/example.test.ts` (dashboard), `jest --testNamePattern="TestName"` (main-api)
- Lint: `bun run lint`, `turbo run lint`
- Format: `bun run format:biome:fix`, `turbo run format`
- TypeCheck: `bun run typecheck`, `turbo run typecheck`
- DB: `bun run db:generate`, `bun run db:migrate`, `bun run db:push`, `bun run db:studio`

## Code Style
- **Formatter**: Biome with tabs, 80 char width, double quotes
- **Imports**: Use `import type` for types, organize imports enabled
- **Types**: Strict TypeScript, no explicit any allowed, use union types
- **Naming**: PascalCase for enums/tables, snake_case for DB columns, camelCase for variables
- **Error <PERSON>ling**: Use proper TypeScript error types, no unused imports
- **Database**: Drizzle ORM with pgEnum pattern, always pair with TypeScript types

## Project Structure
- Monorepo with Turbo, Bun package manager
- Apps: main-api (NestJS), dashboard (Next.js), mini-app
- Packages: db (Drizzle), typescript-config, eslint-config
- Testing: Jest (main-api), Bun test (dashboard)
- Path aliases: `@monorepo/db`, `@repo/typescript-config`