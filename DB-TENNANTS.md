# Multi-Tenant System Documentation

## Overview

The taXi platform implements a robust multi-tenant architecture that enables multiple organizations (tenants) to operate on a shared infrastructure while maintaining strict data isolation. This document outlines the tenant system design, database organization, role-based access control, and how tenants are distributed across the application.


















## Database Organization

### Core Tenant Tables

1. **`tenant`** - The foundation of the multi-tenant system
   - `id` (UUID): Primary identifier for each tenant
   - `name`: Public-facing brand name
   - `legal_name`: For compliance and invoicing
   - `email`, `phone`: Contact information
   - `logo_url`: Branding assets
   - `address`, `country`: Location information
   - `timezone`: Default for reporting and scheduling
   - `plan`: Controls SaaS features and pricing tier
   - `status`: ACTIVE/SUSPENDED/CANCELLED
   - `multi_tenant_group`: Optional link to parent group (for franchises/brands)
   - `metadata`: JSONB for extensibility and custom settings

2. **`multi_tenant_group`** - Supports franchise/brand hierarchies
   - `id` (UUID): Unique group identifier
   - `name`: Display name for the group
   - `type`: Franchise, aggregator, brand, corporate, etc.
   - `parent_group_id`: Enables nested group hierarchies
   - `settings`: Group-level settings that can override tenant defaults
   - `status`: ACTIVE/PENDING/INACTIVE

3. **`tenant_settings`** - Configuration for each tenant
   - `tenant_id`: Link to the tenant
   - `key`: Setting identifier (e.g., "billing.mode", "default_locale")
   - `value`: JSONB for flexible configuration storage
   - `type`: Classifies setting (SYSTEM, PAYMENT, I18N, UI, etc.)

4. **`tenant_billing_profile`** - Billing information
   - `tenant_id`: Link to the tenant
   - `billing_email`, `billing_phone`: Invoice delivery
   - `tax_id`: For VAT or fiscal compliance
   - `currency`: Default for financial operations
   - `payment_method`: Preferred payment method

5. **`tenant_localization`** - Language and regional settings
   - `tenant_id`: Link to the tenant
   - `default_locale`: ISO code for primary language
   - `supported_locales`: Available languages
   - `timezone`: For local UX
   - `labels`: Custom branding text for UI, chatbot, receipts

### Bot System Tables

1. **`bots`** - Telegram bot configurations
   - `id` (UUID): Primary identifier
   - `tenant_id`: Link to the tenant
   - `token`: Encrypted bot token
   - `webhook_path_segment`: Unique path for webhook
   - `bot_username`: Telegram username
   - `is_enabled`: Active status flag
   - `config`: JSONB for bot-specific settings

### Role-Based Access Control

1. **`roles`** - Defines permissions and access levels
   - `id` (UUID): Primary identifier
   - `tenant_id`: Link to the tenant (null for global roles)
   - `name`: Role name (e.g., 'admin', 'driver', 'passenger')
   - `permissions`: JSONB array of granular permissions
   - `is_default`: Whether this is a default role for new users
   - `is_system`: Whether this is a core system role

2. **`user_bot_roles`** - Maps users to roles within a specific bot context
   - `telegram_user_id`: Telegram user identifier
   - `bot_id`: Link to the bot
   - `role_id`: Link to the role

## Core Roles and Privileges

| Role          | Privileges                                          | Visibility                  | Domain Functions                                                           |
|---------------|-----------------------------------------------------|----------------------------|---------------------------------------------------------------------------|
| Passenger     | Order/ride booking, track own rides, feedback       | Ride stats, anonymized     | Place order, see driver stats/successful rides, rating, partial feedback   |
| Driver        | Accept rides, vehicle mgmt, update status, history  | Own orders, vehicle        | Driver profile, stats, own dashboard, can see passenger anonymized stats   |
| Dispatcher    | Answer calls, assign rides, interact via PBX/chat   | Driver/passenger order     | Dashboard, PBX endpoint, call log, queue mgmt, escalation                  |
| Manager       | Asset view, operator stats, financial/performance   | Team asset, ride stats     | Analytics, vehicle pool, operator manage/reports, limited user/ride access |
| Tenant Admin  | All user/asset mgmt, assign roles, global audit     | Tenant-wide                | Add/remove all users/roles, finance, full dashboard, compliance reporting  |

Additional roles can be created for specific tenant needs, such as Franchise Owner or Brand Owner.

## Tenant Distribution and Context Propagation

### Bot Context System

The multi-tenant system integrates with the Telegram bot framework through a context-aware architecture:

1. **Context Derivation**:
   - Each incoming Telegram update is associated with a specific bot
   - The bot is linked to a tenant through the `tenantId` field
   - The `derive()` function enriches the context with tenant information:
     ```typescript
     // Simplified example
     const derivedProps = {
       tenant: { id: botConfig.tenantId, name: tenantRecord.name },
       botInfo: { id: botConfig.id, username: botConfig.botUsername },
       tenantBotApiClient: tenantApiClient,
       userRoles: [...] // User's roles for this tenant/bot
     };
     ```

2. **Context Types**:
   - `AppGlobalDeriveExtensions`: Defines tenant-specific properties added to context
     ```typescript
     interface AppGlobalDeriveExtensions {
       readonly t: (key: string, args?: Record<string, any> | any[], options?: { fallback?: string }) => string;
       tenant?: TenantInfo;
       botInfo?: BotInstanceInfo;
       tenantId?: string;
       userRoles?: string[];
       tenantBotApiClient?: GramioBot<AppBotErrorDefinitions, AppDeriveShape>['api'];
     }
     ```
   - Various context types (AppMessageContext, AppCommandContext, etc.) inherit these tenant properties

### Tenant Isolation Enforcement

1. **Database Level**:
   - `tenant_id` is present on all actionable tables (user, vehicle, ride, payment, etc.)
   - All queries are tenant-scoped using this field
   - Postgres Row-Level Security (RLS) can be used for enterprise deployments

2. **Application Level**:
   - Service methods validate tenant context before operations
   - API endpoints check tenant permissions
   - Bot handlers use tenant context for proper data scoping

3. **Bot Processing Service**:
   - Manages tenant-specific bot instances
   - Routes updates to the correct tenant context
   - Maintains a map of tenant bot clients:
     ```typescript
     private tenantBotClients = new Map<string, GramioBot['client']>();
     private activeBotConfigs = new Map<string, BotConfigFromDB>();
     ```

## Multi-Tenant Group Hierarchies

The system supports complex organizational structures:

1. **Franchise Model**:
   - A parent `multi_tenant_group` can own multiple tenants
   - Each tenant maintains data isolation while inheriting group settings
   - Group-level analytics and reporting are possible while preserving tenant boundaries

2. **Brand Aggregation**:
   - Multiple brands can operate under a single parent group
   - Each brand (tenant) has its own configuration, users, and data
   - Shared resources and settings can be managed at the group level

## Extensibility

1. **JSONB Fields**:
   - `metadata` fields allow for tenant-specific extensions without schema changes
   - Custom fields, configuration, and experimental features can be added with minimal friction

2. **Role Customization**:
   - Tenants can define custom roles beyond the core system roles
   - Permissions can be granularly defined per tenant needs

3. **Localization**:
   - Each tenant can have its own language settings, currency, and branded text
   - UI labels and messages can be customized per tenant

## Best Practices

1. **Always scope queries by tenant_id**
2. **Use UUIDs for all primary and foreign keys**
3. **Index tenant_id and other frequently queried fields**
4. **Validate tenant context in all service methods**
5. **Implement proper error handling for tenant-related operations**
6. **Audit all tenant and role changes for compliance**

## Summary

The multi-tenant architecture provides a robust foundation for the taXi platform, enabling:

- Strict data isolation between organizations
- Flexible role-based access control
- Support for complex organizational hierarchies
- Extensibility for tenant-specific customizations
- Scalable infrastructure for growing businesses

This design ensures that each tenant operates in its own secure environment while benefiting from the shared infrastructure and services of the platform.