This is the exact same error as before with `emoji-regex` and the lockfile during the `bun install --frozen-lockfile` step in the `build-installer` stage (which uses the pruned manifests from `turbo prune`).

```
 => ERROR [main-api build-installer 4/4] RUN bun install --frozen-lockfile  0.3s
------
 > [main-api build-installer 4/4] RUN bun install --frozen-lockfile:
0.233 bun install v1.2.14 (6a363a38)
0.238 812 |     "@isaacs/cliui/string-width": [
0.238           ^
0.238 error: Failed to resolve prod dependency 'emoji-regex' for package '@isaacs/cliui/string-width'
0.238     at bun.lock:812:5
0.238 InvalidPackageInfo: failed to parse lockfile: 'bun.lock'
0.238 
0.238 warn: Ignoring lockfile
0.238 error: lockfile had changes, but lockfile is frozen
------
```

**Root Cause Analysis (Reconfirmed):**

The `turbo prune <app> --docker` command generates:
1.  `out/json/`: A set of `package.json` files for the target app and its local workspace dependencies.
2.  `out/bun.lock`: A **pruned** version of your root `bun.lock`, supposedly containing only the entries relevant to the packages in `out/json/`.

The error `Failed to resolve prod dependency 'emoji-regex' for package '@isaacs/cliui/string-width' at bun.lock:812:5` followed by `InvalidPackageInfo: failed to parse lockfile: 'bun.lock'` and then `warn: Ignoring lockfile` and finally `error: lockfile had changes, but lockfile is frozen` means:

*   The pruned `out/bun.lock` created by `turbo prune` is not perfectly consistent for `bun install --frozen-lockfile` when used with the pruned `package.json` files in `out/json/`. Specifically, it seems to have an issue resolving `emoji-regex` as needed by `@isaacs/cliui/string-width` based on the information *within that pruned lockfile*.
*   Because Bun finds this pruned lockfile problematic, it tries to ignore it and resolve from scratch using only the `package.json` files from `out/json/`.
*   This fresh resolution results in a dependency tree that *would* produce a different lockfile than the pruned `out/bun.lock` it was given.
*   Since `--frozen-lockfile` is active, this discrepancy causes the install to fail.

**This is a known challenge with lockfile pruning in monorepos for all package managers.** Sometimes the pruning isn't perfect, especially with complex dependency graphs or how different package managers handle peer dependencies or resolve versions from the root lockfile for a subset of packages.

**Solutions to Try:**

Since we're using the `oven/bun:1` image (which includes Node.js and npm) for the `build-installer` stage, the "npm: not found" issue from very early on should not be the direct cause here. The issue is the integrity/consistency of the *pruned* `bun.lock`.

**Option 1: Use the Original Root Lockfile in the `build-installer` Stage**

Instead of relying on Turborepo's pruned lockfile (which seems to be causing issues with Bun's strictness), try using the *original, unpruned root `bun.lock`* alongside the pruned `package.json` files from `out/json/`. Bun should be able to use the full lockfile to resolve the dependencies for the subset of packages defined in `out/json/`.

```dockerfile
# /Dockerfile.main-api-turbo (Monorepo Root)

# --- Base Stage ---
FROM oven/bun:1 AS base 
WORKDIR /app

# --- Pruning Stage ---
FROM base AS pruner
RUN bun install -g turbo@latest 
COPY . . # Copy full monorepo
RUN turbo prune main-api --docker 
# Output: ./out/json/ (package manifests), ./out/full/ (source code), ./out/bun.lock (PRUNED lockfile)
# We will also need the ORIGINAL root bun.lock from this stage's context.

# --- Build-Installer Stage ---
# Installs ALL dependencies (dev and prod) for the PRUNED set of package.json files,
# but uses the ORIGINAL root lockfile for resolution.
FROM base AS build-installer 
WORKDIR /app
# Copy the pruned package.json files (these define WHAT to install for the target app + its deps)
COPY --from=pruner /app/out/json/ .
# Copy the ORIGINAL, UNPRUNED, root bun.lock from the pruner stage's initial context
COPY --from=pruner /app/bun.lock ./bun.lock  # <<< KEY CHANGE: Use original root lockfile
RUN bun install --frozen-lockfile
# This tells Bun: "Install dependencies for the package.json files now in /app,
# but ensure all versions and resolutions strictly match what's in the original root bun.lock."

# --- Actual Builder Stage ---
FROM base AS actual-builder
WORKDIR /app
COPY --from=build-installer /app/node_modules ./node_modules
COPY --from=pruner /app/out/full/ . # Copy pruned full source

ENV NODE_ENV=production
WORKDIR /app/apps/main-api 
RUN bun run build 

# --- Production Dependencies Finalizer Stage ---
FROM base AS prod-deps-finalizer
WORKDIR /app
# Copy the pruned package.json files again
COPY --from=pruner /app/out/json/ .
# Copy the ORIGINAL, UNPRUNED,This root bun.lock again
COPY --from=pruner /app/bun.lock ./bun.lock # <<< KEY CHANGE: Use original root lockfile
RUN bun install --production --frozen-lockfile
# This installs only production dependencies for the packages in out/json,
# respecting the original root lockfile.

# --- Final Runner Stage ---
FROM oven is the exact same error as before, specifically:

```
 => ERROR [main-api build-installer 4/4] RUN bun install --frozen-lockfile  0.3s
------
 > [main-api build-installer 4/bun:1-alpine AS runner
WORKDIR /app
ENV NODE_ENV=production

RUN addgroup -S nonroot && add/4] RUN bun install --frozen-lockfile:
0.233 bun install v1.2.1user -S nonroot -G nonroot

COPY --from=prod-deps-finalizer /app/node_modules ./node_modules
COPY --from=actual-builder /app/apps/main-api/dist ./dist
COPY --from=pruner /4 (6a363a38)
0.238 812 |     "@isaacs/cliui/string-width": [
0.238           ^
0.238 error: Failed to resolve prodapp/out/full/apps/main-api/package.json ./package.json
COPY --from= dependency 'emoji-regex' for package '@isaacs/cliui/string-width'
0.238     at bun.lock:812:5
0.238 InvalidPackageInfo: failed to parse lockfile: 'bun.lock'
0prod-deps-finalizer /app/bun.lock ./bun.lock # Copy the root lockfile

USER.238 
0.238 warn: Ignoring lockfile
0.238 error: lockfile had changes, but lockfile is frozen
------
failed to solve: process "/bin/sh -c bun install --frozen-lockfile" did not complete successfully: nonroot
EXPOSE 3004
CMD ["bun", "run", "start:prod"]
```

**Explanation of the Key Change in Option 1:**

*   In both `build-installer` and `prod-deps-finalizer` stages, we now do:
    `COPY --from=pruner /app/bun.lock ./bun.lock`
 exit code: 1
```

**The Problem Persists:**

The issue is still that the `bun.lock    This copies the *original root `bun.lock`* (which was present in the `pruner` stage` file generated by `turbo prune main-api --docker` (and copied into the `build-installer` stage as `/app/bun.lock`) is not perfectly consistent with the pruned set of `package.json` files (copied after `COPY . .`) instead of `COPY --from=pruner /app/out/bun.lock ./bun from `/app/out/json/` into `/app/` in the `build-installer` stage) when `bun install --frozen-.lock` (which was the pruned one).
*   The idea is that `bun install` (with or without `--productionlockfile` tries to validate it.

Specifically, it's struggling to resolve `emoji-regex` as a dependency for `@`) when given a set of `package.json` files (from `out/json/`) and the *complete originalisaacs/cliui/string-width` based on the information in the pruned `bun.lock`. This leads Bun to want to make changes to the lockfile, which `--frozen-lockfile` prevents.

**Why this is lockfile* should be able to resolve and install the correct subset of dependencies without finding inconsistencies in the lockfile itself.

**Option 2: If Option 1 still fails, try without `--frozen-lockfile` in the `build-installer` ( Tricky with `turbo prune` and `bun install`:**

*   **Lockfile Pruning Imperfections:** `turbo prune` does its best to create a minimal lockfile. However, the dependency resolution logic of the package manager (`bun` in this case) isfor dev deps) and `prod-deps-finalizer` stages (Risky for reproducibility)**

This is generally complex. Sometimes the pruned lockfile isn't a perfect subset that `bun install --frozen-lockfile` can use not recommended for CI/production builds because it can lead to non-reproducible builds if a dependency of a dependency gets without wanting to make adjustments, especially with transitive dependencies or peer dependency resolutions.
*   **Bun's Stricter Lockfile Handling ( a patch update, etc. However, it can help identify if the lockfile itself (even the root one) hasPotentially):** Bun might be more sensitive to these minor inconsistencies in the pruned lockfile than Yarn v1 (which the some subtle inconsistency with the current `package.json` files that `turbo prune` isn't handling.

```dockerfile
# In build-installer stage:
RUN bun install # REMOVE --frozen-lockfile

# In prod-deps-finalizer Turborepo Docker guide often implicitly targets with `yarn install`).

**Alternative Strategies for the `build-installer` Stage (and `prod-deps-finalizer`):**

Since directly using the pruned lockfile with `bun install --frozen-lockfile stage:
RUN bun install --production # REMOVE --frozen-lockfile
```
If this works, it` on the pruned `package.json` set is problematic, let's try approaches that are generally more robust in means your root `bun.lock` was slightly out of sync with what `bun install` would resolve based on the ( monorepos.

**Strategy A: Install from the Root using `--filter` (If Bun supports it well for this scenariopruned) `package.json` files. After this build succeeds, a new `bun.lock` would have)**

This is not explicitly shown in the Turborepo guide for the `--docker` output but is a common monorepo pattern. The idea is to use the *original root* `bun.lock` and tell Bun to install dependencies been generated *inside the Docker stage*. You would then want to get that lockfile out, commit it, and then try again with `--frozen-lockfile`. This is a debugging step.

**Option 3: Simplify the " only for the pruned workspaces.

```dockerfile
# /Dockerfile.main-api-turbo (Monorepo Root)

FROM oven/bun:1 AS base 
WORKDIR /app

# --- Pruning Stage ---
FROM base AS pruner
Production Dependencies" step in the final image**

If `prod-deps-finalizer` continues to be problematic, you canRUN bun install -g turbo@latest 
COPY . . # Copy full monorepo
RUN turbo prune main-api --docker simplify the final image by copying the `node_modules` from the `build-installer` stage (which has dev 
# Output: ./out/json/ (package manifests), ./out/full/ (source code), ./out/bun.lock (pruned lockfile)

# --- Build-Installer Stage ---
# Installs ALL dependencies (dev and prod) for the PRUNED workspace.
FROM base AS build-installer 
WORKDIR /app # This will be the root of the pruned dependencies) and accept a larger image. This is less ideal for production but can get you unblocked.

```dockerfile
# --- Final Runner Stage (Simplified node_modules) ---
FROM oven/bun:1-alpine AS runner
# ...
# Copy ALL dependencies (dev and prod for the pruned set)
COPY --from=build-installer /app/node_modules structure from 'out/full'

# Copy the pruned full source code (which includes package.json files at their ./node_modules 
# ... rest of the runner stage
``` pruned locations)
COPY --from=pruner /app/out/full/ .
# Copy the ORIGINAL root lockfile from

**Before trying any of these:**

1.  **Ensure your root `bun.lock` is 100% up-to-date locally:**
    ```bash
     the source, NOT the pruned one from /app/out/
COPY --from=pruner /app/bun.lock ./cd /home/<USER>/opt/taXi/Taxi29/ # Monorepo Root
    bun install
bun.lock 
# (This assumes 'COPY . .' in pruner stage copied the original bun.lock to /app/    git add bun.lock
    git commit -m "Freshen bun.lock" # If there are any changes
    ```
2bun.lock)

# Now, try to install dependencies for the 'main-api' workspace using its pruned package.json
# but.  **Clean Docker build cache for this image specifically if you made Dockerfile changes:**
    ```bash
    docker compose -f packages/docker-config/docker-compose.yml build --no-cache main-api
    ```

 guided by the original, complete monorepo lockfile.
# The --filter flag tells bun to only consider the 'main-api' package and its local dependencies.
# Replace 'main-api' with its actual package name if different**Start with Option 1.** It's the most theoretically sound approach when using `turbo prune` – use the pruned  (e.g., @scope/main-api)
RUN bun install --frozen-lockfile --filter main`package.json` structure but with the original, complete lockfile to guide the installation. This often resolves inconsistencies that arise from trying-api... # The '...' means include dependencies
# This command might need adjustment based on how Bun's -- to use a pruned lockfile with `bun install --frozen-lockfile`.