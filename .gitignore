# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules
.pnp
.pnp.js

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist


# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem
old-example-bot/
*/node_modules/
.aider*
packages/db/src/types/gramio.txt/params.d.ts.txt
packages/db/src/types/gramio.txt/context.ts.txt
packages/db/src/types/gramio.txt/methods.d.ts.txt
packages/db/src/types/gramio.txt/objects.d.ts.txt
repomix-*


# Added by <PERSON> Task Master
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 