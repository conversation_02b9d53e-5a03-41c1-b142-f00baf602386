# packages/docker-config/docker-compose.yml
services:

  postgres:
    container_name: postgres-postgis
    image: postgis/postgis
    restart: unless-stopped
    ports: 
      - "5432:5432"
    environment:
      - POSTGRES_USER=gramio
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=gramio
    volumes:
      - pg_data:/var/lib/postgresql/data
  pgadmin4:
    image: dpage/pgadmin4
    restart: unless-stopped
    ports:
      - "25432:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: supersecred
    volumes:
      - pgadmin_data:/var/lib/pgadmin




  redis:
    container_name: gramio-redis
    image: redis:latest
    command: ["redis-server", "--maxmemory-policy", "noeviction"]
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - 6379:6379 


  rabbitmq:
      image: rabbitmq:3-management
      container_name: rabbitmq1
      ports:
          - "${RABBITMQ_PORT}:5672"
          - "15672:15672"
      environment:
          RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
          RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
      volumes:
          - rabbitmq_data:/var/lib/rabbitmq
      healthcheck:
          test: [ "CMD", "rabbitmqctl", "status" ]
          interval: 30s
          timeout: 10s
          retries: 3


volumes:
    postgres_data:
    redis_data:
    rabbitmq_data:
    pg_data:
    pgadmin_data: