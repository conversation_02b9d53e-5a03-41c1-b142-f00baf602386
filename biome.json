{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "organizeImports": {"enabled": true}, "formatter": {"enabled": true, "indentStyle": "tab", "lineWidth": 80, "formatWithErrors": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"useImportType": "warn", "noNamespace": "error"}, "suspicious": {"noExplicitAny": "off"}, "complexity": {}, "correctness": {"useHookAtTopLevel": "off", "noUnusedImports": "error"}}}, "files": {"ignore": ["node_modules", "dist", "**/dist/**", "out", "**/out/**", "build", "**/build/**", ".next", "**/.next/**", "coverage", "**/coverage/**", ".turbo", "bun.lockb", "*.log"], "ignoreUnknown": true}, "javascript": {"parser": {"unsafeParameterDecoratorsEnabled": true}, "formatter": {"quoteStyle": "double"}}}