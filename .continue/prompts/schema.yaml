name: schemaenum
version: 0.0.1
schema: v1
prompts:
  - name: Create Drizzle Schema/Enum
    description: Generate Drizzle ORM schema and enum files with TypeScript
    prompt: |
      Create schema and enum files for a Drizzle ORM PostgreSQL project using these conventions:

      ENUM FILE (e.g., user-role.enum.ts):
      1. Use `pgEnum` from Drizzle ORM
      2. Define values as a `const` array
      3. Export:
         - Drizzle enum (e.g., `userRoleEnum`)
         - TypeScript union type (e.g., `UserRoleType`)
         - String enum (e.g., `UserRole`)

      SCHEMA FILE (e.g., tenant-bots.schema.ts):
      1. Use `pgTable` with proper column types
      2. Include:
         - Primary keys with `uuid().primaryKey().defaultRandom()`
         - Foreign keys with `.references(() => otherTable.id)`
         - Enums from the enum file
         - Timestamps with `defaultNow()`
         - Indexes with `uniqueIndex()`
      3. Export relations using `relations()`

      Follow these patterns:
      - PascalCase for enum/table names
      - snake_case for column names
      - Add clear comments for each field
      - Include indexes for critical lookup fields
      - Use `created_at`/`updated_at` timestamps