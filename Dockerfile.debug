FROM oven/bun:1 AS base
WORKDIR /app

# Copy workspace configuration
COPY package.json bun.lock bun-workspace.yaml ./
COPY apps/main-api/package.json ./apps/main-api/
COPY packages/db/package.json ./packages/db/
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY apps/dashboard/package.json ./apps/dashboard/
COPY apps/mini-app/package.json ./apps/mini-app/

# Install all dependencies with workspace support
RUN bun install

# Copy source code
COPY . .

# Build the database package first
RUN cd packages/db && bun run build

# Debug workspace links
RUN echo "=== Workspace debug ===" && \
    ls -la node_modules/@monorepo/ && \
    echo "=== DB package contents ===" && \
    ls -la packages/db/dist/ && \
    echo "=== Check if workspace package is installed ===" && \
    cd apps/main-api && ls -la node_modules/@monorepo/ || echo "No local node_modules"
