{"models": {"main": {"provider": "openrouter", "modelId": "google/gemini-2.5-flash-preview-05-20", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "perplexity", "modelId": "sonar", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "openrouter", "modelId": "openai/gpt-4.1-mini", "maxTokens": 8192, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseUrl": "http://localhost:11434/api", "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/"}}