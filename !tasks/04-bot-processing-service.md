# Task ID: 4
# Title: BotProcessingService Implementation
# Status: pending
# Dependencies: 3
# Priority: high
# Description: Implement the BotProcessingService that will manage the universal GramIO processor and tenant-specific bot clients.

# Details:
Implement the BotProcessingService class that will serve as the core of the multi-tenant bot architecture. This service will manage the universal GramIO processor and tenant-specific bot clients, handle context derivation, and process updates for different webhook path segments.

The implementation should include:

1. **Service Structure**
   - Create a NestJS injectable service class
   - Implement OnModuleInit interface
   - Define necessary properties:
     - universalGramioProcessor: GramioBotClass<AppBotErrorDefinitions, AppBaseContext>
     - tenantBotClients: Map<string, GramioBotClass['client']>
     - activeBotConfigs: Map<string, BotConfigFromDB>
   - Inject dependencies:
     - DrizzleService
     - AppConfigService
     - RedisService
     - All handler services

2. **Module Initialization**
   - Implement onModuleInit method
   - Load active bot configurations and initialize clients
   - Set up the universal GramIO processor with plugins
   - Implement context derivation logic
   - Initialize and register all handler services

3. **Bot Configuration Management**
   - Implement loadActiveBotConfigsAndInitializeClients method
   - Query database for active bots
   - Decrypt bot tokens
   - Initialize tenant-specific bot clients
   - Cache bot configurations

4. **Update Processing**
   - Implement processUpdateForSegment method
   - Look up bot configuration by webhook path segment
   - Attach bot configuration to update object
   - Process update using universal processor

5. **Token Security**
   - Implement encryptToken and decryptToken methods
   - Use secure encryption (AES-256-GCM)
   - Manage encryption keys securely

6. **User Role Management**
   - Implement fetchUserRoles method (can be a placeholder initially)
   - Query user_bot_roles and roles tables

# Test Strategy:
1. **Unit Tests**:
   - Test onModuleInit with mock dependencies
   - Test loadActiveBotConfigsAndInitializeClients with mock database
   - Test processUpdateForSegment with mock updates
   - Test token encryption/decryption

2. **Integration Tests**:
   - Test with real database and Redis
   - Verify that bot clients are initialized correctly
   - Test update processing flow

3. **Security Tests**:
   - Verify that tokens are properly encrypted in the database
   - Test token decryption with various inputs
   - Verify that sensitive information is not logged