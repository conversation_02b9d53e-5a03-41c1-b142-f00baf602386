# Task ID: 1
# Title: Multi-Tenant Bot Architecture Refactoring
# Status: pending
# Dependencies: 
# Priority: high
# Description: Refactor the existing GramIO Telegram bot implementation to support a multi-tenant architecture, following NestJS best practices.

# Details:
Implement a multi-tenant architecture for the Telegram bot system that allows handling multiple bots for different tenants through a single universal processor. This refactoring will follow the NestJS best practices outlined in the NestJS-Bot-Refactor-Plan.md and PLAN.md documents.

The implementation should include:

1. **Database Schema Design (Drizzle ORM)**
   - Define schemas for `tenants`, `bots` (with encrypted tokens, webhook path segments), `roles`, `user_bot_roles`
   - Ensure foreign keys and necessary indexes
   - Generate and apply migrations

2. **Core Types/Interfaces**
   - Define database types (BotConfigFromDB, TenantFromDB)
   - Define context extension types (AppContextExtensions, TenantContextExtension)
   - Define bot and context types (AppBotBase, AppBaseContext, AppMessageContext, etc.)

3. **BotProcessingService Implementation**
   - Implement universal GramIO processor
   - Implement tenant bot client management
   - Implement context derivation logic
   - Implement token encryption/decryption
   - Implement update processing for webhook segments

4. **Webhook Controller Implementation**
   - Create BotController with webhook endpoint
   - Implement update handling with path segment routing
   - Add security validation for webhook requests

5. **Handler Services Refactoring**
   - Refactor CommandHandlersService
   - Refactor CallbackQueryHandlersService
   - Refactor other handler services
   - Update all handlers to use tenant-specific API clients

6. **BotModule Configuration**
   - Configure module imports
   - Register all providers and controllers
   - Set up proper dependency injection

Key considerations:
- Security is critical, especially for token encryption
- All handlers must be updated to use tenant-specific API clients
- Database queries must be scoped by tenant/bot IDs

# Test Strategy:
1. **Unit Tests**:
   - Test BotProcessingService with mock bot configurations
   - Test context derivation logic
   - Test token encryption/decryption
   - Test handler services with mock contexts

2. **Integration Tests**:
   - Test webhook flow with mock Telegram updates
   - Test database interactions with test database
   - Test tenant isolation (ensure data from one tenant doesn't leak to another)

3. **End-to-End Tests**:
   - Set up test tenants and bots
   - Send mock updates through the webhook
   - Verify correct routing and processing
   - Verify responses are sent using the correct bot client