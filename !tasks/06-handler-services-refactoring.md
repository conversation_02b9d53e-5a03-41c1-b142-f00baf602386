# Task ID: 6
# Title: Handler Services Refactoring
# Status: pending
# Dependencies: 4
# Priority: high
# Description: Refactor all handler services to work with the multi-tenant bot architecture.

# Details:
Refactor all handler services to work with the multi-tenant bot architecture. This involves updating the services to use the universal GramIO processor and tenant-specific API clients.

The implementation should include:

1. **Common Refactoring Pattern**
   - Remove direct bot injection from constructors
   - Add processorInstance private property
   - Implement initialize method to set the processor instance
   - Update registerHandlers method to use processorInstance
   - Update handler methods to use tenant-specific API clients

2. **CommandHandlersService Refactoring**
   - Update constructor to remove bot injection
   - Add initialize method
   - Update registerHandlers method
   - Update all command handler methods to use ctx.tenantBotApiClient
   - Add tenant/bot context validation

3. **CallbackQueryHandlersService Refactoring**
   - Apply the same pattern as CommandHandlersService
   - Update all callback query handler methods

4. **Other Handler Services Refactoring**
   - Apply the same pattern to:
     - StartCommandHandlersService
     - InlineQueryHandlersService
     - ReactionHandlersService
     - ChosenInlineResultHandlersService
     - SceneCommandHandlersService
     - Any other handler services

5. **Context Usage Updates**
   - Update all handlers to use ctx.tenant and ctx.botInfo for tenant/bot specific logic
   - Scope database queries using ctx.tenant.id or ctx.botInfo.id
   - Use ctx.tenantBotApiClient for all Telegram API calls
   - Add null checks for tenant context properties

6. **Error Handling**
   - Add proper error handling for missing tenant context
   - Log errors with tenant/bot context information
   - Handle API client errors gracefully

# Test Strategy:
1. **Unit Tests**:
   - Test initialize and registerHandlers methods
   - Test handler methods with mock contexts
   - Test error handling

2. **Integration Tests**:
   - Test handlers with the universal processor
   - Verify that handlers use the correct tenant-specific API client
   - Test with multiple tenants to ensure isolation

3. **End-to-End Tests**:
   - Test complete command flows
   - Verify that responses are sent using the correct bot client
   - Test error scenarios