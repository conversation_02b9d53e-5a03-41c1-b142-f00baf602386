Okay, this is a crucial document for detailing the core user interaction flow! Integrating scenes for ordering services is a powerful use case for Gramio.

Here's the detailed documentation for Telegram integration with scene-based ordering, following your Task Structure format. I've placed it logically within the "Multi-Platform Bot Integration (Backend)" phase (Phase 3) as it builds upon the core bot infrastructure.
Task ID: 309
Title: Implement Telegram Scene-Based Service Ordering
Status: pending
Dependencies: [302, 303, 306, 308, 204]
Priority: high
Description: Develop the Telegram bot's scene-based conversational flows for ordering services (e.g., taxi, delivery), leveraging Gramio's scene management and the UnifiedApiClient.
Details:

    1. Overview:

        Platform: Telegram

        Role: Primary messaging channel for users to interact with the service, enabling structured multi-step processes like service ordering.

        Key Libraries: gramio, @gramio/scenes, @gramio/i18n, custom AppBot context.

    2. Core Functionalities:

        Messaging: Handling text messages, commands, and replies.

        Interactive UI: Utilizing Telegram's Inline Keyboards and Reply Keyboards for guided user input and choices.

        Scenes: Implementing state-driven conversational flows for multi-step processes, ensuring a structured and user-friendly ordering experience.

        Proactive Notifications: Sending real-time updates and status changes to users regarding their orders.

        Localization (i18n): Providing multi-language support for all user interactions within the bot.

    3. Scene-Based Service Ordering Flow (Example: Taxi Order):

        Entry Point:

            User sends a command (e.g., /order, /taxi) or taps an "Order Taxi" button (from a persistent menu or inline keyboard).

            The bot's main command handler (CommandHandlersService) detects this and initiates the orderScene.

        Scene: orderScene (Main Order Flow)

            Step 1: Pickup Location (pickupLocationStep)

                Prompt: "Please send your pickup location." (e.g., "sendLocationPrompt" i18n key)

                Expected Input:

                    Text message (address string).

                    Telegram's Location object (user shares current location or selects from map).

                Validation:

                    If text: Call NestJS backend's geospatial API (/api/geospatial/geocode) for address validation and coordinate lookup.

                    If location object: Use provided coordinates.

                Action: Store validated location data (coordinates, formatted address) in ctx.session.scene.data.pickupLocation.

                Transition: If valid, advance to destinationStep. If invalid, re-prompt or offer "Cancel".

            Step 2: Destination (destinationStep)

                Prompt: "Now, where are you going?" (e.g., "sendDestinationPrompt" i18n key)

                Expected Input: Similar to pickup location (text or Telegram Location object).

                Validation: Geocoding via NestJS backend's geospatial API.

                Action:

                    Store validated location data in ctx.session.scene.data.destinationLocation.

                    Call NestJS backend's ETA/Routing API (/api/eta/calculate) with pickup and destination to get estimated price and time.

                    Store ETA/price in session.

                Transition: If valid, display ETA/price and transition to confirmationStep. If invalid, re-prompt or offer "Cancel".

            Step 3: Confirmation/Options (confirmationStep)

                Prompt: Display a summary of the order (pickup, destination, estimated price, ETA). (e.g., "orderSummaryPrompt" i18n key with interpolated data).

                Interactive UI: Send an Inline Keyboard with options:

                    "Confirm Order" (callback_data: 'order:confirm')

                    "Change Pickup" (callback_data: 'order:change_pickup')

                    "Change Destination" (callback_data: 'order:change_destination')

                    "Add Comment" (callback_data: 'order:add_comment')

                    "Select Vehicle Type" (callback_data: 'order:select_vehicle')

                    "Cancel Order" (callback_data: 'order:cancel')

                Logic:

                    Handle callback queries:

                        order:confirm: Proceed to final order placement.

                        order:change_pickup: Re-enter pickupLocationStep (using ctx.scene.reenter()).

                        order:change_destination: Re-enter destinationStep.

                        order:add_comment: Transition to a sub-scene for comment input.

                        order:select_vehicle: Transition to a sub-scene for vehicle type selection.

                        order:cancel: Exit scene, send cancellation message.

                Action (on confirm): Call NestJS backend's order management API (POST /api/orders/create) with all collected order details.

                Transition: If order creation successful, advance to orderPlacedStep. If backend error, inform user and stay in scene or exit.

            Step 4: Order Placed/Status (orderPlacedStep)

                Prompt: "Your order #[ORDER_ID] has been placed!" (e.g., "orderPlacedPrompt" i18n key).

                Display: Show driver details (if assigned), real-time tracking link (if available).

                Action: The scene can optionally remain active to receive real-time updates from the backend (e.g., driver assigned, driver arrived).

                Exit: Scene exits automatically after a period, or on explicit user action/order completion.

            Sub-Scenes (Examples):

                addCommentScene: Prompts for text input, stores in session.data.comment, then returns to confirmationStep.

                selectVehicleTypeScene: Displays vehicle types with inline buttons, stores selection in session.data.vehicleType, then returns to confirmationStep.

    4. Technical Integration Details:

        Inbound Flow:

            Telegram webhook payload -> NestJS TelegramWebhookController -> TelegramInboundAdapter (normalizes to INormalizedUpdate).

            INormalizedUpdate -> BotProcessingService.processUniversalUpdate().

            BotProcessingService -> Gramio AppBot (with AppGlobalDeriveExtensions for t, tenantId, UnifiedApiClient, etc.).

            Gramio's built-in scenes middleware processes ctx.session.scene state and dispatches to the correct scene step handler.

        Outbound Flow:

            Scene handlers utilize ctx.api (the UnifiedApiClient) to send messages and interactive elements back to Telegram.

            UnifiedApiClient delegates to TelegramOutboundAdapter for Telegram-specific API calls.

        Session Management:

            Gramio's session plugin, backed by RedisStorage, will be used to persist ctx.session data, including ctx.session.scene which holds the current scene's state and data across messages.

        Backend API Interaction:

            Scene handlers make HTTP requests to the NestJS backend's core APIs (e.g., /api/geospatial/geocode, /api/eta/calculate, /api/orders/create).

            These APIs will be secured with JWT authentication.

        Proactive Notifications:

            Backend services (e.g., OrderService) will use the UnifiedApiClient to send asynchronous, proactive messages to users via Telegram (and other platforms) when order status changes (e.g., driver assigned, order completed).

    5. Data Models:

        Order Model: The canonical order data model defined in the NestJS backend (Task 204), representing all order details (pickup, destination, vehicle, price, status, driver, etc.). This model will be mirrored in shared TypeScript types.

        SessionData: A TypeScript interface defining the structure of data stored in ctx.session.scene.data for the current scene (e.g., pickupLocation: { lat: number; lon: number; address: string; }, destinationLocation, selectedVehicleType, comment).

        NormalizedMessage / NormalizedKeyboard: The abstract types used by UnifiedApiClient for sending messages and UI elements, which TelegramOutboundAdapter will convert to Telegram-specific formats.

    6. Localization (i18n):

        All user-facing prompts, messages, and button texts within scenes will use ctx.t("i18n_key", { param: "value" }).

        Translation keys will be defined in the assets/i18n/ JSON files, ensuring consistency across all platforms.

    7. Error Handling:

        Scene-specific validation errors: Handled within the scene steps, re-prompting the user with clear, translated error messages (e.g., "Invalid address, please try again.").

        Backend API errors: Handled by scene logic, translating backend error codes/messages into user-friendly, localized bot responses.

        prompt-cancel: Implement a mechanism to allow users to cancel an active scene at any time (e.g., via a "Cancel" button or command), leading to ctx.scene.exit().

        Global bot error handling: For unexpected system errors, ensuring a graceful fallback message is sent to the user.

Test Strategy:

    Unit Tests (Scenes):

        Mock ctx and ctx.session objects.

        Test each scene step's handler in isolation, verifying:

            Correct prompts are sent.

            Input validation logic.

            Correct updates to ctx.session.scene.data.

            Correct scene transitions (ctx.scene.next(), ctx.scene.reenter(), ctx.scene.enter(), ctx.scene.exit()).

            Correct calls to ctx.api (mock UnifiedApiClient).

            Correct calls to backend API services (mock HTTP requests).

    Integration Tests (Bot-Backend):

        Simulate Telegram webhooks for various user inputs (text, location, callback queries).

        Verify that the bot processes updates through the scene middleware, interacts with backend APIs, and sends correct responses back to Telegram.

        Test full end-to-end ordering flows with mock backend data.

    E2E Tests:

        Use a bot simulation framework (if available for Gramio/NestJS) or conduct manual testing to run through the entire ordering flow for different scenarios (successful order, invalid input, cancellation, different vehicle types).

        Verify proactive notifications are received for order status changes.