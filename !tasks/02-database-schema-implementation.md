# Task ID: 2
# Title: Database Schema Implementation for Multi-Tenant Bot
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Implement the database schema for the multi-tenant bot architecture using Drizzle ORM with PostgreSQL.

# Details:
Implement the database schema for the multi-tenant bot architecture as outlined in the PLAN.md document. This includes defining tables for tenants, bots, roles, and user-bot-role relationships using Drizzle ORM with PostgreSQL.

The implementation should include:

1. **Create schema.ts file with Drizzle ORM definitions**
   - Define tenants table with the following fields:
     - id (UUID, primary key)
     - name (varchar, not null)
     - created_at (timestamp)
     - updated_at (timestamp)
   
   - Define bots table with the following fields:
     - id (UUID, primary key)
     - tenant_id (UUID, foreign key to tenants.id)
     - token (text, encrypted, not null)
     - webhook_path_segment (varchar, unique, not null)
     - bot_username (varchar)
     - is_enabled (boolean, default true)
     - config (jsonb for bot-specific settings)
     - created_at (timestamp)
     - updated_at (timestamp)
   
   - Define roles table with the following fields:
     - id (UUID, primary key)
     - tenant_id (UUID, foreign key to tenants.id)
     - name (varchar, not null)
     - permissions (jsonb)
     - Add unique constraint for role name per tenant
   
   - Define user_bot_roles table with the following fields:
     - telegram_user_id (bigint, not null)
     - bot_id (UUID, foreign key to bots.id)
     - role_id (UUID, foreign key to roles.id)
     - Add primary key constraint on (telegram_user_id, bot_id, role_id)

2. **Create migration scripts**
   - Generate initial migration using Drizzle Kit
   - Review and adjust migration as needed

3. **Apply migrations**
   - Set up database connection
   - Run migrations in development environment
   - Verify schema creation

4. **Create seed data (optional)**
   - Create seed script for development testing
   - Add sample tenant and bot data

Important considerations:
- Ensure token encryption is properly implemented
- Add appropriate indexes for performance
- Add created_at and updated_at timestamps to all tables
- Ensure proper cascade delete behavior for related records

# Test Strategy:
1. **Schema Validation Tests**:
   - Verify that all tables are created with the correct structure
   - Verify that constraints (primary keys, foreign keys, unique constraints) are properly applied
   - Verify that indexes are created correctly

2. **Migration Tests**:
   - Test migration up and down functionality
   - Verify that migrations can be applied to a clean database
   - Verify that migrations can be rolled back without data loss

3. **Query Performance Tests**:
   - Test common query patterns for performance
   - Verify that indexes are being used correctly
   - Measure query execution time for critical operations