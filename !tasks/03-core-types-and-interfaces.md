# Task ID: 3
# Title: Core Types and Interfaces Implementation
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Define and implement the core TypeScript types and interfaces needed for the multi-tenant bot architecture.

# Details:
Define and implement the core TypeScript types and interfaces that will be used throughout the multi-tenant bot architecture. These types will provide strong typing for database entities, context extensions, and bot-specific types.

The implementation should include:

1. **Database Types**
   - Define `BotConfigFromDB` interface:
     - id: string (UUID)
     - tenantId: string (UUID)
     - token: string (decrypted token)
     - webhookPathSegment: string
     - botUsername?: string | null
     - isEnabled: boolean
     - config?: any (parsed JSONB)
   
   - Define `TenantFromDB` interface:
     - id: string (UUID)
     - name: string
     - Other tenant-specific fields

2. **Context Extension Types**
   - Define `AppContextExtensions` interface:
     - readonly t: ReturnType<typeof I18nInstance['buildT']> (for i18n)
   
   - Define `TenantInfo` interface (extends Pick<TenantFromDB, 'id' | 'name'>):
     - Additional pre-loaded tenant data if needed
   
   - Define `BotInstanceInfo` interface (extends Pick<BotConfigFromDB, 'id' | 'botUsername'>):
     - Additional pre-loaded bot config data if needed
   
   - Define `TenantContextExtension` interface:
     - tenant?: TenantInfo
     - botInfo?: BotInstanceInfo
     - userRoles?: string[]
     - tenantBotApiClient?: GramioBot['client']

3. **Bot and Context Types**
   - Define `AppBotErrorDefinitions` type
   - Define `AppBotBase` type (using GramIO's Bot)
   - Define `AppBaseContext` type (fully augmented base context)
   - Define specific context types extending AppBaseContext:
     - `AppMessageContext`
     - `AppCommandContext`
     - `AppCallbackQueryContext`
     - Other specific context types as needed

All types should be properly documented with JSDoc comments to explain their purpose and usage.

# Test Strategy:
1. **Type Checking**:
   - Verify that all types compile without errors
   - Test type compatibility with GramIO types
   - Verify that type extensions work correctly

2. **Usage Tests**:
   - Create test files that use the defined types
   - Verify that type inference works correctly
   - Test edge cases and optional properties

3. **Documentation Review**:
   - Review JSDoc comments for completeness and accuracy
   - Verify that IDE intellisense works correctly with the defined types