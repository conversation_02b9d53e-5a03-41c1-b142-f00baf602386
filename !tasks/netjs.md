Okay, understood! WhatsApp integration will be deferred for now. I will remove all mentions of WhatsA<PERSON> from the plan, adjusting task descriptions and component lists to focus solely on **Telegram, Viber, and PBX**.

Here is the updated, comprehensive rewrite plan:

---

## High-Level Rewrite Plan: Old System to NestJS PBX-Integrated Ecosystem (with Existing Next.js/React Dashboard Template)

**Overall Goal:** Fully rewrite the existing JavaScript backend and Angular frontend into a modern, strictly-typed NestJS backend with integrated PBX capabilities, and adapt an existing Next.js/React dashboard template to fulfill the requirements of the provided PRD.

**Phases:**

1.  **Foundational Setup (NestJS Backend Core)**
2.  **Core Backend Feature Parity (APIs & Services)**
3.  **Multi-Platform Bot Integration (Backend)**
4.  **PBX Integration (Backend & Unified API)**
5.  **Dashboard Frontend Integration & Feature Build-out (Next.js/React)**
6.  **Testing, Deployment & Cutover**

---

### Phase 1: Foundational Setup (NestJS Backend Core)

# Task ID: 101
# Title: Initialize NestJS Monorepo Structure
# Status: pending
# Dependencies: []
# Priority: high
# Description: Set up the main NestJS application within the existing monorepo, including basic configuration and initial modules.
# Details:
*   Create a new NestJS application within the `apps/` directory (e.g., `apps/main-api`).
*   Configure `tsconfig.json` for the new NestJS app, extending from `@repo/typescript-config/base.json` and ensuring `lib: ["ES2020"]` and `types: ["node"]`.
*   Set up basic `main.ts` for application bootstrapping.
*   Integrate with existing `turbo.json` for build/dev commands.
# Test Strategy:
*   Run `npm run build` for the new NestJS app to confirm successful compilation.
*   Run `npm run start:dev` to ensure the NestJS app starts without errors.
*   Access a default `/` endpoint to confirm server is running.



You've got a `package.json` for the dashboard, a `next.config.mjs`, and a `tsconfig.json` for the Next.js app. This is all the information needed to integrate it into your Turborepo.

Here's how you add the dashboard to your Turborepo, assuming your main `package.json` (at the root of your monorepo) defines workspaces.

**1. Define the Dashboard as a Workspace:**

In your **root `package.json`**:

```json
{
  "name": "your-monorepo-root",
  "version": "1.0.0",
  "private": true,
  "workspaces": [
    "apps/*",
    "packages/*"
    // Add any other top-level directories for apps/packages
  ],
  "scripts": {
    // ... your existing monorepo scripts
  },
  // ... other root configs
}
```

Make sure your dashboard application is located within a directory that matches one of your `workspaces` globs, e.g., `apps/dashboard`. If it's currently in `uc/`, you'll want to move it to `apps/dashboard`.

**2. Configure Turborepo for the Dashboard:**

In your **`turbo.json`** (at the root of your monorepo):

You'll need to define tasks for the dashboard, typically `dev`, `build`, `start`, and `lint`.

```json
{
  "$schema": "https://turbo.build/schema.json",
  "globalDependencies": [
    "**/.env",
    "**/.env.local",
    "**/.env.development",
    "**/.env.production"
  ],
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**", ".next/**", "!.next/cache/**"] // Important for Next.js outputs
    },
    "lint": {
      "outputs": []
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "start": { // For running the production build of the dashboard
      "cache": false,
      "persistent": true
    },
    "typecheck": { // Add typecheck task for dashboard
      "outputs": []
    },
    "test": { // Add test task if you have tests for the dashboard
      "outputs": []
    },
    "registry:build": { // Specific task for the dashboard's registry
      "dependsOn": ["^build"], // If it depends on other packages being built
      "outputs": ["public/registry/**"]
    }
  }
}
```

**Explanation of `turbo.json` additions:**

*   **`build`**:
    *   `"dependsOn": ["^build"]`: Means that before building the dashboard, Turborepo will try to build any dependencies of the dashboard (e.g., shared UI packages, common utilities) that also have a `build` script.
    *   `"outputs": ["dist/**", ".next/**", "!.next/cache/**"]`: Tells Turborepo which files to cache. For Next.js, this is primarily the `.next` directory (excluding the cache) and any `dist` folder if you have one.
*   **`lint`**:
    *   `"outputs": []`: Linting doesn't produce output files that need caching.
*   **`dev`**:
    *   `"cache": false`: Development servers typically shouldn't be cached.
    *   `"persistent": true`: Keeps the `next dev` process running in the background.
*   **`start`**:
    *   Similar to `dev`, but for running the production build.
*   **`typecheck`**:
    *   `"outputs": []`: Type checking doesn't produce output files.
*   **`test`**:
    *   `"outputs": []`: Tests don't produce output files that need caching (though test *reports* might).
*   **`registry:build`**:
    *   This is a custom script from your dashboard's `package.json`. You should define it in `turbo.json` if you want Turborepo to manage it.
    *   `"outputs": ["public/registry/**"]`: Caches the output of your registry build.

**3. Move the Dashboard Application:**

Move the entire `shadcn-sidebar` directory (which contains `src/`, `public/`, `registry/`, `package.json`, `next.config.mjs`, `tsconfig.json`, etc.) into your monorepo's `apps/` directory.

```
your-monorepo-root/
├── apps/
│   ├── main-api/        # Your NestJS backend (from previous steps)
│   └── dashboard/       # <--- Move the shadcn-sidebar content here
│       ├── .kilocode/
│       ├── public/
│       ├── registry/
│       ├── scripts/
│       ├── src/
│       ├── .eslintrc.json
│       ├── .gitignore
│       ├── # Product Requirements Document (PRD).md
│       ├── biome.jsonc
│       ├── components.json
│       ├── CONVENTIONS.md
│       ├── Dockerfile
│       ├── LICENSE
│       ├── mcp_settings.json
│       ├── nest-cli.json   # This might be specific to NestJS, review if needed for Next.js
│       ├── next.config.mjs
│       ├── package.json    # The package.json you provided
│       ├── postcss.config.mjs
│       ├── README.md
│       ├── tailwind.config.ts
│       └── tsconfig.json
├── packages/
│   ├── db/
│   └── ...
├── node_modules/
├── package.json         # Root package.json
├── turbo.json
└── ...
```

**Important Considerations after Moving:**

*   **`nest-cli.json` in `apps/dashboard`:** The `nest-cli.json` file is specific to NestJS projects. If the `shadcn-sidebar` template is purely Next.js/React, this file is likely leftover or irrelevant and can be removed from `apps/dashboard`. If it's a hybrid project, you'll need to clarify its purpose.
*   **Root `Dockerfile` vs. App `Dockerfile`:** Your monorepo's root `Dockerfile` should be the primary one for building the *entire* application. The `Dockerfile` inside `apps/dashboard` might be a remnant from when it was a standalone project. You'll likely want to:
    *   **Remove `apps/dashboard/Dockerfile`** (or rename it to `Dockerfile.dashboard` for reference).
    *   **Adapt your root `Dockerfile`** to build both `main-api` (NestJS) and `dashboard` (Next.js) and then serve them (e.g., using a multi-stage build, building the Next.js app, and then copying its output to the NestJS serving directory).
*   **Relative Paths in `apps/dashboard`:** After moving, check `apps/dashboard/tsconfig.json` and `next.config.mjs` for any absolute or relative paths that might break. The `paths` alias `"@/*": ["./src/*"]` is good, but if it referenced anything outside its new `apps/dashboard` root, it might need adjustment.
*   **Shared Packages:** If `apps/dashboard` needs to use any code from your `packages/db` or other shared `packages/*`, you'll need to add them as dependencies in `apps/dashboard/package.json` using `workspace:` protocol (e.g., `"@monorepo/db": "workspace:^"`).
*   **`bun` vs `npm`/`pnpm`/`yarn`:** Your root `package.json` and `turbo.json` should reflect your chosen package manager. The provided `package.json` for the dashboard uses `bun`. Ensure your monorepo is configured to use `bun` if that's your preference, or adjust the dashboard's scripts to `npm`/`pnpm`/`yarn`.

Once these steps are complete, you should be able to run `bun run dev` (or `npm run dev`) from the monorepo root, and Turborepo will orchestrate the `dev` scripts for both your NestJS backend and Next.js dashboard.



---

# Task ID: 102
# Title: Implement Configuration Management Module
# Status: pending
# Dependencies: [101]
# Priority: high
# Description: Create a NestJS ConfigModule to manage environment variables (`.env`) and provide validated configuration throughout the application.
# Details:
*   Install `@nestjs/config`.
*   Create `ConfigModule` and `ConfigService` (e.g., `src/config/config.module.ts`, `src/config/config.service.ts`).
*   Define a `Joi` or `class-validator` schema for environment variables (e.g., `PORT`, `DATABASE_URL`, `JWT_SECRET`, and placeholders for all bot/PBX tokens).
*   Integrate `ConfigModule` into `AppModule`.
*   Update `.env` files to reflect new NestJS backend variables.
# Test Strategy:
*   Write unit tests for `ConfigService` to ensure environment variables are loaded and validated correctly.
*   Attempt to start the application with missing/invalid environment variables to confirm validation errors.

---

# Task ID: 103
# Title: Integrate Drizzle ORM and Database Module
# Status: pending
# Dependencies: [101, 102]
# Priority: high
# Description: Set up Drizzle ORM within NestJS, connecting to PostgreSQL and defining initial schemas for core entities (users, tenants, roles, bot_configs).
# Details:
*   Install `drizzle-orm`, `pg` (or appropriate database driver), and `drizzle-kit`.
*   Create a `DatabaseModule` (e.g., `src/database/database.module.ts`) that provides the Drizzle DB instance.
*   Define Drizzle schemas for `users`, `tenants`, `bot_configs` (reusing/extending existing `packages/db` schema definitions).
*   Implement `DatabaseService` (reusing/extending `apps/bot/src/services/database.service.ts`) to interact with Drizzle.
*   Configure `drizzle.config.ts` for migrations.
*   Run initial migrations to set up the database.
# Test Strategy:
*   Write unit tests for `DatabaseService` to perform basic CRUD operations on a test database.
*   Verify database connection on application startup.
*   Confirm migrations run successfully.

---

# Task ID: 104
# Title: Implement Centralized Error Handling and Logging
# Status: pending
# Dependencies: [101]
# Priority: high
# Description: Set up global error handling and a structured logging system for the NestJS backend.
# Details:
*   Implement a global `HttpExceptionFilter` to catch and format API errors consistently.
*   Use NestJS's built-in `Logger` or integrate a more advanced logging library (e.g., Winston, Pino) for structured logging.
*   Configure logging levels (debug, info, warn, error).
*   Ensure sensitive information is not logged.
*   Implement custom exceptions for specific business logic errors.
# Test Strategy:
*   Trigger various error conditions (e.g., invalid input, unauthorized access, internal server errors) and verify consistent error response formats and log outputs.
*   Check that sensitive data is masked in logs.

---

### Phase 2: Core Backend Feature Parity (APIs & Services)

# Task ID: 201
# Title: Document Existing Backend Features and APIs (from old Angular)
# Status: pending
# Dependencies: []
# Priority: high
# Description: Thoroughly document all existing functionalities and API endpoints from the old JavaScript backend and Angular frontend to serve as the blueprint for the rewrite.
# Details:
*   Analyze the old Angular frontend code to identify every API call (paths, methods, request/response bodies, query parameters, authentication) it makes to the existing backend.
*   For each feature/endpoint, record:
    *   **Functional Description:** What does it do?
    *   **API Details:** HTTP Method, URL Path, Request/Response bodies, Query Parameters, Authentication.
    *   **Business Logic:** Any specific rules, validations, or workflows.
    *   **Data Model:** Relevant database entities and relationships.
*   Organize documentation (e.g., Markdown, OpenAPI/Swagger draft). This will be the primary reference for the rewrite.
# Test Strategy:
*   N/A (Documentation task).

---

# Task ID: 202
# Title: Implement Authentication and Authorization Module
# Status: pending
# Dependencies: [103, 104, 201]
# Priority: high
# Description: Develop a robust authentication system using JWT, integrating with NestJS Passport, and defining basic roles and permissions for backend API access.
# Details:
*   Install `@nestjs/passport`, `passport-jwt`, `@nestjs/jwt`.
*   Create `AuthModule` (`src/auth/auth.module.ts`) including `AuthService`, `JwtStrategy`, `AuthGuard`, `RolesGuard` (using the `nestjs-roles` library as per `CONVENTIONS.md`).
*   Implement user login/registration endpoints.
*   Integrate with `UserService` (to be created in 203) to validate user credentials against the database.
*   Define JWT payload structure to include `userId`, `tenantId`, `roles` (using the `Role` enum from `CONVENTIONS.md`).
*   Implement `@Roles()` decorator and `RolesGuard` for API endpoint authorization.
# Test Strategy:
*   Integration tests for login/registration endpoints, verifying JWT token generation.
*   Test authenticated API endpoints with valid/invalid tokens.
*   Test role-protected endpoints with users having/lacking required roles.

---

# Task ID: 203
# Title: Migrate Core User and Tenant Management APIs
# Status: pending
# Dependencies: [103, 201, 202]
# Priority: high
# Description: Recreate core user and tenant management API endpoints in NestJS, ensuring data integrity and proper authentication.
# Details:
*   Create `UserModule` (`src/users/user.module.ts`) and `TenantModule` (`src/tenants/tenant.module.ts`).
*   Implement `UserService` and `TenantService` to encapsulate business logic and interact with `DatabaseService`.
*   Create `UserController` and `TenantController` with endpoints for:
    *   CRUD operations for users (e.g., `GET /users`, `POST /users`, `GET /users/:id`, `PUT /users/:id`, `DELETE /users/:id`).
    *   CRUD operations for tenants (e.g., `GET /tenants`, `POST /tenants`).
    *   Endpoints for linking users to tenants, managing roles within tenants.
*   Apply `AuthGuard` and `RolesGuard` to protect endpoints.
*   **Design API responses to be consumed by the new Next.js dashboard.**
# Test Strategy:
*   Integration tests for all new user and tenant API endpoints (CRUD operations, role assignments).
*   Verify proper authentication and authorization enforcement.
*   Perform data validation tests for incoming requests.

---

# Task ID: 204
# Title: Migrate Remaining Core Business Logic APIs
# Status: pending
# Dependencies: [201, 203]
# Priority: medium
# Description: Translate and implement all remaining business logic from the old JavaScript backend into structured NestJS services and controllers, including geospatial and ETA.
# Details:
*   Identify all remaining distinct business domains from the documentation (Task 201).
*   For each domain, create a dedicated NestJS module (e.g., `OrderModule`, `AnalyticsModule`, `GeospatialModule`, `EtaModule`).
*   Implement services (`OrderService`, `AnalyticsService`, `GeospatialService`, `EtaService`) for the business logic.
*   Create controllers (`OrderController`, `AnalyticsController`, `GeospatialController`, `EtaController`) for the API endpoints.
*   **Geospatial Service:** Implement geocoding (`/api/geospatial/geocode`) and reverse geocoding, potentially integrating Nominatim.
*   **ETA Service:** Implement ETA calculation (`/api/eta/calculate`) and routing, potentially integrating OSRM.
*   Ensure proper dependency injection and separation of concerns.
*   **Design API responses to be consumed by the new Next.js dashboard.**
# Test Strategy:
*   Comprehensive unit tests for each new service method, covering all business rules and edge cases.
*   Integration tests for API endpoints to ensure correct data flow and business logic execution.

---

### Phase 3: Multi-Platform Bot Integration (Backend)

# Task ID: 301
# Title: Refine `INormalizedUpdate` for Multi-Platform Events
# Status: pending
# Dependencies: [103]
# Priority: high
# Description: Define a canonical `INormalizedUpdate` interface and `Platform` enum that all inbound adapters will convert to, covering Telegram, Viber, and PBX events.
# Details:
*   Define a `Platform` enum: `telegram`, `viber`, `pbx`.
*   Create `INormalizedUpdate` interface with common fields as outlined in the previous plan (Task 101 in old plan).
*   Ensure it can accommodate messaging and call event types comprehensively.
# Test Strategy:
*   Create mock raw payloads for Telegram, Viber, and PBX (call events).
*   Develop type assertion tests (`tsd`) to ensure `INormalizedUpdate` correctly types various event payloads.

---

# Task ID: 302
# Title: Integrate Bot Processing Service into NestJS
# Status: pending
# Dependencies: [102, 103, 301]
# Priority: high
# Description: Integrate the existing `BotProcessingService` (from `apps/bot`) into the new NestJS backend application, adapting it to NestJS's DI system and the new multi-platform update format.
# Details:
*   Move or link `apps/bot/src/gramiobot/bot-processing.service.ts` and related files into the new NestJS backend app (e.g., `src/bot-integration/`).
*   Create a `BotIntegrationModule` to encapsulate bot-related services.
*   Adapt `BotProcessingService` to be a NestJS `@Injectable()` service.
*   Inject `ConfigService`, `RedisService`, `DatabaseService`, and other dependencies using NestJS's DI.
*   Ensure `onModuleInit` lifecycle hook is used for bot initialization (`loadActiveBotConfigsAndInitializeClients`).
*   Update `AppBotErrorDefinitions` and `AppGlobalDeriveExtensions` to be fully compatible with the NestJS environment and the new `INormalizedUpdate`.
*   Modify `BotProcessingService`'s `processUpdateForSegment` to accept `INormalizedUpdate`.
# Test Strategy:
*   Unit tests for `BotProcessingService` methods, ensuring all dependencies are correctly injected and initialization logic runs.
*   Verify that active bot configurations are loaded and API clients are initialized.

---

# Task ID: 303
# Title: Implement Telegram Inbound/Outbound Adapters
# Status: pending
# Dependencies: [103, 301, 302]
# Priority: high
# Description: Create NestJS services for Telegram inbound (Gramio webhook parsing) and outbound (Gramio bot.api calls) interactions.
# Details:
*   Create `TelegramInboundAdapter` to extract `TelegramUpdate` and map it to `INormalizedUpdate`.
*   Create `TelegramOutboundAdapter` that wraps Gramio's `bot.api` calls, mapping normalized outbound messages to Telegram's API parameters.
*   These adapters will be injected into the `UnifiedApiClient` (Task 306).
*   Ensure Telegram-specific features (inline keyboards, media groups) are handled.
# Test Strategy:
*   Unit tests for both adapters, verifying correct payload transformation and API call mapping.

---

# Task ID: 304
# Title: Implement Viber Inbound Adapter
# Status: pending
# Dependencies: [103, 301, 302]
# Priority: high
# Description: Create a NestJS service to parse incoming Viber webhooks and convert them into `INormalizedUpdate` objects.
# Details:
*   Create `ViberInboundAdapter` (e.g., `src/platform-adapters/viber/viber-inbound.adapter.ts`).
*   Utilize the `messaging-api-viber` library for parsing incoming JSON.
*   Implement methods to map Viber's message types (text, picture, video, file, contact, location, url, sticker, rich_media) and event types (`message`, `subscribed`, `conversation_started`, `seen`, `delivered`, `failed`) to `INormalizedUpdate.updateType`.
*   Implement logic to determine `tenantId` and `botId` from the incoming webhook payload/headers (e.g., a custom `webhookPathSegment` or a lookup by Viber's `sender.id` if it's a known bot ID).
*   Store the raw Viber payload in `INormalizedUpdate.raw`.
# Test Strategy:
*   Unit tests with various mock Viber webhook payloads, asserting correct transformation into `INormalizedUpdate` format.

---

# Task ID: 305
# Title: Implement Viber Outbound Adapter
# Status: pending
# Dependencies: [103, 301, 302, 304]
# Priority: high
# Description: Create a NestJS service to send messages and perform actions via the Viber API, translating normalized requests into Viber-specific formats.
# Details:
*   Create `ViberOutboundAdapter` (e.g., `src/platform-adapters/viber/viber-outbound.adapter.ts`).
*   Internally, use the `ViberClient` from `messaging-api-viber`.
*   Implement methods that map `INormalizedUpdate`'s message structure (or a common `NormalizedSendMessageParams` object) to Viber's `ViberTypes.Message` format (e.g., `sendText`, `sendPicture`, `sendCarouselContent`).
*   Handle casing conversion (`camelCase` to `snake_case` or `PascalCase`) using `messaging-api-common` utilities as seen in `ViberClient.ts`.
*   Retrieve the appropriate `viberAccessToken` and `viberSender` from the `BotConfig` before making API calls.
# Test Strategy:
*   Unit tests for `ViberOutboundAdapter` methods, mocking outgoing HTTP requests to verify correct Viber API payload construction and authentication headers.

---

# Task ID: 306
# Title: Create Unified API Client & Context Abstraction
# Status: pending
# Dependencies: [303, 305]
# Priority: high
# Description: Develop a `UnifiedApiClient` and adapt the `AppBot` context to support sending messages across different platforms using a single, normalized API.
# Details:
*   Define a `UnifiedApiClient` interface/class in `src/services/unified-api-client.ts`.
*   This client will expose common methods like `sendText(platform, receiverId, text, options)` or `sendMessage(platform, receiverId, normalizedMessage, options)`.
*   The implementation of `UnifiedApiClient` will delegate calls to the correct platform-specific `Outbound Adapter` (e.g., `TelegramOutboundAdapter`, `ViberOutboundAdapter`) based on the `platform` parameter or context.
*   Modify `AppGlobalDeriveExtensions` to include `api: UnifiedApiClient`.
*   Update `BotProcessingService` to inject and manage instances of `UnifiedApiClient` per bot.
# Test Strategy:
*   Unit tests for `UnifiedApiClient` to verify correct dispatching of calls to underlying platform adapters based on the `platform` argument.
*   Integration tests where `BotProcessingService` (or a simulated `UniversalContext`) receives a `NormalizedUpdate`, and calling `ctx.api.sendMessage(...)` correctly routes to the right adapter and triggers a mock API call.

---

# Task ID: 307
# Title: Implement Platform Webhook Controllers
# Status: pending
# Dependencies: [302, 303, 304]
# Priority: high
# Description: Create NestJS controllers to receive incoming webhooks from Telegram and Viber, routing them to the `BotProcessingService`.
# Details:
*   Create `TelegramWebhookController` and `ViberWebhookController` (e.g., `src/bot-integration/webhooks/`).
*   Define `@Post()` routes for each platform's webhook endpoint (e.g., `/telegram/webhook/:botId`, `/viber/webhook/:botId`).
*   Each controller will:
    *   Extract the `botId` from the URL parameter (or identify from payload).
    *   Read the raw request body.
    *   Call the appropriate `Inbound Adapter` (e.g., `TelegramInboundAdapter.normalize(rawPayload, botId)`).
    *   Pass the `INormalizedUpdate` to `BotProcessingService.processUniversalUpdate(normalizedUpdate)`.
    *   Handle platform-specific webhook verification (e.g., Telegram's secret token, Viber's handshake).
    *   Return appropriate HTTP responses (e.g., 200 OK, 404 Not Found, 403 Forbidden).
# Test Strategy:
*   Integration tests simulating incoming webhooks for each platform using mock payloads.
*   Verify that requests are correctly routed, processed by adapters, and dispatched to the `BotProcessingService`.
*   Test webhook verification mechanisms.

---

# Task ID: 308
# Title: Adapt Core Handlers for Multi-Platform Events
# Status: pending
# Dependencies: [302, 306]
# Priority: high
# Description: Modify existing bot logic (or create new handlers) to respond to multi-platform events, using the `UnifiedContext` and `UnifiedApiClient`.
# Details:
*   In `BotProcessingService`, ensure the event dispatching mechanism can handle `INormalizedUpdate` types.
*   Refactor existing Telegram-specific handlers (`CommandHandlersService`, `CallbackQueryHandlersService`, `ReactionHandlersService`) to operate on a `UnifiedContext` (which includes `INormalizedUpdate` and `AppGlobalDeriveExtensions`).
*   Replace direct calls to `ctx.send(...)` (Gramio's API) with `ctx.api.send(...)` (your `UnifiedApiClient`).
*   Identify and isolate any remaining platform-specific logic within handlers, moving it to platform-specific adapters or specialized logic where absolutely necessary.
# Test Strategy:
*   Unit tests for handlers with mock `UnifiedContext` objects (containing both Telegram and Viber `INormalizedUpdate` data).
*   Verify that handlers perform expected logic and call `ctx.api` methods correctly.

---

# Task ID: 309
# Title: Implement Telegram Scene-Based Service Ordering
# Status: pending
# Dependencies: [302, 303, 306, 308, 204]
# Priority: high
# Description: Develop the Telegram bot's scene-based conversational flows for ordering services (e.g., taxi, delivery), leveraging Gramio's scene management and the `UnifiedApiClient`.
# Details:
*   **1. Overview:**
    *   **Platform:** Telegram
    *   **Role:** Primary messaging channel for users to interact with the service, enabling structured multi-step processes like service ordering.
    *   **Key Libraries:** `gramio`, `@gramio/scenes`, `@gramio/i18n`, custom `AppBot` context.
*   **2. Core Functionalities:**
    *   **Messaging:** Handling text messages, commands, and replies.
    *   **Interactive UI:** Utilizing Telegram's Inline Keyboards and Reply Keyboards for guided user input and choices.
    *   **Scenes:** Implementing state-driven conversational flows for multi-step processes, ensuring a structured and user-friendly ordering experience.
    *   **Proactive Notifications:** Sending real-time updates and status changes to users regarding their orders.
    *   **Localization (`i18n`):** Providing multi-language support for all user interactions within the bot.
*   **3. Scene-Based Service Ordering Flow (Example: Taxi Order):**
    *   **Entry Point:**
        *   User sends a command (e.g., `/order`, `/taxi`) or taps an "Order Taxi" button (from a persistent menu or inline keyboard).
        *   The bot's main command handler (`CommandHandlersService`) detects this and initiates the `orderScene`.
    *   **Scene: `orderScene` (Main Order Flow)**
        *   **Step 1: Pickup Location (`pickupLocationStep`)**
            *   **Prompt:** "Please send your pickup location." (e.g., "sendLocationPrompt" i18n key)
            *   **Expected Input:**
                *   Text message (address string).
                *   Telegram's `Location` object (user shares current location or selects from map).
            *   **Validation:**
                *   If text: Call NestJS backend's geospatial API (`/api/geospatial/geocode`) for address validation and coordinate lookup.
                *   If location object: Use provided coordinates.
            *   **Action:** Store validated location data (coordinates, formatted address) in `ctx.session.scene.data.pickupLocation`.
            *   **Transition:** If valid, advance to `destinationStep`. If invalid, re-prompt or offer "Cancel".
        *   **Step 2: Destination (`destinationStep`)**
            *   **Prompt:** "Now, where are you going?" (e.g., "sendDestinationPrompt" i18n key)
            *   **Expected Input:** Similar to pickup location (text or Telegram `Location` object).
            *   **Validation:** Geocoding via NestJS backend's geospatial API.
            *   **Action:**
                *   Store validated location data in `ctx.session.scene.data.destinationLocation`.
                *   Call NestJS backend's ETA/Routing API (`/api/eta/calculate`) with pickup and destination to get estimated price and time.
                *   Store ETA/price in session.
            *   **Transition:** If valid, display ETA/price and transition to `confirmationStep`. If invalid, re-prompt or offer "Cancel".
        *   **Step 3: Confirmation/Options (`confirmationStep`)**
            *   **Prompt:** Display a summary of the order (pickup, destination, estimated price, ETA). (e.g., "orderSummaryPrompt" i18n key with interpolated data).
            *   **Interactive UI:** Send an Inline Keyboard with options:
                *   "Confirm Order" (`callback_data: 'order:confirm'`)
                *   "Change Pickup" (`callback_data: 'order:change_pickup'`)
                *   "Change Destination" (`callback_data: 'order:change_destination'`)
                *   "Add Comment" (`callback_data: 'order:add_comment'`)
                *   "Select Vehicle Type" (`callback_data: 'order:select_vehicle'`)
                *   "Cancel Order" (`callback_data: 'order:cancel'`)
            *   **Logic:**
                *   Handle callback queries:
                    *   `order:confirm`: Proceed to final order placement.
                    *   `order:change_pickup`: Re-enter `pickupLocationStep` (using `ctx.scene.reenter()`).
                    *   `order:change_destination`: Re-enter `destinationStep`.
                    *   `order:add_comment`: Transition to a sub-scene for comment input.
                    *   `order:select_vehicle`: Transition to a sub-scene for vehicle type selection.
                    *   `order:cancel`: Exit scene, send cancellation message.
            *   **Action (on confirm):** Call NestJS backend's order management API (`POST /api/orders/create`) with all collected order details.
            *   **Transition:** If order creation successful, advance to `orderPlacedStep`. If backend error, inform user and stay in scene or exit.
        *   **Step 4: Order Placed/Status (`orderPlacedStep`)**
            *   **Prompt:** "Your order #[ORDER_ID] has been placed!" (e.g., "orderPlacedPrompt" i18n key).
            *   **Display:** Show driver details (if assigned), real-time tracking link (if available).
            *   **Action:** The scene can optionally remain active to receive real-time updates from the backend (e.g., driver assigned, driver arrived).
            *   **Exit:** Scene exits automatically after a period, or on explicit user action/order completion.
        *   **Sub-Scenes (Examples):**
            *   **`addCommentScene`:** Prompts for text input, stores in `session.data.comment`, then returns to `confirmationStep`.
            *   **`selectVehicleTypeScene`:** Displays vehicle types with inline buttons, stores selection in `session.data.vehicleType`, then returns to `confirmationStep`.
*   **4. Technical Integration Details:**
    *   **Inbound Flow:**
        *   Telegram webhook payload -> NestJS `TelegramWebhookController` -> `TelegramInboundAdapter` (normalizes to `INormalizedUpdate`).
        *   `INormalizedUpdate` -> `BotProcessingService.processUniversalUpdate()`.
        *   `BotProcessingService` -> Gramio `AppBot` (with `AppGlobalDeriveExtensions` for `t`, `tenantId`, `UnifiedApiClient`, etc.).
        *   Gramio's built-in `scenes` middleware processes `ctx.session.scene` state and dispatches to the correct scene step handler.
    *   **Outbound Flow:**
        *   Scene handlers utilize `ctx.api` (the `UnifiedApiClient`) to send messages and interactive elements back to Telegram.
        *   `UnifiedApiClient` delegates to `TelegramOutboundAdapter` for Telegram-specific API calls.
    *   **Session Management:**
        *   Gramio's `session` plugin, backed by `RedisStorage`, will be used to persist `ctx.session` data, including `ctx.session.scene` which holds the current scene's state and data across messages.
    *   **Backend API Interaction:**
        *   Scene handlers make HTTP requests to the NestJS backend's core APIs (e.g., `/api/geospatial/geocode`, `/api/eta/calculate`, `/api/orders/create`).
        *   These APIs will be secured with JWT authentication.
    *   **Proactive Notifications:**
        *   Backend services (e.g., `OrderService`) will use the `UnifiedApiClient` to send asynchronous, proactive messages to users via Telegram (and other platforms) when order status changes (e.g., driver assigned, order completed).
*   **5. Data Models:**
    *   **`Order` Model:** The canonical order data model defined in the NestJS backend (Task 204), representing all order details (pickup, destination, vehicle, price, status, driver, etc.). This model will be mirrored in shared TypeScript types.
    *   **`SessionData`:** A TypeScript interface defining the structure of data stored in `ctx.session.scene.data` for the current scene (e.g., `pickupLocation: { lat: number; lon: number; address: string; }`, `destinationLocation`, `selectedVehicleType`, `comment`).
    *   **`NormalizedMessage` / `NormalizedKeyboard`:** The abstract types used by `UnifiedApiClient` for sending messages and UI elements, which `TelegramOutboundAdapter` will convert to Telegram-specific formats.
*   **6. Localization (`i18n`):**
    *   All user-facing prompts, messages, and button texts within scenes will use `ctx.t("i18n_key", { param: "value" })`.
    *   Translation keys will be defined in the `assets/i18n/` JSON files, ensuring consistency across all platforms.
*   **7. Error Handling:**
    *   **Scene-specific validation errors:** Handled within the scene steps, re-prompting the user with clear, translated error messages (e.g., "Invalid address, please try again.").
    *   **Backend API errors:** Handled by scene logic, translating backend error codes/messages into user-friendly, localized bot responses.
    *   **`prompt-cancel`:** Implement a mechanism to allow users to cancel an active scene at any time (e.g., via a "Cancel" button or command), leading to `ctx.scene.exit()`.
    *   **Global bot error handling:** For unexpected system errors, ensuring a graceful fallback message is sent to the user.

# Test Strategy:
*   **Unit Tests (Scenes):**
    *   Mock `ctx` and `ctx.session` objects.
    *   Test each scene step's handler in isolation, verifying:
        *   Correct prompts are sent.
        *   Input validation logic.
        *   Correct updates to `ctx.session.scene.data`.
        *   Correct scene transitions (`ctx.scene.next()`, `ctx.scene.reenter()`, `ctx.scene.enter()`, `ctx.scene.exit()`).
        *   Correct calls to `ctx.api` (mock `UnifiedApiClient`).
        *   Correct calls to backend API services (mock HTTP requests).
*   **Integration Tests (Bot-Backend):**
    *   Simulate Telegram webhooks for various user inputs (text, location, callback queries).
    *   Verify that the bot processes updates through the scene middleware, interacts with backend APIs, and sends correct responses back to Telegram.
    *   Test full end-to-end ordering flows with mock backend data.
*   **E2E Tests:**
    *   Use a bot simulation framework (if available for Gramio/NestJS) or conduct manual testing to run through the entire ordering flow for different scenarios (successful order, invalid input, cancellation, different vehicle types).
    *   Verify proactive notifications are received for order status changes.

---

### Phase 4: PBX Integration (Backend & Unified API)

# Task ID: 401
# Title: Research & Select Asterisk ARI/AMI Client Libraries
# Status: pending
# Dependencies: [101]
# Priority: high
# Description: Identify and select robust Node.js client libraries for interacting with Asterisk ARI and AMI.
# Details:
*   **ARI Client:** Search for well-maintained Node.js libraries for Asterisk REST Interface (e.g., `ari-client`, `asterisk-ari`). Prioritize libraries with good TypeScript support, active development, and clear documentation.
*   **AMI Client:** If AMI is deemed necessary for specific management/monitoring tasks not covered by ARI or FreePBX NestQL, identify a suitable Node.js AMI client.
*   Evaluate features, stability, community support, and licensing.
*   Make a selection and add chosen libraries to `package.json`.
# Test Strategy:
*   N/A (Research task).

---

# Task ID: 402
# Title: Extend Database Schema for PBX Configuration
# Status: pending
# Dependencies: [103, 302]
# Priority: high
# Description: Modify the `BotConfig` (or a new `TenantTelephonyConfig`) and related database schemas to store PBX-specific credentials and settings per tenant.
# Details:
*   Update `BotConfigFromDB` (or create `TenantTelephonyConfigFromDB`) in `src/types/database.ts` and `drizzle` schema.
*   Add fields for PBX connection details:
    *   `pbxEnabled: boolean`
    *   `ariUrl: string | null`, `ariUsername: string | null`, `ariPassword: string | null`
    *   `amiUrl: string | null`, `amiUsername: string | null`, `amiPassword: string | null` (if AMI is used)
    *   `freePbxApiUrl: string | null`, `freePbxApiKey: string | null` (if FreePBX NestQL is a REST API)
*   Add `pbxDids: string[] | null` for phone numbers assigned to a tenant, or a separate `Dids` table for complex mapping.
# Test Strategy:
*   Write database migration scripts.
*   Write unit tests for `DatabaseService` to save and retrieve new PBX configuration properties.

---

# Task ID: 403
# Title: Implement Asterisk ARI Event Listener & Inbound Adapter
# Status: pending
# Dependencies: [301, 302, 306, 401, 402]
# Priority: high
# Description: Create a NestJS service to connect to Asterisk ARI, listen for real-time call events, and convert them into `INormalizedUpdate` objects.
# Details:
*   Create `AsteriskAriService` (e.g., `src/pbx-integration/ari/asterisk-ari.service.ts`) to manage ARI client connections.
*   Implement ARI event listeners (e.g., `StasisStart`, `ChannelStateChange`, `DTMF`, `PlaybackFinished`, `Hangup`).
*   Create `AsteriskInboundAdapter` to map raw ARI events to `INormalizedUpdate` types.
    *   Define new `updateType` values in `INormalizedUpdate` (e.g., `'call_started'`, `'dtmf_received'`, `'call_hungup'`).
    *   Implement logic to determine `tenantId` and `botId` from the incoming call's DID or Caller ID by looking up `pbxDids` in `BotConfig`.
    *   Pass the `INormalizedUpdate` to `BotProcessingService.processUniversalUpdate(normalizedUpdate)`.
*   Implement reconnection logic for ARI client.
# Test Strategy:
*   Unit tests for `AsteriskInboundAdapter` with mock ARI event payloads, asserting correct normalization and tenant identification.
*   Integration tests: Set up a test Asterisk instance, configure ARI, and verify that actual call events are received and processed by NestJS.

---

# Task ID: 404
# Title: Implement Asterisk ARI Outbound Adapter
# Status: pending
# Dependencies: [306, 401, 402]
# Priority: high
# Description: Create a NestJS service to control active calls via Asterisk ARI, translating normalized requests from `UnifiedApiClient` into ARI actions.
# Details:
*   Create `AsteriskAriOutboundAdapter` (e.g., `src/pbx-integration/ari/asterisk-ari-outbound.adapter.ts`).
*   This adapter will be injected into `UnifiedApiClient`.
*   Implement methods to map common call actions to ARI: `makeCall`, `playAudioInCall`, `sendDTMF`, `transferCall`, `hangupCall`.
*   Retrieve ARI credentials from `BotConfig`.
# Test Strategy:
*   Unit tests for `AsteriskAriOutboundAdapter` methods with mock ARI client, asserting correct ARI command construction.
*   Integration tests: Use the `UnifiedApiClient` to make a call via the NestJS backend and verify that Asterisk initiates the call.

---

# Task ID: 405
# Title: Integrate FreePBX Management API (NestQL)
# Status: pending
# Dependencies: [204, 402]
# Priority: medium
# Description: Integrate `FreePBX NestQL` (or direct AMI/FreePBX database interaction) into a NestJS service for managing PBX configurations.
# Details:
*   Create `FreePbxManagementService` (e.g., `src/pbx-integration/freepbx/freepbx-management.service.ts`).
*   This service will encapsulate interactions with FreePBX's configuration layer.
*   Implement methods for: `createExtension`, `assignDidToTenant`, `configureIvr`, `getCallLogs`.
*   Determine if `FreePBX NestQL` directly exposes these or if it requires AMI/database queries.
*   Ensure authentication with FreePBX.
# Test Strategy:
*   Unit tests for `FreePbxManagementService` methods, mocking external FreePBX API/DB calls.
*   Integration tests: Verify that calling these methods from NestJS correctly creates/modifies configurations in a test FreePBX instance.

---

# Task ID: 406
# Title: Extend `UnifiedApiClient` for PBX Actions
# Status: pending
# Dependencies: [306, 404]
# Priority: high
# Description: Add PBX-specific methods to the `UnifiedApiClient` interface and its implementation, allowing core logic to control calls.
# Details:
*   Add methods to `UnifiedApiClient` interface: `makeCall`, `hangupCall`, `playAudioInCall`, `transferCall`, `sendDtmf`, `joinConference`.
*   Implement these methods in `UnifiedApiClient`'s concrete class, delegating to `AsteriskAriOutboundAdapter` when `platform` is `'pbx'`.
*   Ensure all necessary parameters (`callId`, `mediaUrl`, `digits`, `destination`) are passed through.
# Test Strategy:
*   Unit tests for `UnifiedApiClient` methods, verifying correct delegation to `AsteriskAriOutboundAdapter`.
*   Type assertion tests (`tsd`) to confirm `UnifiedApiClient` methods are correctly typed.

---

# Task ID: 407
# Title: Adapt Core Handlers for PBX Events
# Status: pending
# Dependencies: [308, 403, 406]
# Priority: high
# Description: Modify existing bot logic (or create new handlers) to respond to and initiate PBX events, using the `UnifiedContext` and `UnifiedApiClient`.
# Details:
*   In `BotProcessingService`, ensure the event dispatching mechanism can handle the new `updateType` values from PBX events.
*   Create new NestJS handlers/services (e.g., `CallEventHandlerService`) to process `call_started`, `dtmf_received`, `call_hungup` events.
*   Implement business logic examples: answering a call and playing an IVR, routing a call based on DTMF input, connecting a call to a messaging platform user, initiating an outbound call from a messaging command.
*   Use `ctx.api.makeCall()`, `ctx.api.playAudioInCall()`, etc.
# Test Strategy:
*   Unit tests for new PBX event handlers, mocking `UnifiedContext` and `UnifiedApiClient` interactions.
*   Integration tests with a live Asterisk instance: make a call to a DID, send DTMF, initiate an outbound call from a Telegram command, verify bot logic.

---

### Phase 5: Dashboard Frontend Integration & Feature Build-out (Next.js/React)

# Task ID: 501
# Title: Integrate Next.js Dashboard Template into Monorepo
# Status: pending
# Dependencies: [101]
# Priority: high
# Description: Integrate the provided Next.js template into the monorepo, ensuring it can be built and served by the NestJS backend.
# Details:
*   Move the provided Next.js template (the entire directory structure you sent) into `apps/dashboard`.
*   Verify `package.json` scripts (`dev`, `build`, `start`) and dependencies (React, Next.js, shadcn/ui, zustand).
*   Configure the NestJS backend (`apps/main-api`) to serve the compiled Next.js `dist` output (`.next/static` and `public` folders). This will likely involve using NestJS's `ServeStaticModule` or a custom Express static middleware.
*   Ensure all API routes on the NestJS backend are prefixed (e.g., `/api/`) to avoid conflicts with Next.js static asset serving or routing.
*   Implement a catch-all route in NestJS to serve `apps/dashboard/.next/server/pages/index.html` (or equivalent for App Router) for client-side routing.
*   Configure `tsconfig.json` for `apps/dashboard` to ensure correct `jsx`, `jsxImportSource`, `lib`, and `types` (React, Next.js, Node, DOM).
*   **Remove `nest-cli.json` from `apps/dashboard`** as it's a Next.js project, not NestJS.
*   **Review `apps/dashboard/Dockerfile`** and either remove it (if root Dockerfile will build all) or adapt it for multi-stage build within the monorepo context.
# Test Strategy:
*   Run `bun run build` within `apps/dashboard` to confirm successful Next.js compilation.
*   Start the NestJS backend.
*   Access the root URL (`/`) of the NestJS server in a browser and verify the Next.js dashboard loads and displays the initial structure (header, sidebar, main content area).
*   Test Next.js client-side routing by navigating to a demo route (e.g., `/dashboard`) and refreshing the page.

---

# Task ID: 502
# Title: Implement Dashboard Authentication Flow (Next.js)
# Status: pending
# Dependencies: [202, 501]
# Priority: high
# Description: Create the user login, logout, and session management flow for the Next.js dashboard, interacting with the new NestJS authentication APIs.
# Details:
*   Utilize the existing `src/app/(demo)/account/page.tsx` as a potential starting point for user account info, and the `UserNav` component (`src/components/admin-panel/user-nav.tsx`) for logout.
*   Design dedicated login and registration pages using `shadcn/ui` components (Input, Button, Card).
*   Implement API calls to NestJS backend endpoints (`POST /api/auth/login`, `POST /api/auth/register`).
*   Store JWT tokens securely (e.g., in HTTP-only cookies managed by the backend, or `localStorage` with security considerations for client-side access).
*   Implement a React Context or `zustand` store (similar to `useSidebar`) for global authentication state (`isAuthenticated`, `user`, `tenantId`, `roles`).
*   Protect dashboard routes and UI elements based on authentication status and user roles (using Next.js middleware or React component guards, and the `Role` enum from `CONVENTIONS.md`).
*   Implement a logout feature that clears tokens and redirects.
# Test Strategy:
*   E2E tests for login, logout, and authenticated route access.
*   Test with valid/invalid credentials.
*   Verify token storage and retrieval, and correct redirection.

---

# Task ID: 503
# Title: Migrate Core Dashboard Views & Data Display (Calls, Stats)
# Status: pending
# Dependencies: [203, 204, 308, 502]
# Priority: high
# Description: Rebuild the primary call management and daily statistics views using the Next.js template, fetching live data from the new NestJS APIs.
# Details:
*   **Call List (`calls/page.tsx`):**
    *   Adapt the existing `src/app/(demo)/calls/page.tsx` to fetch actual call data from the NestJS backend (`GET /api/calls`).
    *   Map the `Call` interface in `src/types/call.ts` (from the template) to the data model returned by your NestJS API.
    *   Implement sorting, filtering, and pagination using `@tanstack/react-table` and backend API parameters.
    *   Recreate the "Call List" UI from `uc/src/app/calls/calls.component.html` and `uc/src/app/shared/components/list/list.component.html`, integrating `shadcn/ui` components (Table, Input, Button).
    *   Implement action buttons (Dial, SMS, Add, Remove, Cancel, Map) by calling the `UnifiedApiClient` via NestJS backend APIs.
*   **Daily Statistics (`daily-stats/dailystats.component.html`):**
    *   Create a new page `src/app/(demo)/daily-stats/page.tsx`.
    *   Implement data fetching from NestJS backend (`GET /api/daily-stats`).
    *   Integrate a React-compatible charting library (e.g., `react-chartjs-2` for `Chart.js`) to display the daily stats.
    *   Recreate the date range filter and table display using `shadcn/ui` and a suitable date picker component.
*   **Real-time Updates:** Integrate WebSocket communication (from `SocketService` in old Angular, now handled by NestJS backend) to provide live updates for calls and stats. This will involve setting up WebSocket client in the React app and updating `zustand` stores.
# Test Strategy:
*   E2E tests for call list display, sorting, filtering, and pagination.
*   E2E tests for daily statistics display and date range filtering.
*   Integration tests for real-time updates (mock backend WebSocket events).

---

# Task ID: 504
# Title: Migrate Call Editing & Modals (Next.js)
# Status: pending
# Dependencies: [204, 503]
# Priority: high
# Description: Rebuild the call editing, scheduling, and SMS sending modal interfaces, leveraging the new `shadcn/ui` components and backend APIs.
# Details:
*   **Call Edit Modal (`addcall.component.html`, `call-edit.component.html`):**
    *   Utilize `shadcn/ui` Dialog/Sheet components for modals.
    *   Recreate input fields (phone, location, destination, vehicles, arrival time, area, comment) using `shadcn/ui` Input, Select, and custom autocomplete components.
    *   Implement form state management (e.g., `react-hook-form` or `useState` with `zustand`).
    *   Bind inputs to the `Call` model and call NestJS APIs (`POST/PUT /api/calls`).
    *   Replicate scheduling (one-time, recurring) and recurring schedule logic from `addcall.component.ts`.
*   **SMS Send Modal (`sendsms.component.html`):**
    *   Recreate the SMS modal, allowing users to send messages and select templates.
    *   Call NestJS API (`POST /api/messages/send-sms`).
*   **Mapping UI (`maps.component.html`):**
    *   Integrate a Leaflet-based map component (e.g., `react-leaflet`) for visualizing locations and destinations.
    *   Implement search/autocomplete for locations using a geocoding API (e.g., Nominatim via NestJS backend).
    *   Implement marker placement, swapping, and removal.
*   **Input Components:** Reimplement `phone.component.ts`, `location.component.ts`, `destination.component.ts`, `vehicles.component.ts`, `area.component.ts`, `arrivaltime.component.ts` as reusable React components.
# Test Strategy:
*   E2E tests for opening/closing modals, form submission, validation, and data persistence.
*   E2E tests for map interactions (marker placement, routing, swapping).

---

# Task ID: 505
# Title: Migrate Search Functionality (Next.js)
# Status: pending
# Dependencies: [204, 503]
# Priority: medium
# Description: Rebuild the advanced call search interface and display search results, interacting with the new NestJS search APIs.
# Details:
*   **Search Modal (`search.component.html`):**
    *   Create a new page/modal `src/app/(demo)/search/page.tsx`.
    *   Implement the search form with various filters (phone, date range, location, destination, vehicle, area, source, operator, call status, served status).
    *   Use `shadcn/ui` components for inputs and selects.
    *   Call NestJS API (`GET /api/search/calls`).
*   **Search Results (`search-results.component.html`):**
    *   Create a new page `src/app/(demo)/search/results/page.tsx`.
    *   Display search results using `@tanstack/react-table`, similar to the main call list.
# Test Strategy:
*   E2E tests for search form submission, filtering, and displaying results.
*   Verify search parameters are correctly passed to the backend API.

---

# Task ID: 506
# Title: Migrate User and Role Management UI (Next.js)
# Status: pending
# Dependencies: [203, 502]
# Priority: medium
# Description: Build the dashboard UI for managing users and their roles within tenants, interacting with the new NestJS APIs.
# Details:
*   Utilize `src/app/(demo)/users/page.tsx` as a starting point.
*   Create React components/pages for:
    *   Listing users (`GET /api/users`).
    *   Creating/editing users (`POST/PUT /api/users`).
    *   Assigning/removing roles to users within a tenant.
*   Ensure role-based access control is reflected in the UI (e.g., only admins can manage roles).
*   Use `shadcn/ui` for forms and tables.
# Test Strategy:
*   E2E tests for user and role management flows.
*   Verify UI permissions based on logged-in user roles.

---

# Task ID: 507
# Title: Develop Dashboard UI for PBX Management (Next.js)
# Status: pending
# Dependencies: [405, 502]
# Priority: high
# Description: Create Next.js UI components and pages for configuring and managing PBX settings within the dashboard.
# Details:
*   Create new sections/pages in the dashboard for "PBX Integration" or "Telephony Settings" (e.g., `src/app/(demo)/pbx/page.tsx`).
*   Implement forms for entering/editing:
    *   ARI/AMI connection details (URL, username, password).
    *   FreePBX API URL and key.
    *   Enabling/disabling PBX integration for a tenant.
*   Develop a UI for managing DIDs: listing, assigning to tenants, linking to bot instances.
*   (Optional but recommended): UI for viewing real-time call status or historical call logs (if `FreePbxManagementService` supports fetching them).
*   Use `shadcn/ui` for all UI elements.
# Test Strategy:
*   E2E tests for all PBX configuration UI flows.
*   Verify data is correctly saved via the new NestJS APIs and reflected in the UI.
*   Test role-based access to PBX management features.

---

# Task ID: 508
# Title: Implement Dashboard Customization System (Next.js)
# Status: pending
# Dependencies: [502]
# Priority: medium
# Description: Implement the registry-driven component system and customization features as outlined in the PRD.
# Details:
*   Leverage the existing `shadcn-sidebar` template's `registry/` and `hooks/use-sidebar.ts` for managing sidebar state and menu lists.
*   Extend this concept to a broader dashboard customization system:
    *   Allow users to arrange and resize widgets/components on the dashboard.
    *   Implement user preference management (e.g., saving preferred layouts, column visibility).
    *   **Backend Support:** Design NestJS APIs to store and retrieve user/tenant-specific dashboard configurations.
*   Ensure the system supports role-based dashboard variants.
# Test Strategy:
*   E2E tests for saving/loading custom dashboard layouts.
*   Verify different roles see different default layouts or have different customization options.

---

# Task ID: 509
# Title: Implement Map-Based Dashboard & ETA Features (Next.js)
# Status: pending
# Dependencies: [204, 407, 503]
# Priority: high
# Description: Develop the real-time map display, vehicle tracking, routing, and ETA calculation features as described in the PRD.
# Details:
*   Create a dedicated map page/component (e.g., `src/app/(demo)/map/page.tsx`).
*   Integrate a React-compatible mapping library (e.g., `react-leaflet` for Leaflet, or `react-map-gl` for MapLibre GL JS).
*   Fetch real-time vehicle locations from the NestJS backend (via WebSockets or polling).
*   Display vehicle markers with status indicators.
*   Implement route visualization with turn-by-turn details, potentially using OSRM integration (backend Task 204).
*   Display real-time ETA calculations (from backend Task 204).
*   Implement traffic overlay visualization (if supported by chosen map/routing service).
*   Develop UI for geofencing controls and visualization.
# Test Strategy:
*   E2E tests for real-time vehicle tracking and map updates.
*   Verify route visualization and ETA display accuracy.
*   Test geofencing visualization.

---

# Task ID: 510
# Title: Implement Localization (i18n) for Next.js Dashboard
# Status: pending
# Dependencies: [501]
# Priority: medium
# Description: Integrate a robust internationalization (i18n) solution into the Next.js dashboard, replicating the existing Angular localization.
# Details:
*   Choose a React/Next.js i18n library (e.g., `react-i18next` with `next-i18next`).
*   Migrate the existing `en.json`, `mk.json`, `sq.json` translation files from `uc/src/assets/i18n/` to the Next.js project's i18n setup.
*   Implement a language switcher UI component (similar to `language-switcher.component.ts`).
*   Ensure all static text and dynamic messages are translated.
*   Replicate the `MissingTranslationHandler` logic from the old Angular app if needed, or rely on i18n library's fallback mechanisms.
# Test Strategy:
*   Manual testing of all UI elements in all supported languages.
*   Automated tests for translation key presence and correct rendering.

---

# Task ID: 511
# Title: Migrate Remaining Dashboard Functionality and UI
# Status: pending
# Dependencies: [204, 503]
# Priority: medium
# Description: Rebuild any remaining dashboard features from the old Angular frontend, such as analytics, reporting, or other specific business logic UIs.
# Details:
*   Identify all remaining dashboard views and functionalities from Task 201.
*   Implement these as React components, consuming the corresponding NestJS backend APIs.
*   Focus on data visualization and user interaction.
# Test Strategy:
*   E2E tests for each migrated dashboard feature, ensuring data accuracy and UI responsiveness.

---

### Phase 6: Testing, Deployment & Cutover

# Task ID: 601
# Title: Implement Data Migration Strategy
# Status: pending
# Dependencies: [103]
# Priority: high
# Description: Plan and implement a one-time data migration script to transfer existing data from the old backend's database to the new PostgreSQL database.
# Details:
*   Analyze the schema mapping between old and new databases.
*   Develop a script (e.js., Node.js script, Python script) to read data from the old database and insert it into the new Drizzle/PostgreSQL schema.
*   Handle data transformations, cleaning, and any necessary data model changes.
*   Implement validation steps to ensure data integrity post-migration.
*   Plan for downtime during migration if necessary.
# Test Strategy:
*   Perform dry-run migrations on a staging environment.
*   Validate migrated data against the old system's data for accuracy and completeness.

---

# Task ID: 602
# Title: Comprehensive System Testing
# Status: pending
# Dependencies: [204, 308, 407, 511]
# Priority: high
# Description: Conduct thorough testing across the entire new system, including API, bot integration, PBX integration, and dashboard functionality.
# Details:
*   **API Testing:** Extensive Postman/Insomnia/automated API tests for all backend endpoints.
*   **Bot Integration Testing:** Simulate Telegram and Viber updates and verify bot responses and internal processing.
*   **PBX Integration Testing:** Simulate incoming calls/DTMF, originate calls, verify call routing and bot interaction via PBX.
*   **Dashboard E2E Testing:** Use Cypress/Playwright to test all dashboard user flows and features, including real-time updates, map interactions, and customization.
*   **Performance Testing:** Load testing for critical APIs, bot processing, and PBX interactions.
*   **Security Testing:** Penetration testing, vulnerability scanning.
*   **Data Integrity Tests:** Verify that data is correctly stored, retrieved, and updated across all interactions.
# Test Strategy:
*   Execute all defined test suites (unit, integration, E2E).
*   Track and resolve all reported bugs.
*   Document test coverage.

---

# Task ID: 603
# Title: Define Deployment Strategy & Cutover Plan
# Status: pending
# Dependencies: [501, 601]
# Priority: high
# Description: Plan and implement the deployment process for the new NestJS backend and Next.js dashboard, including the cutover from the old system.
# Details:
*   Choose a deployment environment (e.g., Docker, Kubernetes, cloud VMs).
*   Create Dockerfiles for the NestJS backend and a multi-stage build for the Next.js frontend.
*   Configure Nginx (or similar) as a reverse proxy.
*   Set up CI/CD pipelines for automated builds and deployments.
*   **Cutover Plan:** Define the steps for switching from the old system to the new one (e.g., DNS changes, webhook URL updates, data migration window).
*   Plan for rollback procedures.
# Test Strategy:
*   Perform a test deployment to a staging environment.
*   Conduct a full dry-run of the cutover process.
*   Verify all services are running and accessible post-deployment.
*   Conduct basic smoke tests post-deployment.

---

# Task ID: 604
# Title: Finalize Documentation and Handover
# Status: pending
# Dependencies: [602, 603]
# Priority: medium
# Description: Create comprehensive documentation for the new system and prepare for handover.
# Details:
*   Update API documentation (e.g., OpenAPI/Swagger).
*   Create developer guides for setting up and running the project.
*   Document deployment procedures.
*   Provide troubleshooting guides.
*   Conduct training sessions for relevant teams.
# Test Strategy:
*   Review documentation for clarity, accuracy, and completeness.
*   Conduct a "dry run" setup/deployment by someone unfamiliar with the project using only the documentation.