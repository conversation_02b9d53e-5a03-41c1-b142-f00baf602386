# Task ID: 5
# Title: Webhook Controller Implementation
# Status: pending
# Dependencies: 4
# Priority: high
# Description: Implement the NestJS controller for handling Telegram webhook requests for multiple tenants.

# Details:
Implement a NestJS controller that will handle Telegram webhook requests for multiple tenants. The controller will receive updates from Telegram, extract the webhook path segment from the URL, and delegate processing to the BotProcessingService.

The implementation should include:

1. **Controller Structure**
   - Create a NestJS controller class (BotController)
   - Set the controller base path to 'webhook'
   - Inject dependencies:
     - BotProcessingService
     - AppConfigService (for secret token validation)

2. **Webhook Endpoint**
   - Implement a POST endpoint with path parameter ':webhookPathSegment'
   - Extract the webhook path segment from the request URL
   - Extract the update object from the request body
   - Extract the X-Telegram-Bot-Api-Secret-Token header (if used)

3. **Security Validation**
   - Implement validation for the X-Telegram-Bot-Api-Secret-Token header
   - Compare with expected secret token for the webhook path segment
   - Return 401 Unauthorized if validation fails

4. **Update Processing**
   - Call BotProcessingService.processUpdateForSegment with the webhook path segment and update
   - Handle errors and return appropriate HTTP status codes
   - Log processing errors with appropriate context

5. **Response Handling**
   - Return 200 OK for successful processing
   - Return appropriate error codes for failures
   - Ensure responses are sent quickly (Telegram expects a response within 10 seconds)

# Test Strategy:
1. **Unit Tests**:
   - Test the controller with mock dependencies
   - Test webhook endpoint with various inputs
   - Test error handling

2. **Integration Tests**:
   - Test with mock Telegram updates
   - Verify that updates are correctly passed to BotProcessingService
   - Test secret token validation

3. **End-to-End Tests**:
   - Send mock webhook requests to the endpoint
   - Verify that updates are processed correctly
   - Test with invalid webhook path segments