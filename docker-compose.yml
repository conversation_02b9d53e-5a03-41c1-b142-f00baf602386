# packages/docker-config/docker-compose.yml
services:
  main-api:
    container_name: gramio-bot 
    restart: unless-stopped
    ports:
      - "3004:3004"
    build:
      context: .  
      dockerfile: Dockerfile.main-api-turbo 
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=redis 
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - PRIMARY_BOT_TOKEN=${PRIMARY_BOT_TOKEN}
      - TOKEN_ENCRYPTION_KEY=${TOKEN_ENCRYPTION_KEY}
    depends_on:
      - postgres
      - redis
# ... other services

  mini-app:
    container_name: mini-app-gramio-bot
    restart: unless-stopped
    ports:
      - 3001:3001
    build:
      context: .
      dockerfile: Dockerfile.mini-api
    environment:
      - NODE_ENV=production
    depends_on:
      - main-api

  dashboard:
    container_name: dashboard
    restart: unless-stopped
    ports:
      - 3000:3000
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    environment:
      - NODE_ENV=production
    depends_on:
      - main-api

  postgres:
    container_name: postgres-postgis
    image: postgis/postgis:17-3.5-alpine
    restart: unless-stopped
    ports: 
      - "5432:5432"
    environment:
      - POSTGRES_USER=gramio
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=gramio
    volumes:
      - pg_data:/var/lib/postgresql/data

  redis:
    container_name: gramio-redis
    image: redis:latest
    command: ["redis-server", "--maxmemory-policy", "noeviction"]
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - 6379:6379 


  rabbitmq:
      image: rabbitmq:3-management
      container_name: rabbitmq1
      ports:
          - "${RABBITMQ_PORT}:5672"
          - "15672:15672"
      environment:
          RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
          RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
      volumes:
          - rabbitmq_data:/var/lib/rabbitmq
      healthcheck:
          test: [ "CMD", "rabbitmqctl", "status" ]
          interval: 30s
          timeout: 10s
          retries: 3


volumes:
    postgres_data:
    redis_data:
    rabbitmq_data:
    pg_data: