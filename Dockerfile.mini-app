# Stage 1: Build the Vite app
FROM oven/bun:latest AS builder
WORKDIR /app

# Copy monorepo structure for dependency installation
COPY ../../package.json ../../bun.lock ./
COPY ../../apps/mini-app/package.json ./apps/mini-app/
# Copy any shared packages' package.json if mini-app depends on them from source

RUN bun install --frozen-lockfile

COPY . .

WORKDIR /app/apps/mini-app
ENV NODE_ENV=production
RUN bun run build # Assuming this runs "vite build", output usually in 'dist'

# Stage 2: Serve the static files
FROM joseluisq/static-web-server:2 AS final 
# This is a good lightweight server for static SPAs

# Copy built static assets from the builder stage
COPY --from=builder /app/apps/mini-app/dist /public

ENV SERVER_PORT=3001 
ENV SERVER_ROOT=/public
# SERVER_INDEX_FILE defaults to index.html, which is usually correct for Vite SPAs

EXPOSE 3001
# CMD is already defined in joseluisq/static-web-server