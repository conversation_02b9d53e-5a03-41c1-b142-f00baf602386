# Multi-stage build for mini-app (Vite + SolidJS)
FROM oven/bun:1 AS base
WORKDIR /app

# Copy workspace configuration
COPY package.json bun.lock ./
COPY apps/mini-app/package.json ./apps/mini-app/
COPY packages/db/package.json ./packages/db/
COPY packages/typescript-config/package.json ./packages/typescript-config/
COPY packages/eslint-config/package.json ./packages/eslint-config/

# Install all dependencies with workspace support
RUN bun install

# Copy source code
COPY . .

# Build the database package first (mini-app depends on it)
RUN cd packages/db && bun run build

# Build the mini-app
WORKDIR /app/apps/mini-app
ENV NODE_ENV=production
RUN bun run build

# Production stage - serve static files with a simple HTTP server
FROM nginx:alpine AS production
WORKDIR /app

# Copy built static files
COPY --from=base /app/apps/mini-app/dist /usr/share/nginx/html

# Copy custom nginx configuration for SPA
COPY <<EOF /etc/nginx/conf.d/default.conf
server {
    listen 3001;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Handle SPA routing
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
EOF

EXPOSE 3001

CMD ["nginx", "-g", "daemon off;"]
