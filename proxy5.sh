#!/bin/bash
set -euo pipefail

# Configuration (configurable via command-line arguments)
SSH_USER="root"
SSH_HOST="energosistem.centkom.mk"
SSH_PORT=660
LOCAL_SOCKS_PORT=1080

SSH_OPTIONS=(
    -o ServerAliveInterval=10
    -o ExitOnForwardFailure=yes
    -o BatchMode=yes
    -o StrictHostKeyChecking=yes
    -o ConnectTimeout=10
    -p "${SSH_PORT}"  # Optional: Include port in options
)

# Dependency check
check_dependencies() {
    local missing=()
    for cmd in ssh autossh curl; do
        if ! command -v "$cmd" &> /dev/null; then
            missing+=("$cmd")
        fi
    done
    
    if [[ ${#missing[@]} -gt 0 ]]; then
        echo "Missing required commands: ${missing[*]}"
        exit 1
    fi
}

# Connection information display
show_connection_info() {
    local info ip country org
    info=$(curl -s --socks5-hostname "localhost:${LOCAL_SOCKS_PORT}" https://ipinfo.io/json)
    ip=$(grep '"ip":' <<< "$info" | cut -d '"' -f4)
    country=$(grep '"country":' <<< "$info" | cut -d '"' -f4)
    org=$(grep '"org":' <<< "$info" | cut -d '"' -f4)

    echo "SOCKS5 Proxy Connection Details:"
    echo "IP: ${ip:-Unknown}"
    echo "Country: ${country:-Unknown}"
    echo "Organization: ${org:-Unknown}"
}

# Cleanup function
cleanup() {
    echo "Cleaning up..."
    pkill -f "autossh.*${LOCAL_SOCKS_PORT}"
    exit 0
}

# Main execution
main() {
    check_dependencies
    
    echo "Establishing resilient SOCKS5 proxy on port ${LOCAL_SOCKS_PORT}"
    echo "Using SSH connection to ${SSH_USER}@${SSH_HOST}:${SSH_PORT}"

    # Improved error reporting for SSH connection
    echo "Attempting to establish SSH connection..."
    if ! autossh -M 0 -f -N \
        -D "${LOCAL_SOCKS_PORT}" \
        "${SSH_OPTIONS[@]}" \
        "${SSH_USER}@${SSH_HOST}" 2> ssh_error.log; then
      echo "Error: Failed to establish SSH connection." >&2
      echo "Check the 'ssh_error.log' for more details."
      exit 1
    fi

    sleep 2  # Give autossh time to establish connection
    show_connection_info

    # System notification
    if command -v notify-send &> /dev/null && [[ -n "$DISPLAY" ]]; then
        notify-send -t 5000 "SOCKS5 Proxy Active" "Proxy established on port ${LOCAL_SOCKS_PORT}"
    fi

    trap cleanup EXIT
    echo "Press CTRL+C to terminate the proxy connection"
    while true; do sleep 60; done
}

# Start main function
main
