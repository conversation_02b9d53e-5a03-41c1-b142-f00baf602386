import Link from "next/link";
import { HomeIcon } from "lucide-react";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import { Breadcrumb, B<PERSON>crumbList, BreadcrumbItem, BreadcrumbLink } from "@/components/ui/breadcrumb";

export default function HomePage() {
  return (
    <ContentLayout title="Home">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/home">
                <HomeIcon className="w-4 h-4 mr-2" />
                Home
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="mt-6 p-6 border rounded-lg bg-card">
        <h2 className="text-xl font-semibold">Welcome to the Admin Panel</h2>
        <p className="text-muted-foreground mt-2">
          Manage your application content and settings from here.
        </p>
      </div>
    </ContentLayout>
  );
}
