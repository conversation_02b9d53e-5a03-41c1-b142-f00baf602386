"use client";

import { useState } from "react";

import {
	type Column,
	type ColumnDef,
	type ColumnFiltersState,
	type Row,
	type SortingState,
	type Table,
	flexRender,
	getCoreRowModel,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	useReactTable,
} from "@tanstack/react-table";

import { Check, ChevronLeft, ChevronRight, Pencil } from "lucide-react";

import { Button } from "@/components/ui/button";

import { Input } from "@/components/ui/input";

import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,

} from "@/components/ui/breadcrumb";

import { ContentLayout } from "@/components/admin-panel/content-layout";

import { cn } from "@/lib/utils";
import Link from "next/dist/client/link";

declare module "@tanstack/react-table" {
	interface TableState {
		density: "sm" | "md" | "lg";
	}
}

interface Call {
	id: string;

	caller: string;

	number: string;

	status: "Ringing" | "Answered" | "Active" | "On Hold";

	duration: number;

	pickup: string;

	dropoff: string;

	vehicle: string;

	taxiAssigned: boolean;
}

const sampleCalls: Call[] = [
	{
		id: "1",

		caller: "John Taxi Co.",

		number: "******-8294",

		status: "Active",

		duration: 8,

		pickup: "Airport Terminal B",

		dropoff: "Downtown Convention Center",

		vehicle: "Luxury Van",

		taxiAssigned: true,
	},

	{
		id: "2",

		caller: "Emergency Medical",

		number: "******-9111",

		status: "On Hold",

		duration: 2,

		pickup: "City Hospital ER",

		dropoff: "Urgent Care Clinic",

		vehicle: "Priority Ambulance",

		taxiAssigned: true,
	},

	{
		id: "3",

		caller: "City Cab",

		number: "******-1234",

		status: "Ringing",

		duration: 0,

		pickup: "Main Street",

		dropoff: "Central Park",

		vehicle: "Sedan",

		taxiAssigned: false,
	},

	{
		id: "4",

		caller: "Quick Ride",

		number: "******-5678",

		status: "Answered",

		duration: 3,

		pickup: "Bus Station",

		dropoff: "Shopping Mall",

		vehicle: "SUV",

		taxiAssigned: true,
	},

	{
		id: "5",

		caller: "Luxury Limos",

		number: "******-9012",

		status: "Active",

		duration: 10,

		pickup: "Hotel Grand",

		dropoff: "Airport",

		vehicle: "Limo",

		taxiAssigned: true,
	},
];

function getStatusColor(status: Call["status"], taxiAssigned: boolean) {
	if (!taxiAssigned) return "bg-red-100/80 dark:bg-red-900/50";

	switch (status) {
		case "Active":
			return "bg-red-200/80 dark:bg-red-800/50";

		case "Answered":
			return "bg-red-300/80 dark:bg-red-700/50";

		case "Ringing":
			return "bg-red-400/80 dark:bg-red-600/50";

		case "On Hold":
			return "bg-red議員-100/80 dark:bg-red-900/50";

		default:
			return "";
	}
}

function Filter({ column, table }: { column: Column<any>; table: Table<any> }) {
	// The first value of the column filter is retrieved.
	const firstValue = table
		.getPreFilteredRowModel()
		.flatRows[0]?.getValue(column.id);

	if (firstValue !== undefined) {
		return typeof firstValue === "number" ? (
			<Input
				type="number"
				value={(column.getFilterValue() as number) ?? ""}
				onChange={(e) => column.setFilterValue(Number(e.target.value))}
				placeholder="Filter..."
				className="w-full h-8 rounded-md border bg-background px-3 py-1 text-xs shadow-sm transition-colors focus:outline-none focus:ring-1 focus:ring-red-500"
			/>
		) : (
			<Input
				type="text"
				value={(column.getFilterValue() ?? "") as string}
				onChange={(e) => column.setFilterValue(e.target.value)}
				placeholder="Filter..."
				className="w-full min-w-[100px] h-8 rounded-md border bg-background px-3 py-1 text-xs shadow-sm transition-colors focus:outline-none focus:ring-1 focus:ring-red-500"
			/>
		);
	}

	return null;
}

const CallMonitoring = () => {
	const [editingCall, setEditingCall] = useState<Partial<Call> | null>(null);
	const [data, setData] = useState<Call[]>(sampleCalls);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
	const [globalFilter, setGlobalFilter] = useState("");
	const [density, setDensity] = useState<"sm" | "md" | "lg">("md");

	const columns: ColumnDef<Call>[] = [
		{
			accessorKey: "caller",
			header: "Caller",
			size: 160,
			meta: {
				width: "160px",
			} as { width: string },
			cell: ({ row }: { row: Row<Call> }) => {
				if (editingCall?.id === row.original.id) {
					return (
						<Input
							value={editingCall?.caller || ""}
							onChange={(e) =>
								setEditingCall((p: Partial<Call> | null) => ({
									...(p || {}),
									caller: e.target.value,
								}))
							}
							className="h-8"
						/>
					);
				}

				return row.getValue("caller");
			},
		},
		{
			accessorKey: "status",
			header: "Status",
			size: 120,
			meta: {
				width: "120px",
			} as { width: string },
			cell: ({ row }: { row: Row<Call> }) => {
				if (editingCall?.id === row.original.id) {
					return (
						<select
							value={editingCall?.status || ""}
							onChange={(e) =>
								setEditingCall((p: Partial<Call> | null) => ({
									...(p || {}),
									status: e.target.value as Call["status"],
								}))
							}
							className="h-9 rounded-md border bg-background px-3 py-1 text-sm shadow-sm transition-colors focus:outline-none focus:ring-1 focus:ring-ring"
						>
							{["Active", "Answered", "Ringing", "On Hold"].map((s) => (
								<option key={s} value={s}>
									{s}
								</option>
							))}
						</select>
					);
				}

				return row.getValue("status");
			},
		},
		{
			accessorKey: "number",
			header: "Number",
			size: 140,
			meta: {
				width: "140px",
			} as { width: string },
			cell: ({ row }: { row: Row<Call> }) => {
				if (editingCall?.id === row.original.id) {
					return (
						<Input
							value={editingCall?.number || ""}
							onChange={(e) =>
								setEditingCall((p: Partial<Call> | null) => ({
									...(p || {}),
									number: e.target.value,
								}))
							}
							className="h-8"
						/>
					);
				}

				return row.getValue("number");
			},
		},
		{
			accessorKey: "duration",
			header: "Duration",
			size: 100,
			meta: {
				width: "100px",
			} as { width: string },
			cell: ({ row }: { row: Row<Call> }) => {
				if (editingCall?.id === row.original.id) {
					return (
						<Input
							type="number"
							value={editingCall?.duration || ""}
							onChange={(e) =>
								setEditingCall((p: Partial<Call> | null) => ({
									...(p || {}),
									duration: e.target.valueAsNumber,
								}))
							}
							className="h-8"
						/>
					);
				}

				return row.getValue("duration");
			},
		},
		{
			accessorKey: "pickup",
			header: "Pickup",
			size: 200,
			meta: {
				width: "200px",
			} as { width: string },
			cell: ({ row }: { row: Row<Call> }) => {
				if (editingCall?.id === row.original.id) {
					return (
						<Input
							value={editingCall?.pickup || ""}
							onChange={(e) =>
								setEditingCall((p: Partial<Call> | null) => ({
									...(p || {}),
									pickup: e.target.value,
								}))
							}
							className="h-8"
						/>
					);
				}

				return row.getValue("pickup");
			},
		},
		{
			accessorKey: "dropoff",
			header: "Dropoff",
			size: 200,
			meta: {
				width: "200px",
			} as { width: string },
			cell: ({ row }: { row: Row<Call> }) => {
				if (editingCall?.id === row.original.id) {
					return (
						<Input
							value={editingCall?.dropoff || ""}
							onChange={(e) =>
								setEditingCall((p: Partial<Call> | null) => ({
									...(p || {}),
									dropoff: e.target.value,
								}))
							}
							className="h-8"
						/>
					);
				}

				return row.getValue("dropoff");
			},
		},
		{
			accessorKey: "vehicle",
			header: "Vehicle",
			size: 120,
			meta: {
				width: "120px",
			} as { width: string },
			cell: ({ row }: { row: Row<Call> }) => {
				if (editingCall?.id === row.original.id) {
					return (
						<Input
							value={editingCall?.vehicle || ""}
							onChange={(e) =>
								setEditingCall((p: Partial<Call> | null) => ({
									...(p || {}),
									vehicle: e.target.value,
								}))
							}
							className="h-8"
						/>
					);
				}

				return row.getValue("vehicle");
			},
		},
		{
			accessorKey: "taxiAssigned",
			header: "Taxi Assigned",
			size: 120,
			meta: {
				width: "120px",
			} as { width: string },
			cell: ({ row }: { row: Row<Call> }) => {
				if (editingCall?.id === row.original.id) {
					return (
						<select
							value={editingCall?.taxiAssigned?.toString() || ""}
							onChange={(e) =>
								setEditingCall((p: Partial<Call> | null) => ({
									...(p || {}),
									taxiAssigned: e.target.value === "true",
								}))
							}
							className="border rounded p-1"
						>
							<option value="true">Yes</option>
							<option value="false">No</option>
						</select>
					);
				}

				return row.getValue("taxiAssigned") ? "Yes" : "No";
			},
		},
		{
			id: "actions",
			size: 80,
			meta: {
				width: "80px",
			} as { width: string },
			cell: ({ row }: { row: Row<Call> }) => {
				const call = row.original;

				const handleSave = () => {
					if (editingCall) {
						setData((prev) =>
							prev.map((c) =>
								c.id === call.id ? { ...c, ...editingCall } : c,
							),
						);
						setEditingCall(null);
					}
				};

				return (
					<div className="flex items-center gap-1">
						{editingCall?.id === call.id ? (
							<Button
								variant="ghost"
								size="icon"
								onClick={handleSave}
								className="h-8 w-8"
							>
								<Check className="h-4 w-4" />
							</Button>
						) : (
							<Button
								variant="ghost"
								size="icon"
								onClick={() => setEditingCall({ ...call })}
								className="h-8 w-8"
							>
								<Pencil className="h-4 w-4" />
							</Button>
						)}
					</div>
				);
			},
		},
	];

	const table = useReactTable<Call>({
		data,

		columns,

		state: {
			sorting,

			columnFilters,

			globalFilter,

			density,
		},

		onSortingChange: setSorting,

		onColumnFiltersChange: setColumnFilters,

		onGlobalFilterChange: setGlobalFilter,

		getCoreRowModel: getCoreRowModel(),

		getPaginationRowModel: getPaginationRowModel(),

		getSortedRowModel: getSortedRowModel(),

		getFilteredRowModel: getFilteredRowModel(),
	});

	return (
		<div className="space-y-4">
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold tracking-tight">Active Calls</h2>

				<div className="flex gap-1.5">
					<Input
						placeholder="Global search..."
						value={globalFilter}
						onChange={(e) => setGlobalFilter(e.target.value)}
						className="w-48 md:w-60 h-8 text-xs bg-background placeholder:text-muted-foreground/60 border"
					/>

					<Button
						onClick={() =>
							setDensity(
								density === "md" ? "sm" : density === "sm" ? "lg" : "md",
							)
						}
						variant="outline"
						size="sm"
						className="border"
					>
						<span className="hidden md:inline">View</span> (
						{density === "sm"
							? "Compact"
							: density === "md"
								? "Normal"
								: "Expanded"}
						)
					</Button>
				</div>
			</div>

			<div className="border rounded-lg overflow-x-auto min-w-fit">
				<table className="w-full" style={{ tableLayout: "fixed" }}>
					<thead>
						{table.getHeaderGroups().map((headerGroup) => (
							<tr key={headerGroup.id}>
								{headerGroup.headers.map((header) => (
									<th
										key={header.id}
										className={cn(
											"text-left text-foreground whitespace-nowrap",
											density === "sm"
												? "px-2 py-1"
												: density === "md"
													? "px-4 py-2"
													: "px-6 py-3",
											"bg-red-50 dark:bg-red-900/80",
										)}
										style={
											header.isPlaceholder
												? {}
												: {
														width: header.getSize(),
														minWidth: header.getSize(),
													}
										}
									>
										{header.column.getCanSort() ? (
											<div
												className="flex flex-col gap-1"
												onClick={header.column.getToggleSortingHandler()}
												onKeyUp={(e) => {
													if (e.key === "Enter") {
														header.column.getToggleSortingHandler()?.(e);
													}
												}}
											>

												{flexRender(
													header.column.columnDef.header,
													header.getContext(),
												)}
												{{
													asc: " ",
													desc: " ",
												}[header.column.getIsSorted() as string] ?? null}
											</div>
										) : (
											flexRender(
												header.column.columnDef.header,
												header.getContext(),
											)
										)}
										{header.column.getCanFilter() && (
											<div className="w-full">
												<Filter column={header.column} table={table} />
											</div>
										)}
									</th>
								))}
							</tr>
						))}
					</thead>

					<tbody>
						{table.getRowModel().rows.map((row) => (
							<tr
								key={row.id}
								className={cn(
									"group relative transition-colors hover:bg-muted/50",
									getStatusColor(
										row.original.status,
										row.original.taxiAssigned,
									),
								)}
							>
								{row
									.getVisibleCells()
									.filter(
										(cell) =>
											!(
												cell.column.columnDef.meta &&
												"placeholder" in cell.column.columnDef.meta &&
												cell.column.columnDef.meta.placeholder
											),
									)
									.map((cell) => (
										<td
											key={cell.column.id}
											className={cn(
												"text-left text-foreground whitespace-nowrap",
												density === "sm"
													? "px-2 py-1"
													: density === "md"
														? "px-4 py-2"
														: "px-6 py-3",
											)}
										>
											{flexRender(
												cell.column.columnDef.cell,
												cell.getContext(),
											)}
										</td>
									))}
							</tr>
						))}
					</tbody>
				</table>
			</div>

			<div className="flex items-center justify-between mt-4">
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						size="sm"
						onClick={() => table.previousPage()}
						disabled={!table.getCanPreviousPage()}
					>
						<ChevronLeft className="h-4 w-4 mr-1" />
						Previous
					</Button>

					<Button
						variant="outline"
						size="sm"
						onClick={() => table.nextPage()}
						disabled={!table.getCanNextPage()}
					>
						Next
						<ChevronRight className="h-4 w-4 ml-1" />
					</Button>

					<span className="flex items-center gap-1">
						Page {table.getState().pagination.pageIndex + 1} of{" "}
						{table.getPageCount()}
					</span>
				</div>

				<div className="flex items-center gap-2">
					<select
						value={table.getState().pagination.pageSize}
						onChange={(e) => table.setPageSize(Number(e.target.value))}
						className="h-9 rounded-md border bg-background px-3 py-1 text-sm shadow-sm transition-colors focus:outline-none focus:ring-1 focus:ring-ring"
					>
						{[10, 20, 30, 40].map((size) => (
							<option key={size} value={size}>
								Show {size}
							</option>
						))}
					</select>
				</div>
			</div>
		</div>
	);
};
const CallsPage = () => {
	return (
		<ContentLayout title="Calls">
			<Breadcrumb>
				<BreadcrumbItem>
					<BreadcrumbLink asChild>
						<Link href="/">Home</Link>
					</BreadcrumbLink>
				</BreadcrumbItem>
			</Breadcrumb>
			<div className="container py-6">
				<CallMonitoring />
			</div>
		</ContentLayout>
	);
};

export default CallsPage;
