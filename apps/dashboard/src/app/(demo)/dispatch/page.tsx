"use client";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import type { Call } from "@/types/call";
import { Dialog, DialogContent, DialogTitle } from "@radix-ui/react-dialog";
import { CheckIcon, XIcon, MessageSquareIcon, EditIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";

export default function EnhancedDispatchCenter() {
	const [calls, setCalls] = useState<Call[]>([]);
	const [selectedCall, setSelectedCall] = useState<Call | null>(null);
	const [smsMessage, setSmsMessage] = useState("");
	const [receivedMessage, setReceivedMessage] = useState("");
	const [isSmsModalOpen, setIsSmsModalOpen] = useState(false);
	const [isEditModalOpen, setIsEditModalOpen] = useState(false);

	useEffect(() => {
		const interval = setInterval(() => {
			const newCall: Call = {
				id: Math.random().toString(36).substr(2, 9),
				status: "incoming",
				number: `07${Math.floor(Math.random() * 90000000 + 10000000)}`,
				location: ["dom mece", "ogukjanka", "centar", ""][
					Math.floor(Math.random() * 4)
				],
				destination: ["Hospital", "Airport", "Downtown", ""][
					Math.floor(Math.random() * 4)
				],
				vehicle: Math.floor(Math.random() * 30 + 1).toString(),
				time: new Date().toTimeString().split(" ")[0],
			};
			setCalls((prevCalls) => [newCall, ...prevCalls.slice(0, 14)]);
		}, 5000);

		return () => clearInterval(interval);
	}, []);

	const handleCallAction = (
		id: string,
		action: "accept" | "reject" | "complete",
	) => {
		setCalls((prevCalls) =>
			prevCalls.map((call) => {
				if (call.id === id) {
					switch (action) {
						case "accept":
							return { ...call, status: "active" as const };
						case "reject":
						case "complete":
							return { ...call, status: "completed" as const };
						default:
							return call;
					}
				}
				return call;
			}),
		);
	};

	const handleSendSms = () => {
		if (selectedCall && smsMessage) {
			console.log(`Sending SMS to ${selectedCall.number}: ${smsMessage}`);
			setReceivedMessage("Message received: OK");
			setSmsMessage("");
		}
	};

	const handleEditCall = (updatedCall: Call) => {
		setCalls((prevCalls) =>
			prevCalls.map((call) =>
				call.id === updatedCall.id ? updatedCall : call,
			),
		);
		setIsEditModalOpen(false);
	};

	const openEditModal = (call: Call) => {
		setSelectedCall(call);
		setIsEditModalOpen(true);
	};

	return (
		<ContentLayout title="Dispatch Panel">
			<div className="mb-4">
				<Input type="text" placeholder="Search..." className="max-w-sm" />
			</div>

			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Статус</TableHead>
						<TableHead>Телефон</TableHead>
						<TableHead>Локација</TableHead>
						<TableHead>Дестинација</TableHead>
						<TableHead>Возило</TableHead>
						<TableHead>Време</TableHead>
						<TableHead>Акции</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{calls.map((call) => (
						<TableRow key={call.id}>
							<TableCell
								className="py-2"
								onDoubleClick={() => openEditModal(call)}
							>
								<Badge
									variant={
										call.status === "incoming"
											? "destructive"
											: call.status === "active"
												? "default"
												: "secondary"
									}
								>
									{call.status}
								</Badge>
							</TableCell>
							<TableCell
								className="py-2"
								onDoubleClick={() => openEditModal(call)}
							>
								{call.number}
							</TableCell>
							<TableCell className="py-2">{call.location || "-"}</TableCell>
							<TableCell className="py-2">{call.destination || "-"}</TableCell>
							<TableCell className="py-2">{call.vehicle || "-"}</TableCell>
							<TableCell className="py-2">{call.time}</TableCell>
							<TableCell className="py-2">
								<div className="flex gap-2">
									{call.status === "incoming" && (
										<>
											<Button
												size="icon"
												variant="ghost"
												onClick={() => handleCallAction(call.id, "accept")}
											>
												<CheckIcon className="h-4 w-4" />
											</Button>
											<Button
												size="icon"
												variant="ghost"
												onClick={() => handleCallAction(call.id, "reject")}
											>
												<XIcon className="h-4 w-4" />
											</Button>
										</>
									)}
									{call.status === "active" && (
										<Button
											size="icon"
											variant="ghost"
											onClick={() => handleCallAction(call.id, "complete")}
										>
											<CheckIcon className="h-4 w-4" />
										</Button>
									)}
									<Button
										size="icon"
										variant="ghost"
										onClick={() => {
											setSelectedCall(call);
											setIsSmsModalOpen(true);
										}}
									>
										<MessageSquareIcon className="h-4 w-4" />
									</Button>
									<Button
										size="icon"
										variant="ghost"
										onClick={() => openEditModal(call)}
									>
										<EditIcon className="h-4 w-4" />
									</Button>
								</div>
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>

			<div className="mt-4">
				<Link
					href="/home"
					className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
				>
					Go to Home Dashboard
				</Link>
			</div>

			<Dialog open={isSmsModalOpen} onOpenChange={setIsSmsModalOpen}>
				<DialogContent>
					<DialogTitle>Send SMS to {selectedCall?.number}</DialogTitle>
					<div className="space-y-4">
						<textarea
							placeholder="Type your message here"
							value={smsMessage}
							onChange={(e) => setSmsMessage(e.target.value)}
							className="w-full p-2 border border-gray-300 rounded"
						/>
						<Button onClick={handleSendSms}>Send SMS</Button>
						{receivedMessage && (
							<div className="p-2 mt-4 rounded bg-muted">
								<p className="text-sm">{receivedMessage}</p>
							</div>
						)}
					</div>
				</DialogContent>
			</Dialog>

			<Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
				<DialogContent>
					<DialogTitle>Edit Call Details</DialogTitle>
					{selectedCall && (
						<div className="space-y-4">
							<div>
								<label htmlFor="phone" className="text-sm font-medium">
									Phone
								</label>
								<Input
									id="phone"
									value={selectedCall.number}
									onChange={(e) =>
										setSelectedCall({
											...selectedCall,
											number: e.target.value,
										})
									}
								/>
							</div>
							<div>
								<label htmlFor="location" className="text-sm font-medium">
									Location
								</label>
								<Input
									id="location"
									value={selectedCall.location}
									onChange={(e) =>
										setSelectedCall({
											...selectedCall,
											location: e.target.value,
										})
									}
								/>
							</div>
							<div>
								<label htmlFor="destination" className="text-sm font-medium">
									Destination
								</label>
								<Input
									id="destination"
									value={selectedCall.destination}
									onChange={(e) =>
										setSelectedCall({
											...selectedCall,
											destination: e.target.value,
										})
									}
								/>
							</div>
							<div>
								<label htmlFor="vehicle" className="text-sm font-medium">
									Vehicle
								</label>
								<Input
									id="vehicle"
									value={selectedCall.vehicle}
									onChange={(e) =>
										setSelectedCall({
											...selectedCall,
											vehicle: e.target.value,
										})
									}
								/>
							</div>
							<Button onClick={() => handleEditCall(selectedCall)}>
								Save Changes
							</Button>
						</div>
					)}
				</DialogContent>
			</Dialog>
		</ContentLayout>
	);
}
