# CONVENTIONS - Comprehensive Guide

    Naming convention typically follows these guidelines:

## PascalCase

    Modules: Use PascalCase and end with Module. Example: User Module.
    Services: Use PascalCase and end with Service. Example: User Service.
    Controllers: Use PascalCase and end with Controller. Example: User Controller.
    DTOs (Data Transfer Objects): Use PascalCase and end with Dto. Example: CreateUser Dto.
    Interfaces: Use PascalCase and may start with an I. Example: IUser .
    Enums: Use PascalCase. Example: User Role.
    CamelCase is typically used in the following situations in NestJS:

## CamelCase

    Variables: For variable names, use camelCase. Example: userName.
    Function/Method Names: Method names should also be in camelCase. Example: getUser Info().
    Private Properties: Private class properties often use camelCase. Example: private userId.
    CamelCase helps differentiate these elements from classes and types, which follow PascalCase conventions.

## 🔍 Key Technologies

1. **Backend Framework**: NestJS
2. **Frontend Framework**: React
3. **ORM**: Drizzle
4. **Database**: PostgreSQL
5. **Caching**: Redis
6. **Package Management**: pnpm or bun
7. **Containerization**: Docker


## 🚀 Deployment Workflow

```mermaid
sequenceDiagram
    participant Dev as Developer
    participant Git as Git Repository
    participant CI as CI/CD
    participant Docker as Docker
    participant Cloud as Cloud Platform

    Dev->>Git: Push Code
    Git->>CI: Trigger Build
    CI->>CI: Run Tests
    CI->>Docker: Build Container
    Docker->>Cloud: Deploy Application
```

## 🔒 Security and Performance Considerations

- **JWT Authentication**
- **Rate Limiting**
- **Caching with Redis**
- **Environment-specific configurations**
- **Strict TypeScript type checking**
- **RBAC (Role-Based Access Control)**


---

## 🚀 Getting Started

---

## 🚫 DON'Ts

### 1. Never Modify Core Configuration Files

- `tsconfig.json`
- `drizzle.config.ts`
- `package.json` (root)
- Dockerfile
- docker-compose.yml

### 2. Avoid Direct Dependency Modifications

- Use workspace dependencies
- Always add dependencies to specific packages
- Respect existing dependency management

---

## ❗ Important Configuration Guidelines

### 1. Environment Variables

```bash
# Always use .env.local for local development
# Use .env.test for testing
# Production environment in .env
```

### 2. Path Aliases

```typescript
// Good: Use defined path aliases
import { someUtil } from '@packages/utils'
import { CoreService } from '@core/services'

// Bad: Avoid relative imports outside immediate context
import { ... } from '../../../utils/something'
```

## 🔒 Security Recommendations

### 1. Secrets Management

```typescript
// Always use environment variables
const JWT_SECRET = process.env.JWT_SECRET
// Never hardcode secrets

// Use zod for runtime validation
const configSchema = z.object({
  DATABASE_URL: z.string().url(),
  JWT_SECRET: z.string().min(32)
})
```

### 2. Authentication

```typescript
// Use built-in NestJS authentication
@UseGuards(JwtAuthGuard)
class ProtectedController {
  // Implement role-based access control
}
```

### Role-Based Access Control - Nestjs-roles libary
To implement role-based access control, we need to define the roles and create a guard to check for these roles.

#### Step 1: Role Definition
Define roles using TypeScript enums:
```typescript
export enum Role {
  USER,
  ADMIN,
  DISPATCHER,
  OPERATOR, 
  MANAGER,
  OWNER,
  DRIVER,
  GUEST,
  API
}
```
#### Step 2: Guard Creation
The package provides a `createRolesGuard` function that creates a guard based on how to extract roles from the request context:
```typescript
function getRole(context: ExecutionContext) {
  const { session } = context.switchToHttp().getRequest();
  if (!session) return;
  return session.role; // Can return single role or array of roles
}

export const Roles = createRolesGuard<Role>(getRole);
```
#### Step 3: Global Guard Setup
Register the guard globally in the application:
```typescript
const app = await NestFactory.create(AppModule);
const reflector = app.get<Reflector>(Reflector);
app.useGlobalGuards(new Roles(reflector));
```
#### Step 4: Usage in Controllers
The guard can be used in two ways:

* At the controller level to set default access:
```typescript
@Controller('secrets')
@Roles.Params(true) // Allows access to any authenticated user
export class SecretsController {}
```
* At the route level to override controller-level settings:
```typescript
@Patch(':id')
@Roles.Params(Role.ADMIN) // Only allows admin access
async update() {}
```
#### Step 5: Role Checking Logic
The guard implements several access control rules:
* If you specify `false`, only non-authenticated users can access
* If you specify `true`, any authenticated user can access
* If you specify specific roles, only users with those roles can access
* For users with multiple roles, access is granted if they have any of the required roles

#### Step 6: Type Safety
The package ensures type safety by:
* Using TypeScript generics to ensure role types match enum
* Providing compile-time checking of role values
* Ensuring you can't accidentally use invalid role values



---

## 🏗️ Extending the Boilerplate

### 1. Adding New Features

```bash
# Generate NestJS resource
npx @nestjs/cli generate resource users

# Generate React component
# Use your preferred method or CLI

---

## ⚠️ Common Pitfalls

### 1. Performance Considerations

```typescript
// Use lazy loading
@Injectable()
class HeavyService {
  @Inject(lazy(() => DependentService))
  private dependentService: DependentService
}

// Optimize database queries
const results = await this.repository
  .createQueryBuilder()
  .select()
  .where()
  .limit(10)
  .getMany()
```

### 2. Error Handling

```typescript
// Use consistent error handling
@Catch()
class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    // Centralized error management
  }
}
```

---

## 🔍 Debugging and Monitoring

### 1. Logging

Configuration of the `pino-logger` in a NestJS application, using the `LoggerModule` to set up logging with customizable options. Here's a brief explanation of the implementation:

```typescript
// src/app.module.ts
LoggerModule.forRootAsync({
  inject: [ConfigService],
  useFactory: (configService: ConfigService) => ({
    pinoHttp: {
      level: configService.get<string>("LOG_LEVEL") || "debug", // Set log level from config or default to debug
      transport: 
        configService.get("NODE_ENV") !== "production" // Check if not in production
          ? {
              target: "pino-pretty", // Use pretty print for logs in development
              options: {
                colorize: true, // Enable colorized output
                singleLine: true, // Output logs in a single line
                translateTime: "UTC:yyyy-mm-dd HH:MM:ss.l", // Format timestamp
                ignore: "pid,hostname", // Ignore certain fields in logs
              },
            }
          : undefined, // No transport in production
    },
  }),
}),
```

### Key Points:
1. **Asynchronous Configuration**: The logger is configured asynchronously using `forRootAsync`, allowing for dependency injection (e.g., `ConfigService`).
2. **Log Level**: The logging level is dynamically set based on the application's configuration, defaulting to "debug" if not specified.
3. **Transport Options**: In non-production environments, the logger uses `pino-pretty` for more readable log output, with options for colorization and timestamp formatting.
4. **Production Settings**: In production, the transport is set to `undefined`, which means logs will not be prettified, optimizing performance.

### Logger Substitution:
- The implementation allows for a custom logger to be injected into the NestJS application, replacing the default logger. This is done by calling `app.useLogger(app.get(Logger))` in the `main.ts` file.
- The `bufferLogs` option ensures that logs are buffered until the custom logger is ready, which is crucial for standalone applications.
- The `LoggerService` accept arguments without a second context argument. This change improves compatibility with `pino` logging methods and simplifies usage.




### 2. Tracing

```typescript
// Use OpenTelemetry or similar for distributed tracing
@Span()
async processOrder() {
  // Traceable method
}
```

---

## 💡 Recommended Development Flow

```mermaid
graph TD
    A[Start Development] --> B{Feature/Fix}
    B --> |Small Change| C[Create Branch]
    B --> |Major Feature| D[RFC/Design Discussion]
    C --> E[Implement]
    D --> E
    E --> F[Write Tests]
    F --> G[Run Linters]
    G --> H[Create Pull Request]
    H --> I{Code Review}
    I --> |Approved| J[Merge]
    I --> |Changes Needed| E
```

---

## 🎯 Best Practices Checklist

- [ ] Use TypeScript strict mode
- [ ] Write comprehensive tests
- [ ] Follow SOLID principles
- [ ] Use dependency injection
- [ ] Implement proper error handling  
- [ ] Use environment-specific configurations
- [ ] Keep sensitive data out of version control
- [ ] Role enum values should use UPPERCASE for consistency with existing implementation
- [ ] Error handling must:
  - Use ErrorResponseDto format
  - Include correlation ID from headers or generate new
  - Log structured error context
  - Use i18n translation keys for messages
- [ ] Validation decorators:
  - Must return translation keys instead of hardcoded messages
  - Should use @ApiField() descriptions- 
-  [ ] JWT authentication must include role claims and expiration

---

## 🔍 Debugging Workflow

```bash
# Verify workspace configuration
bun list

# Check individual package configurations
for pkg in packages/* apps/*; do
  echo "$pkg package.json:"
  cat "$pkg/package.json"
done

# Validate dependencies
bun install --verify
```

---

## 🚨 Common Pitfalls

- Inconsistent workspace package names
- Missing `package.json` in workspace packages
- Incorrect dependency references
- Not building workspace packages before Docker build

- Always use `bun install --frozen-lockfile` in Docker
- Copy only necessary files in build stages
- Use multi-stage builds to minimize image size
- Verify workspace dependencies before containerization
