{
  "extends": "@repo/typescript-config/base.json",
  "compilerOptions": {
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true, // Already in base
    "strict": true, // Already in base
    "noEmit": true, // Next.js handles transpilation
    "esModuleInterop": true, // Already in base
    "module": "esnext", // Good for Next.js
    "moduleResolution": "bundler", // Or "node" if "bundler" is problematic
    "resolveJsonModule": true, // Already in base
    "isolatedModules": true, // Already in base
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
      // If you need to reference other packages in the monorepo for the dashboard:
      // "@monorepo/types": ["../../packages/types/src"] // Example if you create a shared types package
    },
    "types": ["node", "jest", "@testing-library/jest-dom"] // Add "node" for things like process.env, "jest" for testing
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": ["node_modules"]
}