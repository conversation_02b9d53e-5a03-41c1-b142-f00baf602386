# shadcn/ui Sidebar – Modern Sidebar Template for Next.js

A robust, fully featured sidebar system for Next.js powered by [shadcn/ui](https://ui.shadcn.com), built for desktop and mobile, with a composable registry-driven architecture and extensive customization.

---
## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [System Architecture](#system-architecture)
- [Folder Structure](#folder-structure)
- [Installation & Quick Start](#installation--quick-start)
- [Usage](#usage)
- [Registry & Component System](#registry--component-system)
- [Development & Contribution Guide](#development--contribution-guide)
- [Best Practices & Troubleshooting](#best-practices--troubleshooting)
- [License & Attribution](#license--attribution)

---

## Overview

This project delivers a modular, registry-based sidebar template for Next.js, supporting fast onboarding, easy extension, and instant UI upgrades for admin panels and dashboard use cases.

- **Demo**: [shadcn-ui-sidebar.salimi.my](https://shadcn-ui-sidebar.salimi.my)
- **Author**: [Salimi](https://www.salimi.my)

---

## Features

- **Retractable mini/wide sidebar**
- **Menu grouping, submenus, collapsible items**
- **Mobile sheet menu**
- **Theme support, dark/light mode**
- **Registry-driven install and extension**
- **Typed, modular component structure**

---

## System Architecture

```mermaid
graph TD
    subgraph "External User/Consumer"
      Z1[Install via npx & registry JSON]
    end
    subgraph "Registry System"
      A1["public/registry/shadcn-sidebar.json<br>(Distributable registry)"] 
      A2["registry/registry-components.ts<br>(Registry generator)"]
      S1["registry/schema.ts<br>(Types/validation)"]
    end
    subgraph "Source Code"
      C1["src/components/"]
      H1["src/hooks/"]
      L1["src/lib/menu-list.ts"]
    end
    subgraph "UI Application"
      U1["src/app/layout.tsx / AdminPanelLayout"]
      U2["Sidebar, Menu, UI pages"]
    end

    Z1-->|"npx shadcn@latest add ..."|A1
    A2-->|aggregates code from|C1
    A2-->|aggregates code from|H1
    A2-->|uses menu logic from|L1
    A2-->|validates with|S1
    A2-->|outputs|A1
    A1-->|consumed by|Z1
    C1-->|used by|U2
    H1-->|used by|U2
    L1-->|provides menu logic|U2
    U1-->|renders|U2
```

**How it Works**:  
- `registry-components.ts` scans and collects sidebar-related components & hooks, then outputs a full registry entry (`shadcn-sidebar.json`).
- The sidebar app consumes this registry at runtime; external users/projects can install the full sidebar package via a single registry operation.

---

## Folder Structure

```text
├── src/
│   ├── app/                  # Next.js app routes & layout (entry point)
│   ├── components/           # All sidebar/admin UI components (grouped by domain)
│   ├── hooks/                # Custom hooks for sidebar state and utilities
│   ├── lib/menu-list.ts      # Configurable menu/group structure logic
│   └── ...
├── registry/
│   ├── registry-components.ts # Registry generator script
│   ├── schema.ts              # Registry type definitions & Zod validation
│   ├── index.ts               # Entry logic (if present)
├── public/
│   └── registry/shadcn-sidebar.json # Distributable registry export for external installs
├── tests/                    # Unit and system tests
├── README.md                 # (This file)
├── CONVENTIONS.md            # Project-wide code/style conventions
```

---

## Installation & Quick Start

Clone and setup:
```bash
git clone https://github.com/salimi-my/shadcn-ui-sidebar
cd shadcn-ui-sidebar
npm install
```

Add sidebar block from registry (**shadcn/ui v2+**):
```bash
npx shadcn@latest add https://shadcn-ui-sidebar.salimi.my/registry/shadcn-sidebar.json
# or
npx shadcn@latest add https://raw.githubusercontent.com/salimi-my/shadcn-ui-sidebar/refs/heads/master/public/registry/shadcn-sidebar.json
```

Start development server:
```bash
npm run dev
```

---

## Usage

**Example: Integrating Sidebar Layout**
```tsx
// app/layout.tsx
import AdminPanelLayout from "@/components/admin-panel/admin-panel-layout";
export default function Layout({ children }) {
  return <AdminPanelLayout>{children}</AdminPanelLayout>;
}

// app/page.tsx
import { ContentLayout } from "@/components/admin-panel/content-layout";
export default function Page() {
  return (
    <ContentLayout title="Test">
      <div>Test Page</div>
    </ContentLayout>
  );
}
```

---

## Registry & Component System

- The **Registry** defines a set of components, hooks, menu logic, dependencies, and Tailwind config needed for the sidebar block.
- `registry-components.ts` aggregates valid components (`src/components`) and hooks (`src/hooks`), combines them with logic (`lib/menu-list.ts`), describes dependencies, and outputs a JSON registry.
- The JSON registry can be referenced by external tools or imported to rapidly scaffold full sidebar setups.
- **Types** and validation are enforced via `registry/schema.ts`.

**Adding or Customizing the Sidebar:**
- Add or edit entries in `src/components/admin-panel/`, `src/hooks/`, and update the output registry as needed.
- Change menu structure by updating `src/lib/menu-list.ts`.

---

## Development & Contribution Guide

- **Naming Conventions**:
  - Modules, Services, Controllers: PascalCase (e.g., UserModule)
  - Variables, functions: camelCase (e.g., getUserInfo)
  - DTOS, Interfaces: PascalCase, interfaces may start with I
- **Config Guidelines**:
  - Use `.env.local` (development), `.env.test` (test), `.env` (prod)
  - Use path aliases, avoid messy relative imports
- **Security & Performance**:  
  - Use JWT auth, role-based guards, Redis caching for performance
  - TypeScript strict mode, SOLID principles, error handling

- **Recommended Dev Flow**:
```mermaid
graph TD
  A[Start] --> B{Feature/Fix}
  B --> |Small Change| C[Create Branch]
  B --> |Major Feature| D[RFC/Design]
  C --> E[Implement]
  D --> E
  E --> F[Test]
  F --> G[Linter]
  G --> H[PR]
  H --> I{Review}
  I --> |Approved| J[Merge]
  I --> |Needs Changes| E
```

---

## Best Practices & Troubleshooting

- Always validate registry and package setup.
- If extending: use proper file conventions, update relevant sections in the registry/component mapping.
- For Docker or deployment, ensure all dependencies and registry files are built/copied as needed.
- See CONVENTIONS.md for extended checklists and troubleshooting.

---

## License & Attribution

- Licensed under MIT.
- Built by [Salimi](https://www.salimi.my) atop [shadcn/ui](https://ui.shadcn.com).
- Source available at [GitHub](https://github.com/salimi-my/shadcn-ui-sidebar).
