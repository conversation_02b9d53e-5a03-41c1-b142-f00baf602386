{"name": "@monorepo/dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "bun --bun next dev", "build": "bun run --bun next build", "start": "bun --bun next start", "lint": "bun --bun next lint", "typecheck": "bunx tsc --noEmit", "test": "bun test", "registry:build": "tsx ./scripts/build-registry.ts"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-table": "^8.20.6", "@testing-library/jest-dom": "^6.6.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "geist": "^1.3.0", "immer": "^10.1.1", "jest": "^29.7.0", "lucide-react": "^0.474.0", "next": "^15.3.2", "next-themes": "^0.4.3", "react": "^18", "react-dom": "^18", "tailwind": "^4.0.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.28", "zustand": "^4.5.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/bun": "^1.2.2", "@types/react": "^18", "@types/react-dom": "^18", "@testing-library/jest-dom": "^6.6.3", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "jest": "^29.7.0", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "typescript": "^5"}}