{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "src", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "ES2021", "module": "NodeNext", "moduleResolution": "NodeNext", "sourceMap": true, "noEmit": false, "composite": true, "lib": ["ES2021"], "types": ["node"]}, "references": [{"path": "../../packages/db"}], "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}