// apps/main-api/src/types/bot-context.ts
// biome-ignore lint/style/useImportType: <explanation>
import {
	CallbackData,
	DeriveDefinitions,
	Bot as GramioBot,
	CallbackQueryContext as GramioCallbackQueryContext,
	CallbackQueryShorthandContext as GramioCallbackQueryShorthandContext,
	ChosenInlineResultContext as GramioChosenInlineResultContext,
	Context as GramioContext,
	InlineQueryContext as GramioInlineQueryContext,
	MessageContext as GramioMessageContext,
	MessageReactionContext as GramioMessageReactionContext,
	Require,
	TelegramUpdate,
	UpdateName,
} from "gramio";

import type { BotConfigFromDB, TenantFromDB } from "./database.js";

import type {
	EnterExit as GramioEnterExit,
	InActiveSceneHandlerReturn as GramioInActiveSceneHandlerReturn,
	StateTypesDefault,
} from "@gramio/scenes";

import type { i18n as I18nInstanceType } from "../shared/locales/index.js";

// 1. App-Specific Error Definitions
export type AppBotErrorDefinitions = {
	[K in UpdateName | "global"]: Error;
} & {
	"prompt-cancel": Error;
};

// 2. Tenant and Bot Info Structures
export interface TenantInfo extends Pick<TenantFromDB, "id" | "name"> {}
export interface BotInstanceInfo
	extends Pick<BotConfigFromDB, "id" | "botUsername"> {}

// 3. Define what YOUR .derive() function adds to the context
export interface AppGlobalDeriveExtensions {
	readonly t: ReturnType<(typeof I18nInstanceType)["buildT"]>;
	tenant?: TenantInfo;
	botInfo?: BotInstanceInfo;
	tenantId?: string;
	userRoles?: string[];
	tenantBotApiClient?: GramioBot<AppBotErrorDefinitions, AppDeriveShape>["api"];
}

// 4. Define the SHAPE for GramIO's DeriveDefinitions
export type AppDeriveShape = DeriveDefinitions & {
	[K in UpdateName | "global"]?: Partial<AppGlobalDeriveExtensions>;
};

// 5. Define your main AppBot type.
export type AppBot = GramioBot<AppBotErrorDefinitions, AppDeriveShape>;

// 6. Helper type for guaranteed derived properties
type GuaranteedDerivedProperties = Require<AppGlobalDeriveExtensions, "t" | "tenantBotApiClient">;
// Helper type for the remaining optional derived properties
type OptionalDerivedProperties = Partial<Omit<AppGlobalDeriveExtensions, "t" | "tenantBotApiClient">>;

// This combines them: 't' and 'tenantBotApiClient' are non-optional, others are optional.
type CoreAppDerivedContext = GuaranteedDerivedProperties & OptionalDerivedProperties;

// 7. The Fully Augmented Base Context Type
export type AppBaseContext = GramioContext<AppBot> & CoreAppDerivedContext;

// 8. Specific Augmented Context Types
export type BasicSceneMethods = GramioEnterExit;
export type ActiveSceneMethods<
	P = any,
	S extends StateTypesDefault = any,
> = GramioInActiveSceneHandlerReturn<P, S>;

// Message Reaction Context
export type AppMessageReactionContext = GramioMessageReactionContext<AppBot> & CoreAppDerivedContext;

// Message Context
export type AppMessageContext = GramioMessageContext<AppBot> & CoreAppDerivedContext & {
	scene: BasicSceneMethods | ActiveSceneMethods<any, any>;
	mediaGroup?: AppMessageContext[];
	mediaGroupId?: string;
};

// Command Context
export type AppCommandContext = AppMessageContext &
	Require<GramioMessageContext<AppBot>, "from"> & {
		args: string | null;
	};

// Callback Query Context
export type AppCallbackQueryContext = GramioCallbackQueryContext<AppBot> & CoreAppDerivedContext & {
	scene: BasicSceneMethods | ActiveSceneMethods<any, any>;
};

export type AppCallbackQueryShorthandContext<T extends CallbackData<any>> =
	GramioCallbackQueryShorthandContext<AppBot, T> & CoreAppDerivedContext & {
		scene: BasicSceneMethods | ActiveSceneMethods<any, any>;
	};

export type AppInlineQueryContext = GramioInlineQueryContext<AppBot> & CoreAppDerivedContext;
export type AppChosenInlineResultContext =
	GramioChosenInlineResultContext<AppBot> & CoreAppDerivedContext;

// Context Type for Input to Your Main `derive` Function
export type ContextBeforeAppDerive = GramioContext<GramioBot<any, any>> & {
	from?: { languageCode?: string; id?: number };
	chat?: { id: number };
	update: TelegramUpdate;
	updateId: number;
	updateType: UpdateName;
	session?: any;
	scene?: BasicSceneMethods;
};
