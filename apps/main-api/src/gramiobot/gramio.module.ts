// src/gramiobot/gramio.module.ts
import { Module } from "@nestjs/common";
// Import Modules needed by BotProcessingService and Handlers
import { ConfigModule } from "../config/config.module.js";
import { DatabaseModule } from "../services/database.module.js"; // Import your DatabaseModule
import { RedisModule } from "../services/redis.module.js";

import { CallbackQueryHandlersService } from "../commands/callback-query-handlers.service.js";
import { ChosenInlineResultHandlersService } from "../commands/chosen-inline-result-handlers.service.js";
// Import Handler Services (using their class names for providers)
import { CommandHandlersService } from "../commands/command-handlers.service.js";
import { InlineQueryHandlersService } from "../commands/inline-query-handlers.service.js";
import { ReactionHandlersService } from "../commands/reaction-handlers.service.js";
import { StartCommandHandlersService } from "../commands/start-handlers.service.js";
import { SceneCommandHandlersService } from "../scenes/scenes-command-handlers.service.js";

import { RoleBasedAccessMiddleware } from "../middleware/role-based-access.middleware.js"; // Middleware is used by handler services

// Import the main Bot Processing Service and Controller
import { BotProcessingService } from "./bot-processing.service.js";
import { BotController } from "./gramio.controller.js";

// Remove the old single bot service setup
// import { BotService } from "./gramio.service";
// import { BOT_INSTANCE } from "../types/bot.constants";
// import { AppConfigService } from "../config/app-config.service";
// import { HooksService } from "../hooks/hooks.service"; // Hooks are integrated into BotProcessingService now

@Module({
	imports: [
		// Import necessary modules
		ConfigModule, // Provides AppConfigService
		RedisModule, // Provides RedisService
		DatabaseModule, // Provides the Drizzle DB instance ('DATABASE' token) and DatabaseService
		// ScenesModule, // Keep if you have global scene registration logic here, otherwise integrate scenes directly in BotProcessingService
	],
	controllers: [BotController], // The webhook controller
	providers: [
		BotProcessingService, // The main multi-tenant processing service

		// Provide DatabaseService

		// Provide RoleBasedAccessMiddleware so it can be injected into handlers
		RoleBasedAccessMiddleware,

		// Provide ALL Handler services
		CommandHandlersService,
		CallbackQueryHandlersService,
		StartCommandHandlersService,
		InlineQueryHandlersService,
		ReactionHandlersService,
		ChosenInlineResultHandlersService,
		SceneCommandHandlersService,

		// Remove providers related to the old single bot service
		// HooksService, // Integrated into BotProcessingService derive
		// {
		// 	provide: "BotConfig", // No longer needed
		// 	useFactory: (config: AppConfigService) => ({
		// 		botToken: config.botToken,
		// 	}),
		// 	inject: [AppConfigService],
		// },
		// {
		// 	provide: BOT_INSTANCE, // No longer needed
		// 	useFactory: async (botService: BotService) => {
		// 		return botService.getInitializedBot();
		// 	},
		// 	inject: [BotService],
		// },
	],
	// Export BotProcessingService if other parts of your app need to interact with it
	exports: [BotProcessingService],
})
export class BotModule {}
