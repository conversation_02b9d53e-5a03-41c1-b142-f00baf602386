import {
	<PERSON>,
	Controller,
	Get,
	HttpStatus,
	Lo<PERSON>,
	Param,
	Post,
	Res,
} from "@nestjs/common";
// biome-ignore lint/style/useImportType: <explanation>
import { BotProcessingService } from "./bot-processing.service.js";

@Controller()
export class BotController {
	private readonly logger = new Logger(BotController.name);

	constructor(private readonly botProcessingService: BotProcessingService) {}

	@Get()
	async healthCheck(): Promise<string> {
		return "Bot is running!";
	}

	/**
	 * Webhook endpoint for tenant bots
	 * @param webhookPathSegment The webhook path segment
	 * @param update The Telegram update
	 * @param reply The response object
	 */
	@Post("webhook/:webhookPathSegment")
	async handleUpdate(
		@Param("webhookPathSegment") webhookPathSegment: string,
		@Body() update: any,
		@Res() reply: any,
	) {
		this.logger.debug(`Webhook received for segment: ${webhookPathSegment}`);

		try {
			await this.botProcessingService.processUpdateForSegment(
				webhookPathSegment,
				update,
			);
			reply.status(HttpStatus.OK).send("OK");
		} catch (error) {
			this.logger.error(
				`Error processing update for ${webhookPathSegment}:`,
				error,
			);
			reply.status(HttpStatus.INTERNAL_SERVER_ERROR).send("Error");
		}
	}
}
