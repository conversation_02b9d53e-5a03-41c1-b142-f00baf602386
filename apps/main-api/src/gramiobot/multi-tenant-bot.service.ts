// biome-ignore lint/style/useImportType: <explanation>
import {
	Inject,
	Injectable,
	Lo<PERSON>,
	OnModuleDestroy,
	OnModuleInit,
	forwardRef,
} from "@nestjs/common";
// biome-ignore lint/style/useImportType: <explanation>
import { Bo<PERSON> } from "gramio";

import { CallbackQueryHandlersService } from "../commands/callback-query-handlers.service.js";
import { ChosenInlineResultHandlersService } from "../commands/chosen-inline-result-handlers.service.js";
import { CommandHandlersService } from "../commands/command-handlers.service.js";
import { InlineQueryHandlersService } from "../commands/inline-query-handlers.service.js";
import { ReactionHandlersService } from "../commands/reaction-handlers.service.js";
import { StartCommandHandlersService } from "../commands/start-handlers.service.js";
import { SceneCommandHandlersService } from "../scenes/scenes-command-handlers.service.js";
// biome-ignore lint/style/useImportType: <explanation>
import { TenantBotFactoryService } from "../services/tenant-bot-factory.service.js";
// biome-ignore lint/correctness/noUnusedImports: <explanation>
// biome-ignore lint/style/useImportType: <explanation>
import { AppBotErrorDefinitions } from "../types/bot-context.js";

/**
 * Service responsible for managing multi-tenant bot instances
 */
@Injectable()
export class MultiTenantBotService implements OnModuleInit, OnModuleDestroy {
	private readonly logger = new Logger(MultiTenantBotService.name);
	private activeTenantBots: Map<string, Bot<AppBotErrorDefinitions>> =
		new Map();

	constructor(
		private readonly tenantBotFactory: TenantBotFactoryService,

		// Handler services
		@Inject(forwardRef(() => CommandHandlersService))
		private readonly commandHandlers: CommandHandlersService,
		@Inject(forwardRef(() => CallbackQueryHandlersService))
		private readonly callbackQueryHandlers: CallbackQueryHandlersService,
		@Inject(forwardRef(() => StartCommandHandlersService))
		private readonly startCommandHandlers: StartCommandHandlersService,
		@Inject(forwardRef(() => InlineQueryHandlersService))
		private readonly inlineQueryHandlers: InlineQueryHandlersService,
		@Inject(forwardRef(() => ReactionHandlersService))
		private readonly reactionHandlers: ReactionHandlersService,
		@Inject(forwardRef(() => ChosenInlineResultHandlersService))
		private readonly chosenInlineResultHandlers: ChosenInlineResultHandlersService,
		@Inject(forwardRef(() => SceneCommandHandlersService))
		private readonly sceneCommandHandlers: SceneCommandHandlersService,
	) {}

	async onModuleInit() {
		try {
			this.logger.log("MultiTenantBotService onModuleInit started.");

			// Initialize all active tenant bots
			await this.initializeAllTenantBots();

			this.logger.log("MultiTenantBotService initialized successfully.");
		} catch (error) {
			this.logger.error("Failed to initialize MultiTenantBotService:", error);
			throw error;
		}
	}

	/**
	 * Initialize all active tenant bots
	 */
	private async initializeAllTenantBots() {
		try {
			// Get all active tenant bots
			const activeTenantBots =
				await this.tenantBotFactory.getAllActiveTenantBots();

			// Initialize each tenant bot
			for (const tenantBot of activeTenantBots) {
				await this.initializeTenantBot(tenantBot.tenantId);
			}

			this.logger.log(`Initialized ${this.activeTenantBots.size} tenant bots.`);
		} catch (error) {
			this.logger.error("Failed to initialize all tenant bots:", error);
			throw error;
		}
	}

	/**
	 * Initialize a tenant bot
	 * @param tenantId The tenant ID
	 */
	private async initializeTenantBot(tenantId: string) {
		try {
			// Get the bot instance for this tenant
			const botInstance = await this.tenantBotFactory.getBotForTenant(tenantId);

			if (!botInstance) {
				this.logger.warn(`Failed to get bot instance for tenant ${tenantId}.`);
				return;
			}

			// Initialize handler services with the bot instance
			this.commandHandlers.initialize(botInstance);
			this.callbackQueryHandlers.initialize(botInstance);
			this.inlineQueryHandlers.initialize(botInstance);
			this.reactionHandlers.initialize(botInstance);
			this.chosenInlineResultHandlers.initialize(botInstance);
			this.startCommandHandlers.initialize(botInstance);
			if (this.sceneCommandHandlers) {
				this.sceneCommandHandlers.initialize(botInstance);
			}

			// Register handlers
			this.commandHandlers.registerHandlers();
			this.callbackQueryHandlers.registerHandlers();
			this.inlineQueryHandlers.registerHandlers();
			this.reactionHandlers.registerHandlers();
			this.chosenInlineResultHandlers.registerHandlers();
			this.startCommandHandlers.registerHandlers();
			if (this.sceneCommandHandlers) {
				this.sceneCommandHandlers.registerHandlers();
			}

			// Start the bot
			await botInstance.start();

			// Store the bot instance
			this.activeTenantBots.set(tenantId, botInstance);

			this.logger.log(`Initialized bot for tenant ${tenantId}.`);
		} catch (error) {
			this.logger.error(
				`Failed to initialize bot for tenant ${tenantId}:`,
				error,
			);
			throw error;
		}
	}

	/**
	 * Get a bot instance for a specific tenant
	 * @param tenantId The tenant ID
	 * @returns The bot instance for the tenant
	 */
	async getBotForTenant(
		tenantId: string,
	): Promise<Bot<AppBotErrorDefinitions> | null> {
		// Check if we already have an instance for this tenant
		if (this.activeTenantBots.has(tenantId)) {
			return this.activeTenantBots.get(tenantId) || null;
		}

		// If not, try to initialize one
		await this.initializeTenantBot(tenantId);

		return this.activeTenantBots.get(tenantId) || null;
	}

	async onModuleDestroy() {
		// Stop all active tenant bots
		for (const [tenantId, botInstance] of this.activeTenantBots.entries()) {
			try {
				await botInstance.stop();
				this.logger.log(`Stopped bot for tenant ${tenantId}.`);
			} catch (error) {
				this.logger.error(`Failed to stop bot for tenant ${tenantId}:`, error);
			}
		}

		this.logger.log("MultiTenantBotService destroyed.");
	}
}
