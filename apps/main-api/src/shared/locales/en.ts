import type { LanguageMap } from "@gramio/i18n";
import { bold, format } from "gramio";

export const en = {
	commands: {
		start: {
			greeting: (name: string) =>
				format`Hello, ${bold(name)}! Welcome to our bot!`,
			error: "Sorry, I couldn't process your request. Please try again later.",
		},
		admin: {
			panel: (tenantName: string, roles: string) =>
				format`Admin panel for ${bold(tenantName)}. Your roles: ${roles}`,
		},
		mod: {
			panel: (tenantName: string, roles: string) =>
				format`Moderator panel for ${bold(tenantName)}. Your roles: ${roles}`,
		},
	},
	errors: {
		accessDenied:
			"Access denied. You do not have permission to use this command.",
		missingContext: "Missing required context to process this command.",
	},
	inlineQuery: {
		result_title: (query: string) => format`Results for "${bold(query)}"`,
		result_content: (query: string) =>
			format`Here are your results for "${query}"`,
		button_details: "Show details",
	},
	scenes: {
		greeting: {
			askName: "Hi! What's your name?",
			invalidName: "Please provide a valid name.",
			askAge: "How old are you?",
			invalidAge: "Please provide a valid age.",
			summary: (name: string, age: number) =>
				format`Nice to meet you, ${bold(name)}! You're ${age} years old.`,
		},
	},
	// New global keys
	greeting: (name: string) => format`Hello, ${bold(name)}!`,
	thankYou: "Thank you!",
	chooseAction: "Choose an action:",
	selectedAction: (id: string) => format`You selected action with ID: ${id}`,
	hi: "Hi!",
	doAction1: "Perform action 1",
	actionReceived: (id: string) => format`Action ${id} received!`,
} satisfies LanguageMap;

export type EnInlineQuery = typeof en.inlineQuery;
