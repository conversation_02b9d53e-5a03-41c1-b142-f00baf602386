import type { ShouldFollowLanguage } from "@gramio/i18n";
import { bold, format } from "gramio";
import type { EnInlineQuery, en } from "./en.js";

export const mk = {
	greeting: (name: string) => format`Здраво, ${bold(name)}!`,
	thankYou: "Ти благодарам!",
	chooseAction: "Изберете акција:",
	selectedAction: (id: string) => format`Избравте дејство со ID: ${id}`,
	hi: "Здраво!",
	doAction1: "Изврши акција 1",

	actionReceived: (id: string) => format`Акцијата ${id} е примена!`,

	inlineQuery: {
		result_title: (searched: string) =>
			format`Резултат од пребарување за „${searched}“`,
		result_content: (searched: string) =>
			format`Еве го вашиот резултат за „${searched}“`,
		button_details: "Покажи детали",
	} as EnInlineQuery,

	scenes: {
		greeting: {
			askName: "Здраво! Како се викаш?",
			invalidName: "Ве молиме, напишете го вашето име.",
			askAge: "Колку години имаш?",
			invalidAge: "Ве молиме правилно внесете ги вашите години.",
			summary: (name: string, age: number) =>
				format`Мило ми е што се запознавме! Сега знам дека се викаш ${bold(name)} и имаш ${age} години.`,
		},
	},
} satisfies ShouldFollowLanguage<typeof en>;
