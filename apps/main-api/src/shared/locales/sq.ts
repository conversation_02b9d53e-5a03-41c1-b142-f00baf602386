import type { ShouldFollowLanguage } from "@gramio/i18n";
import { bold, format } from "gramio";
import type { EnInlineQuery, en } from "./en.js";

export const sq = {
	greeting: (name: string) => format`Përshëndetje, ${bold(name)}!`,
	thankYou: "Faleminderit!",
	chooseAction: "Zgjidh një veprim:",
	selectedAction: (id: string) => format`Zgjodhe veprimin me ID: ${id}`,
	hi: "Përshëndetje!",
	doAction1: "Kryej veprimin 1",

	actionReceived: (id: string) => format`Veprimi ${id} u pranua!`,

	inlineQuery: {
		result_title: (searched: string) =>
			format`Rezultati i kërkimit për “${searched}”`,
		result_content: (searched: string) =>
			format`Këtu është rezultati juaj për “${searched}”.`,
		button_details: "<PERSON>h<PERSON><PERSON> detajet",
	} as EnInlineQuery,

	scenes: {
		greeting: {
			askName: "Përshëndetje! Si quhesh?",
			invalidName: "Ju lutem shkruani emrin tuaj.",
			askAge: "Sa vjeç je?",
			invalidAge: "Ju lutem shkruani saktë moshën tuaj.",
			summary: (name: string, age: number) =>
				format`Kënaqësi që u njohëm! Tani di që emri yt është ${bold(name)} dhe ke ${age} vjeç.`,
		},
	},
} satisfies ShouldFollowLanguage<typeof en>;
