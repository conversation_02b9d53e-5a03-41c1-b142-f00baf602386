import { Injectable, Logger } from "@nestjs/common";
import type { AppMessageContext } from "../types/bot-context.js";

@Injectable()
export class RoleBasedAccessMiddleware {
	private readonly logger = new Logger(RoleBasedAccessMiddleware.name);

	hasRoles(requiredRoles: string[]) {
		// Ensure the context type here matches what GramIO expects for command middleware, including derived properties from AppMessageContext
		return async (ctx: AppMessageContext, next: () => Promise<void>): Promise<void> => {
			// Check if requiredRoles are empty or if user/chat ID is missing. userRoles is now part of AppMessageContext.
			if (!requiredRoles || requiredRoles.length === 0 || (!ctx.from?.id && !ctx.chat?.id)) {
				return next(); // Allow if no roles are required or user/chat ID is missing
			}

			const userRoles = ctx.userRoles || []; // Use nullish coalescing for safety

			const hasRequiredRole = requiredRoles.some((role) =>
				userRoles.includes(role),
			);

			if (hasRequiredRole) {
				return next();
			}

			this.logger.warn(
				`Access denied for user ${ctx.from?.id || ctx.chat?.id} - Required roles: ${requiredRoles.join(", ")}, User roles: ${userRoles.join(", ")}`,
			);

			// Check if chat ID and the derived tenantBotApiClient are available before attempting to send a message
			if (ctx.chat?.id && ctx.tenantBotApiClient) { // Access tenantBotApiClient from the derived context
				try {
					await ctx.tenantBotApiClient.sendMessage({
						chat_id: ctx.chat.id,
						text: "Access denied. You do not have permission to use this command.",
					});
				} catch (e) {
					this.logger.error("Failed to send access denied message", e);
				}
			}
			return;
		};
	}
	// ... isAdmin, isModerator, isSupport
}
