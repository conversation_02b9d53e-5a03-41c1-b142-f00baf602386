import { Injectable, Logger } from "@nestjs/common";
// biome-ignore lint/style/useImportType: <explanation>
import { ConfigService as NestConfigService } from "@nestjs/config";

@Injectable()
export class AppConfigService {
	private readonly logger = new Logger(AppConfigService.name);
	appPort = 3004; // Initialize with a default value

	constructor(private readonly nestConfig: NestConfigService) {
		// Debug log for nestConfig DI correctness
		console.debug(
			"AppConfigService constructed. nestConfig:",
			nestConfig,
			"Type:",
			typeof nestConfig,
		);
	}

	get nodeEnv() {
		return this.nestConfig.get<string>("NODE_ENV", "development");
	}
	get botToken() {
		return this.nestConfig.get<string>("BOT_TOKEN", "");
	}
	get primaryBotToken() {
		return this.nestConfig.get<string>("PRIMARY_BOT_TOKEN", "");
	}
	get tokenEncryptionKey() {
		return this.nestConfig.get<string>("TOKEN_ENCRYPTION_KEY", "");
	}
	get databaseUrl() {
		const url = this.nestConfig.get<string>("DATABASE_URL", "");
		if (!url) {
			this.logger.warn("DATABASE_URL is not set in environment variables");
		}
		return url;
	}

	get isDatabaseConfigured() {
		const url = this.databaseUrl;
		return !!url && url.includes("://") && url.includes("@");
	}
	get redisHost() {
		return this.nestConfig.get<string>("REDIS_HOST", "localhost");
	}
	get lockStore() {
		return this.nestConfig.get<string>("LOCK_STORE", "memory");
	}
}
