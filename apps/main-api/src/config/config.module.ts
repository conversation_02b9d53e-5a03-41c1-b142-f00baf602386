import { Global, Module } from "@nestjs/common";
import { ConfigModule as NestConfigModule } from "@nestjs/config";
import { AppConfigService } from "./app-config.service.js";

export const APP_CONFIG_SERVICE_TOKEN = "HighlyUniqueAppConfigToken_XYZ123";

@Global() // Make this module global
@Module({
	imports: [NestConfigModule.forRoot({ isGlobal: true })],
	providers: [
		AppConfigService, // Provide the class itself for direct class-based injection
		{
			provide: APP_CONFIG_SERVICE_TOKEN, // Provide under the unique string token
			useExisting: AppConfigService, // Reuse the instance provided by AppConfigService class token
		},
	],
	exports: [AppConfigService, APP_CONFIG_SERVICE_TOKEN], // Export both with the new token
})
export class ConfigModule {}
