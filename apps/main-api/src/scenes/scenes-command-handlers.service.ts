// src/scenes/scenes-command-handlers.service.ts
import { Injectable, Logger } from "@nestjs/common";
// Import the correct context type
// biome-ignore lint/style/useImportType: <explanation>
import {
	AppBot,
	AppCommandContext,
} from "../types/bot-context.js";
// You might not need SceneRegistrationService here if BotService gets scenes directly for initialization
// import { SceneRegistrationService } from './scene-registration.service';

import { greetingScene } from "./greeting.scene.js";

@Injectable()
export class SceneCommandHandlersService {
	private readonly logger = new Logger(SceneCommandHandlersService.name);
	private botInstance!: AppBot;

	public initialize(bot: AppBot): void {
		this.botInstance = bot;
	}

	registerHandlers(): void {
		this.logger.log("Registering scene command handlers");

		// Example: Command to start the 'greeting' scene
		this.botInstance.command(
			"survey",
			(ctx: any) => this.handleStartGreetingScene(ctx as AppCommandContext),
		);
		this.botInstance.command(
			"begin_greeting",
			(ctx: any) => this.handleStartGreetingScene(ctx as AppCommandContext),
		);

		// Register other commands that trigger scenes
		// const scenes = this.sceneRegistration.getScenes();
		// scenes.forEach(scene => {
		//   if (scene.id === 'someOtherScene') { // gramio/scenes Scene has an `id` property
		//     this.botInstance.command(`start_${scene.id}`, (ctx: AppCommandContext) => ctx.scene.enter(scene.id));
		//   }
		// });
	}

	private async handleStartGreetingScene(
		context: AppCommandContext,
	): Promise<void> {
		this.logger.log(
			`User ${context.from?.id} triggered greeting scene via command.`,
		);
		// AppCommandContext has `scene.enter`
		await context.scene.enter(greetingScene);
		// Optionally send a message before entering, or let the scene handle the first message
		// await context.send(context.t('scene.greeting.starting'));
	}
}
