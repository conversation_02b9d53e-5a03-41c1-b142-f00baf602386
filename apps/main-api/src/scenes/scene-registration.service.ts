// biome-ignore lint/style/useImportType: <explanation>
import { AnyScene } from "@gramio/scenes"; // Use AnyScene from your @gramio/scenes types
// src/scenes/scene-registration.service.ts
import { Injectable, Logger } from "@nestjs/common";

// Import your scene instances
import { greetingScene } from "./greeting.scene.js"; // Assuming greeting.scene.ts is in the same folder
// import { languageSelectionScene } from './language-selection.scene';
// import { surveyScene } from './survey.scene';
// ... import other scenes as you create them

@Injectable()
export class SceneRegistrationService {
	private readonly logger = new Logger(SceneRegistrationService.name);
	private readonly registeredScenes: AnyScene[] = [];

	constructor() {
		// Register scenes upon service instantiation
		this.registerScenes();
	}

	private registerScenes(): void {
		this.logger.log("Registering application scenes...");

		this.addScene(greetingScene);
		// this.addScene(languageSelectionScene);
		// this.addScene(surveyScene);
		// ... add other imported scenes

		this.logger.log(`${this.registeredScenes.length} scene(s) registered.`);
	}

	private addScene(scene: AnyScene): void {
		if (
			this.registeredScenes.find(
				(s) =>
					(s as unknown as { id: string }).id ===
					(scene as unknown as { id: string }).id,
			)
		) {
			// gramio/scenes Scene class has an `id` property (usually same as name)
			this.logger.warn(
				`Scene with id '${(scene as unknown as { id: string }).id}' is already registered. Skipping.`,
			);
			return;
		}
		this.registeredScenes.push(scene);
		this.logger.log(
			`Scene '${(scene as unknown as { id: string }).id}' added to registration.`,
		);
	}

	/**
	 * Returns all registered scene instances.
	 * To be used by BotService when initializing the scenes plugin.
	 */
	public getScenes(): AnyScene[] {
		return this.registeredScenes;
	}
}
