import { autoAnswerCallbackQuery } from "@gramio/auto-answer-callback-query";
import { autoRetry } from "@gramio/auto-retry";
import { mediaCache } from "@gramio/media-cache";
import { mediaGroup } from "@gramio/media-group";
import { prompt } from "@gramio/prompt";
import { scenes } from "@gramio/scenes";
import { session } from "@gramio/session";
import { redisStorage } from "@gramio/storage-redis";
import { Injectable, Logger } from "@nestjs/common";
import { Bot } from "gramio";
import { i18n } from "@/shared/locales/index.js";
// biome-ignore lint/style/useImportType: <explanation>
import { AppBotErrorDefinitions } from "../types/bot-context.js";
// biome-ignore lint/style/useImportType: <explanation>
import { RedisService } from "./redis.service.js";

/**
 * Service responsible for creating and managing tenant-specific bot instances
 */
@Injectable()
export class TenantBotFactoryService {
	private readonly logger = new Logger(TenantBotFactoryService.name);
	private tenantBotInstances: Map<string, Bot<any>> = new Map();
	private tenantBotApiClients: Map<string, any> = new Map();

	constructor(private readonly redisService: RedisService) {}

	/**
	 * Get or create a bot instance for a specific tenant
	 * @param tenantId The tenant ID
	 * @returns The bot instance for the tenant
	 */
	async getBotForTenant(tenantId: string): Promise<Bot<any> | null> {
		// Check if we already have an instance for this tenant
		if (this.tenantBotInstances.has(tenantId)) {
			const instance = this.tenantBotInstances.get(tenantId);
			if (instance) {
				return instance;
			}
		}

		// If not, try to create one
		try {
			// For now, use a mock token - in a real implementation, you would fetch this from the database
			// This is just for demonstration purposes
			const mockToken =
				process.env.BOT_TOKEN || "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11";

			this.logger.log(
				`Creating bot instance for tenant ${tenantId} with token ${mockToken.substring(0, 5)}...`,
			);

			// Create a new bot instance for this tenant
			const storage = redisStorage(this.redisService.client);

			// Create the bot instance with proper error definitions
			const botInstance = new Bot<AppBotErrorDefinitions>(mockToken)
				.extend(autoAnswerCallbackQuery())
				.extend(mediaGroup())
				.extend(autoRetry())
				.extend(mediaCache())
				.extend(
					session({
						initial: () => ({}),
						storage,
						getSessionKey: (ctx) => {
							// Safe way to get user ID or chat ID
							let id = "unknown";
							if ("from" in ctx && ctx.from && "id" in ctx.from) {
								id = String(ctx.from.id);
							} else if ("chat" in ctx && ctx.chat && "id" in ctx.chat) {
								id = String(ctx.chat.id);
							}
							return `${tenantId}:${id}`;
						},
					}),
				)
				.extend(scenes([], { storage }))
				.extend(prompt());

			// Add tenant context through derive with proper typing
			const botWithContext = botInstance.derive(
				["message", "callback_query", "inline_query", "chosen_inline_result"],
				(ctx) => {
					// Safe way to get language code
					let lang: string | undefined;
					if ("from" in ctx && ctx.from && "languageCode" in ctx.from) {
						lang = ctx.from.languageCode as string;
					}

					// Create a strictly typed i18n translator
					const translator = i18n.buildT(lang);

					return {
						t: translator,
						tenantId,
						tenantBotApiClient: botInstance,
					};
				},
			);

			// Store the instance and its API client for future use
			this.tenantBotInstances.set(tenantId, botWithContext);
			this.tenantBotApiClients.set(tenantId, botWithContext.api);

			return botWithContext;
		} catch (error) {
			this.logger.error(`Failed to get bot for tenant ${tenantId}:`, error);
			return null;
		}
	}

	/**
	 * Get the API client for a specific tenant
	 * @param tenantId The tenant ID
	 * @returns The API client for the tenant
	 */
	async getApiClientForTenant(tenantId: string) {
		if (this.tenantBotApiClients.has(tenantId)) {
			return this.tenantBotApiClients.get(tenantId);
		}

		const bot = await this.getBotForTenant(tenantId);
		return bot ? bot.api : null;
	}

	/**
	 * Get all active tenant bots
	 * @returns Array of tenant bot configurations
	 */
	async getAllActiveTenantBots() {
		try {
			// For now, return mock data - in a real implementation, you would fetch this from the database
			return [
				{ tenantId: "1", name: "Tenant 1", isActive: true },
				{ tenantId: "2", name: "Tenant 2", isActive: true },
				{ tenantId: "3", name: "Tenant 3", isActive: true },
			];
		} catch (error) {
			this.logger.error("Failed to get active tenant bots:", error);
			return [];
		}
	}

	/**
	 * Initialize all active tenant bots
	 */
	async initializeAllActiveTenantBots() {
		const activeTenantBots = await this.getAllActiveTenantBots();

		for (const tenantBot of activeTenantBots) {
			await this.getBotForTenant(tenantBot.tenantId);
			this.logger.log(`Initialized bot for tenant ${tenantBot.tenantId}`);
		}

		this.logger.log(
			`Initialized ${this.tenantBotInstances.size} tenant bot instances`,
		);
	}
}
