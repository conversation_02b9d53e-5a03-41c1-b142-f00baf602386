import { Global, Module } from "@nestjs/common";
import { ConfigModule } from "../config/config.module.js";
import { RedisService } from "./redis.service.js";

export const REDIS_SERVICE_TOKEN = "HighlyUniqueRedisToken_ABC789";

@Global() // Make this module global
@Module({
	imports: [ConfigModule],
	providers: [
		RedisService, // Provide the class itself
		{
			provide: REDIS_SERVICE_TOKEN, // Provide under the unique string token
			useExisting: RedisService, // Reuse the instance
		},
	],
	exports: [RedisService, REDIS_SERVICE_TOKEN], // Export both with the new token
})
export class RedisModule {}
