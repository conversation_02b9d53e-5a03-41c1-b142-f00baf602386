// Import from the package entry point
import { bots, roles, tenants, userBotRoles, type DbType } from "@monorepo/db";
import {
	Inject,
	Injectable,
	Logger,
	type OnModuleDestroy,
	type OnModuleInit,
} from "@nestjs/common";
import { and, eq } from "drizzle-orm";
import type { BotConfigFromDB, TenantFromDB } from "../types/database.js";

@Injectable()
export class DatabaseService implements OnModuleInit, OnModuleDestroy {
	private readonly logger = new Logger(DatabaseService.name);

	// Inject the Drizzle DB instance provided by DatabaseModule
	// Use the DbType for type safety
	private db!: DbType;

	constructor(@Inject("DRIZZLE_INSTANCE") dbInstance: DbType) {
		// Assign the injected DB instance to the class property
		this.db = dbInstance;
	}

	// onModuleInit is no longer needed here as the DB connection is managed by the module
	async onModuleInit() {
		this.logger.log("DatabaseService initialized with injected DB instance.");
		// You might want to run a simple query to verify the connection is alive if needed
		// try {
		//      await this.db.select({ count: sql<number>`count(*)` }).from(sql`pg_tables`).limit(1);
		//      this.logger.log("Database connection verified.");
		// } catch (error) {
		//      this.logger.error("Database connection verification failed:", error);
		// }
	}

	getDb(): DbType {
		if (!this.db) {
			// This indicates a serious issue with DI setup
			throw new Error(
				"Database instance not injected or initialized in DatabaseService",
			);
		}
		return this.db;
	}

	/**
	 * Get all active bots from the database
	 * @returns Array of active bot configurations
	 */
	async getActiveBots(): Promise<BotConfigFromDB[]> {
		try {
			const dbInstance = this.getDb();
			const botsFromDB = await dbInstance.query.bots.findMany({
				where: eq(bots.isEnabled, true),
			});
			return botsFromDB as BotConfigFromDB[];
		} catch (error: any) {
			this.logger.error(
				`Failed to get active bots: ${error.message}`,
				error.stack,
			);
			return [];
		}
	}

	/**
	 * Get a tenant by ID
	 * @param tenantId The tenant ID
	 * @returns The tenant record or undefined if not found
	 */
	async getTenantById(tenantId: string): Promise<TenantFromDB | undefined> {
		try {
			const dbInstance = this.getDb();
			const tenant = await dbInstance.query.tenants.findFirst({
				where: eq(tenants.id, tenantId),
			});
			return tenant as TenantFromDB;
		} catch (error: any) {
			this.logger.error(
				`Failed to get tenant by ID ${tenantId}: ${error.message}`,
				error.stack,
			);
			return undefined;
		}
	}

	/**
	 * Get user roles for a specific Telegram user and bot
	 * @param telegramUserId The Telegram user ID
	 * @param botId The bot ID
	 * @returns Array of role names
	 */
	async getUserRoles(telegramUserId: number, botId: string): Promise<string[]> {
		try {
			const dbInstance = this.getDb();

			// Get user-bot-role associations
			const userRoles = await dbInstance.query.userBotRoles.findMany({
				where: and(
					eq(userBotRoles.telegramUserId, telegramUserId),
					eq(userBotRoles.botId, botId),
				),
				with: {
					role: true,
				},
			});

			// Extract role names
			const roleNames = userRoles.map(
				(ur: { role: { name: any } }) => ur.role.name,
			);

			// If no roles found, return default role
			if (roleNames.length === 0) {
				// Find default role for this bot's tenant
				const botRecord = await dbInstance.query.bots.findFirst({
					where: eq(bots.id, botId),
				});

				if (botRecord) {
					const defaultRoles = await dbInstance.query.roles.findMany({
						where: and(
							eq(roles.tenantId, botRecord.tenantId),
							eq(roles.isDefault, true),
						),
					});

					// Add default role names
					roleNames.push(...defaultRoles.map((r: { name: any }) => r.name));
				}

				// If still no roles, add 'user' as fallback
				if (roleNames.length === 0) {
					roleNames.push("user");
				}
			}

			return roleNames;
		} catch (error: any) {
			this.logger.error(
				`Failed to get user roles for user ${telegramUserId} and bot ${botId}: ${error.message}`,
				error.stack,
			);
			// Return default role on error
			return ["user"];
		}
	}

	// onModuleDestroy is no longer needed here as the DB connection is managed by the module
	async onModuleDestroy() {
		// The underlying connection will be closed by the module that provided it
		this.logger.log("DatabaseService destroying.");
	}
}
