import { Injectable } from "@nestjs/common";
import { Redis } from "ioredis";
// biome-ignore lint/style/useImportType: <explanation>
import { AppConfigService } from "../config/app-config.service.js";

@Injectable()
export class RedisService {
	public client: Redis;
	private readonly config: AppConfigService;

	constructor(config: AppConfigService) {
		this.config = config;
		this.client = new Redis({
			host: this.config.redisHost,
			maxRetriesPerRequest: null,
		});
	}
}
