// apps/main-api/webpack.config.cjs
const { swcDefaultsFactory } = require("@nestjs/cli/lib/compiler/defaults/swc-defaults");
const nodeExternals = require("webpack-node-externals");
const path = require("node:path");

const swcDefaultConfig = swcDefaultsFactory().swcOptions;

module.exports = {
	externals: [
		nodeExternals({
			// Add your workspace packages to the allowlist to ensure Webpack processes them
			// instead of treating them as externals.
			// Add other workspace packages if main-api depends on them directly.
			allowlist: [/@monorepo\/.*/],
		}),
	],
	resolve: {
		extensions: ['.ts', '.js'],
		extensionAlias: {
			'.js': ['.ts', '.js'],
		},
		alias: {
			'@': path.resolve(__dirname, 'src'),
		},
	},
	module: {
		rules: [
			{
				test: /\.ts$/,
				exclude: /node_modules/,
				use: {
					loader: "swc-loader",
					options: swcDefaultConfig,
				},
			},
		],
	},
};
