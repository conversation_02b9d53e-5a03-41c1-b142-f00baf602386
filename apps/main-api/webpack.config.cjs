// apps/main-api/webpack.config.cjs
const { swcDefaultsFactory } = require("@nestjs/cli/lib/compiler/defaults/swc-defaults");
const nodeExternals = require("webpack-node-externals");
const path = require("node:path");

const swcDefaultConfig = swcDefaultsFactory().swcOptions;

module.exports = {
	target: 'node',
	mode: 'development',
	externals: [
		nodeExternals({
			// Exclude workspace packages from bundling to avoid module format conflicts
			// They will be loaded as external dependencies at runtime
			allowlist: [], // Bundle nothing from node_modules
			additionalModuleDirs: ['../../node_modules'], // Check root node_modules too
		}),
		// Explicitly externalize workspace packages
		'@monorepo/db',
	],
	resolve: {
		extensions: ['.ts', '.js'],
		extensionAlias: {
			'.js': ['.ts', '.js'],
		},
		alias: {
			'@': path.resolve(__dirname, 'src'),
		},
	},
	output: {
		filename: 'main.js',
		library: {
			type: 'commonjs2',
		},
	},
	module: {
		rules: [
			{
				test: /\.ts$/,
				exclude: /node_modules/,
				use: {
					loader: "swc-loader",
					options: swcDefaultConfig,
				},
			},
		],
	},
};
