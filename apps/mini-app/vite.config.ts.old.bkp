import { defineConfig } from "vite";
import mkcert from 'vite-plugin-mkcert';
import solidPlugin from "vite-plugin-solid";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
	base: "/solidjs-template",
	plugins: [
		// Uncomment the following line to enable solid-devtools.
		// For more info see https://github.com/thetarnav/solid-devtools/tree/main/packages/extension#readme
		// devtools(),
		solidPlugin(),
		// Allows using the compilerOptions.paths property in tsconfig.json.
		// https://www.npmjs.com/package/vite-tsconfig-paths
		tsconfigPaths(),
		// Creates a custom SSL certificate valid for the local machine.
		// Using this plugin requires admin rights on the first dev-mode launch.
		// https://www.npmjs.com/package/vite-plugin-mkcert
		process.env.HTTPS && mkcert(),
	],
	build: {
		target: "esnext",
	},
	publicDir: "./public",
	server: {
		// Exposes your dev server and makes it accessible for the devices in the same network.
		host: true,
	},
});
