import { retrieveLaunchParams } from "@telegram-apps/sdk-solid";
/* @refresh reload */
import { render } from "solid-js/web";

import { Root } from "@/components/Root";
import { init } from "@/init";

import "./index.css";

// Mock the environment in case, we are outside Telegram.
import "./mockEnv.js";

// Configure all application dependencies.
init(retrieveLaunchParams().startParam === "debug" || import.meta.env.DEV);

const root = document.getElementById("root");

if (import.meta.env.DEV && !(root instanceof HTMLElement)) {
	throw new Error(
		"Root element not found. Did you forget to add it to your index.html? Or maybe the id attribute got misspelled?",
	);
}

if (root) {
	render(() => <Root />, root);
} else {
	console.error("Failed to find root element");
}
