import { retrieveLaunchParams } from "@telegram-apps/sdk-solid";
import { type Component, ErrorBoundary, Match, Switch, type JSXElement } from "solid-js";

import { App } from "@/components/App.js";
import { publicUrl } from "@/helpers/publicUrl.js";
import { TonConnectUIProvider } from "@/tonconnect/TonConnectUIProvider.js";

const Inner: Component = () => {
	const debug = retrieveLaunchParams().startParam === "debug";
	if (debug) {
		import("eruda").then((lib) => lib.default.init());
	}

	return (
		<TonConnectUIProvider manifestUrl={publicUrl("tonconnect-manifest.json")}>
			<App />
		</TonConnectUIProvider>
	);
};

export const Root: Component = () => {
	return (
		<ErrorBoundary
			fallback={(err: unknown): JSXElement => {
				console.error("ErrorBoundary handled error:", err);

				return (
					<div>
						<p>ErrorBoundary handled error:</p>
						<blockquote>
							<code>
								<Switch fallback={JSON.stringify(err)}>
									<Match when={typeof err === "string" ? err : false}>
										{(v: string): JSXElement => <>{v}</>}
									</Match>
									<Match when={err instanceof Error ? err.message : false}>
										{(v: string): JSXElement => <>{v}</>}
									</Match>
								</Switch>
							</code>
						</blockquote>
					</div>
				);
			}}
		>
			<Inner />
		</ErrorBoundary>
	);
};
