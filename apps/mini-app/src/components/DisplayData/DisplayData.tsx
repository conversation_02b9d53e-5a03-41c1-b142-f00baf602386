import { isRGB } from "@telegram-apps/sdk-solid";
import { type Component, For, type JSXElement } from "solid-js";

import { RGB } from "@/components/RGB/RGB.js";

import "./DisplayData.css";

export interface DisplayDataRow {
	title: string;
	value: string | JSXElement;
}

export interface DisplayDataProps {
	rows: DisplayDataRow[];
}

export const DisplayData: Component<DisplayDataProps> = (props) => {
	return (
		<div>
			<For each={props.rows}>
				{(row) => (
					<div class="display-data__line">
						<span class="display-data__line-title">{row.title}</span>
						<span class="display-data__line-value">
							{typeof row.value === "string" && isRGB(row.value) ? (
								<RGB color={row.value} />
							) : (
								row.value
							)}
						</span>
					</div>
				)}
			</For>
		</div>
	);
};
