import { retrieveLaunchParams } from "@telegram-apps/sdk-solid";
import type { Component } from "solid-js";

import { DisplayData } from "@/components/DisplayData/DisplayData.js";
import { Link } from "@/components/Link/Link.js";
import { Page } from "@/components/Page/Page.js";

export const LaunchParamsPage: Component = () => {
	const lp = retrieveLaunchParams();
	return (
		<Page
			title="Launch Params"
			disclaimer={
				<>
					This page displays application{" "}
					<Link href="https://docs.telegram-mini-apps.com/platform/launch-parameters">
						launch parameters
					</Link>
					.
				</>
			}
		>
			<DisplayData
				rows={[
					{ title: "tgWebAppPlatform", value: lp.platform },
					{ title: "tgWebAppShowSettings", value: lp.showSettings },
					{ title: "tgWebAppVersion", value: lp.version },
					{ title: "tgWebAppBotInline", value: lp.botInline },
					{
						title: "tgWebAppStartParam",
						value:
							typeof lp.startParam === "string" ? lp.startParam : undefined,
					},

					{
						title: "tgWebAppData",
						value: lp.data ? <Link href="/init-data">View</Link> : undefined,
					},
					{
						title: "tgWebAppThemeParams",
						value: <Link href="/theme-params">View</Link>,
					},
				]}
			/>
		</Page>
	);
};
