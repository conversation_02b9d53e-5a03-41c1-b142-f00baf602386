{"name": "@monorepo/mini-app", "private": true, "version": "0.0.2", "type": "module", "scripts": {"deploy": "gh-pages -d dist", "dev": "vite", "dev:https": "vite", "build": "bunx tsc --noEmit && vite build", "lint": "bunx eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "bunx eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix", "typecheck": "bunx tsc --noEmit", "preview": "vite preview", "predeploy": "bun run build", "clean": "rm -rf dist .turbo tsconfig.tsbuildinfo"}, "dependencies": {"@monorepo/db": "workspace:*", "@solidjs/router": "^0.15.3", "@telegram-apps/sdk-solid": "3.0.22", "@tonconnect/ui": "^2.1.0", "eruda": "^3.4.1", "solid-js": "^1.9.7"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "vite": "^6.3.5", "eslint-plugin-solid": "^0.14.5", "vite-plugin-solid": "^2.10.2", "vite-plugin-mkcert": "^1.17.6", "vite-tsconfig-paths": "^5.1.4", "typescript": "^5.8.3"}}