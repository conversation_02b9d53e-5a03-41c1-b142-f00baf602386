#!/bin/bash

# Docker Build and Run Script for Taxi29 Production Environment
# This script helps build and run the production Docker containers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Please copy .env.example to .env and configure it."
    echo "cp .env.example .env"
    exit 1
fi

# Function to build all services
build_all() {
    print_status "Building all Docker services..."

    # Build the database package first
    print_status "Building @monorepo/db package..."
    cd packages/db && bun run build && cd ../..

    # Build Docker images
    print_status "Building Docker images..."
    docker compose build --no-cache

    print_success "All services built successfully!"
}

# Function to start all services
start_all() {
    print_status "Starting all services..."
    docker compose up -d

    print_success "All services started!"
    print_status "Services are running on:"
    echo "  - Dashboard: http://localhost:3000"
    echo "  - Main API: http://localhost:3004"
    echo "  - Mini App: http://localhost:3001"
    echo "  - PostgreSQL: localhost:5432"
    echo "  - Redis: localhost:6379"
    echo "  - RabbitMQ Management: http://localhost:15672"
}

# Function to stop all services
stop_all() {
    print_status "Stopping all services..."
    docker compose down
    print_success "All services stopped!"
}

# Function to view logs
view_logs() {
    if [ -z "$1" ]; then
        print_status "Showing logs for all services..."
        docker compose logs -f
    else
        print_status "Showing logs for $1..."
        docker compose logs -f "$1"
    fi
}

# Function to restart a specific service
restart_service() {
    if [ -z "$1" ]; then
        print_error "Please specify a service name (main-api, dashboard, mini-app, postgres, redis, rabbitmq)"
        exit 1
    fi

    print_status "Restarting $1..."
    docker compose restart "$1"
    print_success "$1 restarted!"
}

# Function to show help
show_help() {
    echo "Docker Build and Run Script for Taxi29"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build                 Build all Docker services"
    echo "  start                 Start all services"
    echo "  stop                  Stop all services"
    echo "  restart [service]     Restart a specific service"
    echo "  logs [service]        View logs (all services if no service specified)"
    echo "  status                Show status of all services"
    echo "  clean                 Stop and remove all containers, networks, and volumes"
    echo "  help                  Show this help message"
    echo ""
    echo "Services: main-api, dashboard, mini-app, postgres, redis, rabbitmq"
    echo ""
    echo "Examples:"
    echo "  $0 build             # Build all services"
    echo "  $0 start             # Start all services"
    echo "  $0 logs main-api     # View main-api logs"
    echo "  $0 restart dashboard # Restart dashboard service"
}

# Function to show status
show_status() {
    print_status "Service status:"
    docker compose ps
}

# Function to clean everything
clean_all() {
    print_warning "This will stop and remove all containers, networks, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up..."
        docker compose down -v --remove-orphans
        docker system prune -f
        print_success "Cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Main script logic
case "$1" in
    "build")
        build_all
        ;;
    "start")
        start_all
        ;;
    "stop")
        stop_all
        ;;
    "restart")
        restart_service "$2"
        ;;
    "logs")
        view_logs "$2"
        ;;
    "status")
        show_status
        ;;
    "clean")
        clean_all
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    "")
        print_error "No command specified. Use 'help' to see available commands."
        show_help
        exit 1
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
