{"$schema": "https://turborepo.com/schema.json", "globalDependencies": ["tsconfig.json", "packages/typescript-config/**", "bun.lock"], "tasks": {"@monorepo/db#build": {"dependsOn": [], "outputs": ["dist/**", "tsconfig.tsbuildinfo"]}, "main-api#build": {"dependsOn": ["@monorepo/db#build"], "outputs": ["apps/main-api/dist/**"]}, "main-api#dev": {"dependsOn": ["@monorepo/db#build"], "cache": false, "persistent": true}, "main-api#start": {"dependsOn": ["@monorepo/db#build", "main-api#build"], "cache": false, "persistent": true}, "build": {"dependsOn": ["^build"], "outputs": ["dist/**", "apps/bot/dist/**", "apps/mini-app/dist/**", ".next/**", "!.next/cache/**", "apps/dashboard/.next/**", "apps/dashboard/out/**", "apps/dashboard/public/registry/**"]}, "typecheck": {"dependsOn": ["^typecheck"], "outputs": []}, "lint": {"outputs": []}, "format:check": {}, "format:fix": {"cache": false}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}, "format": {"cache": false}, "generate": {"cache": false}, "migrate": {"cache": false}, "push": {"cache": false}, "studio": {"cache": false, "persistent": true}, "dashboard#dev": {"cache": false, "persistent": true}, "dashboard#build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "public/registry/**"]}, "dashboard#start": {"cache": false, "persistent": true}, "dashboard#lint": {"outputs": []}, "dashboard#typecheck": {"dependsOn": ["^typecheck"], "outputs": []}, "dashboard#registry:build": {"outputs": ["public/registry/**"]}}}